server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name  _;
    client_max_body_size 10M;

    root /opt/repo/frontend/saks-prod/frontend/build;
    index index.html index.htm;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    location / {
        add_header Access-Control-Allow-Origin *;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Access-Control-Allow-Credentials true;
        try_files $uri /index.html;
    }

    gzip on;
    gzip_http_version 1.1;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types text/plain text/html text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript text/x-js image/gif image/jpeg application/atom+xml application/rss+xml text/x-component image/png image/tiff image/vnd.wap.wbmp image/x-icon image/x-jng image/x-ms-bmp image/svg+xml image/webp application/font-woff application/msword application/pdf;
    gzip_buffers 16 8k;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";

  }