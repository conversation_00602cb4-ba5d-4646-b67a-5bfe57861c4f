const HtmlWebPackPlugin = require("html-webpack-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const path = require("path");
const ModuleFederationPlugin = require("webpack/lib/container/ModuleFederationPlugin");
const webpack = require("webpack");
const deps = require("../package.json").dependencies;
const Dotenv = require("dotenv-webpack");

module.exports = () => {
    // * Get environment variables or set defaults. Do not change
    // * Keep in mind the casing of the below variables. This has to match the dns settings and cloud runs settings
    const environment = process.env.ENVIRONMENT;
    const client = process.env.CLIENT;
    // * End of dynamic keys

    return {
        output: {
            path: path.resolve(__dirname, "../build"),
            filename: "js/[name].[contenthash].bundle.js",
            assetModuleFilename: "assets/[hash][ext][query]",
            clean: true,
            publicPath: `https://pricesmart-promo-${client}.${environment}.impactsmartsuite.com/`,
        },
        resolve: {
            extensions: [".tsx", ".ts", ".jsx", ".js", ".json"],
        },
        resolve: {
            extensions: [".js", ".jsx"],
            alias: {
                process: "process/browser",
                config: path.resolve(__dirname, "../src/config/"),
                actions: path.resolve(__dirname, "../src/core/actions/"),
                assets: path.resolve(__dirname, "../src/assets"),
                modules: path.resolve(__dirname, "../src/modules"),
                Styles: path.resolve(__dirname, "../src/core/Styles"),
                store: path.resolve(__dirname, "../src/store"),
                posthog: path.resolve(__dirname, "../src/core/posthog"),
                core: path.resolve(__dirname, "../src/core"),
                auth: path.resolve(__dirname, "../src/auth"),
                reducers: path.resolve(__dirname, "../src/core/reducers"),
            },
        },
        module: {
            rules: [
                {
                    test: /\.m?js/,
                    type: "javascript/auto",
                    resolve: {
                        fullySpecified: false,
                    },
                },
                {
                    test: /\.(css|s[ac]ss)$/i,
                    use: [
                        "style-loader",
                        "css-loader",
                        "postcss-loader",
                        "sass-loader",
                    ],
                },
                {
                    test: /\.(ts|tsx|js|jsx)$/,
                    exclude: /node_modules/,
                    use: {
                        loader: "babel-loader",
                    },
                },
                {
                    test: /\.svg$/i,
                    oneOf: [
                        {
                            resourceQuery: /url/, // *.svg?url
                            type: "asset/resource",
                        },
                        {
                            issuer: /\.[jt]sx?$/,
                            use: ["@svgr/webpack"],
                        },
                    ],
                },
                // Images: Copy image files to build folder
                {
                    test: /\.(?:ico|gif|png|jpg|jpeg)$/i,
                    type: "asset/resource",
                },
                // Fonts: Inline files
                { test: /\.(woff(2)?|eot|ttf|otf|)$/, type: "asset/inline" },
                { test: /\.json$/, type: "json" },
                {
                    test: /\.xlsm$/,
                    use: [
                        {
                            loader: "file-loader",
                        },
                    ],
                },
            ],
        },
        plugins: [
            new ModuleFederationPlugin({
                name: "pricesmartPromo",
                filename: "remoteEntry.js",
                exposes: {
                    "./bootstrap": "./src/bootstrap.js",
                },
                remotes: {},
                shared: {
                    react: {
                        singleton: true,
                        requiredVersion: deps.react,
                    },
                    "react-dom": {
                        singleton: true,
                        requiredVersion: deps["react-dom"],
                    },
                    "react-redux": {
                        singleton: true,
                        requiredVersion: deps["react-redux"],
                    },
                },
            }),
            new HtmlWebPackPlugin({
                template: "./public/index.html",
            }),
            new CopyPlugin({
                patterns: [{ from: "./public", to: "assets" }],
            }),
            new Dotenv({
                path: `./.env.${environment}`,
            }),
            new webpack.DefinePlugin({
                REACT_APP_VERSION: JSON.stringify(
                    require("../package.json").version
                ),
            }),
        ],
    };
};
