<svg xmlns="http://www.w3.org/2000/svg" width="238.633" height="199.401" viewBox="0 0 238.633 199.401">
  <g id="Group_20556" data-name="Group 20556" transform="translate(-4.74 0.419)">
    <path id="Path_47006" data-name="Path 47006" d="M115.332,130.13a25.989,25.989,0,0,1-8.225,2.357v5.564a20.989,20.989,0,0,0,10.171-.354Z" transform="translate(63.857 52.185)" fill="#fecfc4"/>
    <path id="Path_47007" data-name="Path 47007" d="M79.447,130.537l-1.782,6.8a20.981,20.981,0,0,0,11.569,0l1.453-4.687A21.722,21.722,0,0,1,79.447,130.537Z" transform="translate(45.215 52.545)" fill="#fecfc4"/>
    <path id="Path_47008" data-name="Path 47008" d="M137.777,105.5H119.319a1.645,1.645,0,0,1-1.644-1.646v-6.25a1.644,1.644,0,0,1,1.644-1.645h18.458a1.647,1.647,0,0,1,1.644,1.645v6.25a1.648,1.648,0,0,1-1.644,1.646Z" transform="translate(63.349 32.085)" fill="#fecfc4"/>
    <path id="Path_47009" data-name="Path 47009" d="M67.129,51.688V40.144A1.65,1.65,0,0,0,65.484,38.5H25.511a1.648,1.648,0,0,0-1.644,1.645V51.688a1.648,1.648,0,0,0,1.644,1.645h40a1.609,1.609,0,0,0,1.618-1.645Z" transform="translate(16.596 -3.485)" fill="#efb668"/>
    <path id="Path_47010" data-name="Path 47010" d="M99.063,38.995A64.126,64.126,0,0,0,54.706,75.071l58.311,26.408Z" transform="translate(28.177 -4.926)" fill="rgba(4,122,71,0.65)"/>
    <path id="Path_47011" data-name="Path 47011" d="M40.21,90.785H35.246a1.748,1.748,0,0,0-1.753,1.755v26.621h8.472V92.54a1.751,1.751,0,0,0-1.755-1.755Z" transform="translate(17.809 27.031)" fill="#636ce7"/>
    <path id="Path_47012" data-name="Path 47012" d="M47.014,94.071H42.051a1.76,1.76,0,0,0-1.241.512,1.742,1.742,0,0,0-.512,1.243V116.25h8.471V95.826a1.765,1.765,0,0,0-1.755-1.755Z" transform="translate(22.081 29.653)" fill="#636ce7"/>
    <path id="Path_47013" data-name="Path 47013" d="M130.987,142.848c-2.989-2.8-8.719-7.239-13.71-8.69a21.7,21.7,0,0,1-10.171.354v2.77h8.225v5.483Z" transform="translate(62.408 55.752)" fill="#636ce7"/>
    <path id="Path_47014" data-name="Path 47014" d="M96.53,58.491a1.89,1.89,0,0,1-.393,1.269,1.877,1.877,0,0,1,1.479,1.1,2.232,2.232,0,0,1,4.091.652,2.147,2.147,0,0,1,3.752,1.837,2.137,2.137,0,0,1,2.309,3.453A2.15,2.15,0,0,1,108.18,71a2.123,2.123,0,0,1,.739,2.961l-.165-.713c-1.974-8.17-5.922-3.372-6.416-.274-.11.576-.466.6-1.178-.219C99.68,71,97.65,73.792,100.666,76.1a5.713,5.713,0,0,1,.3,2.521l-.081,1.155a2,2,0,0,1-1.206-1.155,2.142,2.142,0,0,1-4.113-.822,2.142,2.142,0,0,1-3.126-2.714,2.165,2.165,0,0,1-2.413-2.165,2.428,2.428,0,0,1,.987-1.837,2.148,2.148,0,0,1-.68-3.787,1.877,1.877,0,0,1-.56-1.128,1.876,1.876,0,0,1-2.659-2.6,1.892,1.892,0,0,1-.055-3.752,1.876,1.876,0,0,1,2.577-2.685,1.954,1.954,0,0,1,3.866-.028,1.885,1.885,0,0,1,3.025,1.385Z" transform="translate(49.116 6.353)" fill="#636ce7"/>
    <path id="Path_47015" data-name="Path 47015" d="M70.263,72.053l2.906,1.01a4.821,4.821,0,0,1,1.974.823C75.418,71.285,73.005,70.71,70.263,72.053Z" transform="translate(41.117 17.364)" fill="#6153bd"/>
    <path id="Path_47016" data-name="Path 47016" d="M68.838,75.9l-1.453.055c.494,1.563,2.385,1.672,4.221.9Z" transform="translate(39.34 20.279)" fill="#6153bd"/>
    <path id="Path_47017" data-name="Path 47017" d="M95.687,134.143a20.981,20.981,0,0,1-11.569,0c-4.99,1.454-10.72,5.894-13.709,8.69,11.048,0,18.011-.083,26.675-.109A23.745,23.745,0,0,0,95.687,134.143Z" transform="translate(39.748 55.738)" fill="#636ce7"/>
    <path id="Path_47018" data-name="Path 47018" d="M106.139,70.081a1.571,1.571,0,0,0-.867.265,1.57,1.57,0,0,0-.664,1.6,1.562,1.562,0,1,0,1.531-1.868Z" transform="translate(62.944 16.534)" fill="#6153bd"/>
    <path id="Path_47019" data-name="Path 47019" d="M75.18,75.975a.947.947,0,1,1-.29-.645A.947.947,0,0,1,75.18,75.975Z" transform="translate(43.218 19.732)" fill="#6153bd"/>
    <path id="Path_47020" data-name="Path 47020" d="M89.9,78.587l-4.473,8.251c1.561-1.289,3.207-2.658,5.263-4.166C90.256,80.259,89.927,78.587,89.9,78.587Z" transform="translate(50.661 21.469)" fill="#6153bd"/>
    <path id="Path_47021" data-name="Path 47021" d="M97.484,95.744l-.823,13.6-2.029-4.169Z" transform="translate(56.672 31.55)" fill="#6153bd"/>
    <path id="Path_47022" data-name="Path 47022" d="M108.1,76.769,91.4,105.117h20.534V93.136l3.262-6.058h5.813v18.038h.575l16.723-28.348Z" transform="translate(50.407 18.746)" fill="#636ce7"/>
    <path id="Path_47023" data-name="Path 47023" d="M95.244,68.654l1.092,1.517.157-1.023A7,7,0,0,1,95.244,68.654Z" transform="translate(57.192 15.739)" fill="#6153bd"/>
    <path id="Path_47024" data-name="Path 47024" d="M117.052,78.387a3.272,3.272,0,0,0-.356-2.769c.684-.137,4.3-.9,5.049-2.082.741-1.206-4.6-.631-5.536-.577a2.518,2.518,0,0,0-1.81.986c-.767.987-2.112,2.688-3.537,4.442" transform="translate(66.126 18.053)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47025" data-name="Path 47025" d="M119.685,76.107a20.686,20.686,0,0,1,3.75-.768c3.862-.651,3.839.329.686,1.7a31.543,31.543,0,0,0-9.392,6.132" transform="translate(68.466 19.349)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47026" data-name="Path 47026" d="M116.747,75.077a44.564,44.564,0,0,1,5.428-1.616c3.752-1.172,3.841-.188.905,1.587a41.391,41.391,0,0,0-3.839,2.6" transform="translate(70.086 18.223)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47027" data-name="Path 47027" d="M116.557,74.735a61.235,61.235,0,0,1,5.374-3.51c3.524-1.752,3.752-.85,1.178,1.426a38.363,38.363,0,0,0-3.571,3.9" transform="translate(69.98 16.479)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47028" data-name="Path 47028" d="M114.692,75.88A55.147,55.147,0,0,1,118.814,71c2.823-2.742,3.353-1.913,1.535,1.01a26.592,26.592,0,0,0-1.563,3.157" transform="translate(68.973 15.85)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47029" data-name="Path 47029" d="M118.507,79.035a13.043,13.043,0,0,0-2.195-5.537" transform="translate(70.466 18.507)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47030" data-name="Path 47030" d="M91.143,112.3h16.587" transform="translate(53.305 43.224)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47031" data-name="Path 47031" d="M119.609,184.111H25.279a7.462,7.462,0,0,1-3.324-.786,8.732,8.732,0,0,1-2.817-2.248,10.648,10.648,0,0,1-1.883-3.362,12.082,12.082,0,0,1-.657-3.967V41.579a12.113,12.113,0,0,1,.659-3.967,10.675,10.675,0,0,1,1.882-3.363A8.748,8.748,0,0,1,21.956,32a7.468,7.468,0,0,1,3.323-.786H195.418a7.475,7.475,0,0,1,3.323.788,8.743,8.743,0,0,1,2.816,2.246,10.687,10.687,0,0,1,1.881,3.362,12.138,12.138,0,0,1,.66,3.966v132.17a12.1,12.1,0,0,1-.659,3.967,10.649,10.649,0,0,1-1.883,3.362,8.73,8.73,0,0,1-2.816,2.248,7.459,7.459,0,0,1-3.323.786H161.7" transform="translate(10.158 -3.938)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47032" data-name="Path 47032" d="M103.157,67.755l-.164-.713c-1.96-8-5.759-3.567-6.38-.47-.18.895-.6.655-1.214-.051-1.481-1.755-3.494.984-.495,3.345a7.758,7.758,0,0,1,.25,3.345c-.164,2-1.342,4.8-3.152,5.564,5.4,3.509,9.184.85,9.019.192a3.675,3.675,0,0,1-1.655-4.39,3.852,3.852,0,0,0,2.742-.049C103.322,73.911,104.11,72.121,103.157,67.755Z" transform="translate(54.278 10.975)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47033" data-name="Path 47033" d="M109.214,71.142c-2.056.433-4.661-.493-8.417,1.919C93.97,77.447,84.54,85.234,84.54,85.234a119.131,119.131,0,0,0-11.977-13.1" transform="translate(40.276 16.474)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47034" data-name="Path 47034" d="M96.784,71.259c2.8,1.154,6.526,1.234,8.635,4.853a22.386,22.386,0,0,1,1.6,3.618" transform="translate(57.387 16.925)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47035" data-name="Path 47035" d="M72.892,72.014c2.424-1.155,4.572-.807,4.8,1.131.356,3.03-3.681,7.319-7.184,8.081-1.443.315-2.72,0-3.121-1.241" transform="translate(38.976 16.883)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47036" data-name="Path 47036" d="M67.659,76.367c3.8,4.347,8.98,9.986,14.562,16.162a9.163,9.163,0,0,0,11.544,1.782c4.844-2.957,7.449-5.83,12.641-9.632.486,2.756,1.088,6.482,1.443,9.852a33.037,33.037,0,0,0,7.622,1.453" transform="translate(36.458 19.235)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47037" data-name="Path 47037" d="M80.46,75.595a4.817,4.817,0,0,0-1.974-.822s-7.973-2.938-10.715-4.195c-3.781-1.732-4.329-1.512-2.5.685.8.957,4.827,3.647,2.661,4.3" transform="translate(36.669 15.939)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47038" data-name="Path 47038" d="M75.661,76.886c-2.44-.577-7.266-.108-9.4.028-1.753.111-4.279.633-6.222.85-.44.049-.932-.6,1.645-1.672l3.453-1.343" transform="translate(33.841 19.453)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47039" data-name="Path 47039" d="M74,75.9a32.954,32.954,0,0,0-5.935.683c-1.587.384-4.276,1.4-6,1.837-.433.111-1.01-.433,1.369-1.919.2-.113.54-.341.967-.6" transform="translate(35.282 20.205)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47040" data-name="Path 47040" d="M85.236,131.671l-1.422,4.661" transform="translate(49.927 53.549)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47041" data-name="Path 47041" d="M77.679,137.329l1.758-6.784" transform="translate(46.011 52.552)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47042" data-name="Path 47042" d="M98.785,86c-6.169,9.348-7.563,18.9-20.836,82.3-.215,1.033,1.6,1.944,2.582,2.338a22.841,22.841,0,0,0,9.917,1.473c.876-.143,1.485-.434,1.649-.905l20.177-66.839,1.453-.8" transform="translate(43.558 13.107)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47043" data-name="Path 47043" d="M87.609,94.777a11.9,11.9,0,0,0,5.182-6.332" transform="translate(52.045 27.726)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47044" data-name="Path 47044" d="M81.27,162.792c.493.083,16.23-66.729,16.23-66.729" transform="translate(47.215 22.017)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47045" data-name="Path 47045" d="M109.081,138.99l-5.236-19.382-9.212-18.863" transform="translate(55.663 26.163)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47046" data-name="Path 47046" d="M107.108,153.114c4.084-.466,9.786-2.357,9.458-3.782L109.2,114.952q-.891-4.1-2.084-8.115" transform="translate(63.927 31.557)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47047" data-name="Path 47047" d="M104.366,106.2,99.954,94.8" transform="translate(59.903 31.187)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47048" data-name="Path 47048" d="M113.632,143.4l-6.524-27.388" transform="translate(64.218 39.682)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47049" data-name="Path 47049" d="M92.074,106.018l-1.426-1.892,16.724-28.375h30.184l1.424,1.919" transform="translate(49.803 17.977)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47050" data-name="Path 47050" d="M99.518,75.751l1.4,1.919" transform="translate(59.892 20.154)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47051" data-name="Path 47051" d="M97.085,142.724c-8.658.026-15.627.109-26.676.109,2.989-2.8,8.719-7.237,13.709-8.69a20.981,20.981,0,0,0,11.569,0A23.676,23.676,0,0,1,97.085,142.724Z" transform="translate(39.748 55.738)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47052" data-name="Path 47052" d="M113.417,137.7l-1.948-7.567" transform="translate(67.421 52.185)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47053" data-name="Path 47053" d="M9.6,138.766h213.9" transform="translate(3.964 59.832)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47054" data-name="Path 47054" d="M111.371,183.26h-9.077V88.295l3.264-6.059h5.813Z" transform="translate(60.947 9.772)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47055" data-name="Path 47055" d="M104.025,82.235V183.26" transform="translate(62.878 9.772)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47056" data-name="Path 47056" d="M123.906,141.3H97.424v-5.483h26.482Z" transform="translate(56.217 57.219)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47057" data-name="Path 47057" d="M99.134,104.337q-2.884.942-5.84,1.642" transform="translate(55.577 38.061)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47058" data-name="Path 47058" d="M130.136,76.347a64.009,64.009,0,0,1-23.028,41.622" transform="translate(62.503 17.261)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47059" data-name="Path 47059" d="M119.561,74.442c-.028.493-.083.987-.109,1.481" transform="translate(72.675 19.364)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47060" data-name="Path 47060" d="M119.594,71.973c0,.8,0,1.587-.025,2.387" transform="translate(72.759 17.762)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47061" data-name="Path 47061" d="M111.066,166.041A64.01,64.01,0,0,1,110.545,38.4c34.025-2.752,66.492,21.79,69.113,61.137" transform="translate(19.42 -10.211)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47062" data-name="Path 47062" d="M99.036,38.995l13.982,62.48L54.706,75.047" transform="translate(28.177 -4.926)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47063" data-name="Path 47063" d="M75.29,52.048,65.735,38.5H51.973" transform="translate(28.697 -3.449)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47064" data-name="Path 47064" d="M109.739,96.049l11.1,15.682h14.886" transform="translate(63.775 31.526)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47065" data-name="Path 47065" d="M127.174,42.989a4.664,4.664,0,1,1-.364-1.782A4.664,4.664,0,0,1,127.174,42.989Z" transform="translate(70.679 -3.424)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47066" data-name="Path 47066" d="M133.586,49.226a4.659,4.659,0,1,1-.933-2.771A4.664,4.664,0,0,1,133.586,49.226Z" transform="translate(69.184 0.423)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47067" data-name="Path 47067" d="M117.9,59.689l21.246-21.494" transform="translate(63.545 -3.859)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47068" data-name="Path 47068" d="M67.157,40.146A1.649,1.649,0,0,0,65.511,38.5h-40a1.649,1.649,0,0,0-1.644,1.645V51.69a1.649,1.649,0,0,0,1.644,1.645h40a1.649,1.649,0,0,0,1.645-1.645Z" transform="translate(16.596 -3.484)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47069" data-name="Path 47069" d="M67.157,52.911a1.649,1.649,0,0,0-1.645-1.645h-40a1.649,1.649,0,0,0-1.644,1.645V97.022a1.649,1.649,0,0,0,1.644,1.645h40a1.649,1.649,0,0,0,1.645-1.645Z" transform="translate(16.596 2.604)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47070" data-name="Path 47070" d="M117.685,103.844a1.645,1.645,0,0,0,1.005,1.518,1.621,1.621,0,0,0,.626.126h18.45a1.645,1.645,0,0,0,1.646-1.644V97.621a1.647,1.647,0,0,0-1.646-1.645H119.342a1.659,1.659,0,0,0-1.657,1.645Z" transform="translate(63.358 32.095)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47071" data-name="Path 47071" d="M35.175,123.3V87.852A1.758,1.758,0,0,0,33.42,86.11H28.456A1.761,1.761,0,0,0,26.7,87.88V123.3" transform="translate(19.106 23.335)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47072" data-name="Path 47072" d="M23.838,81.364V127.5H87.087" transform="translate(16.569 19.624)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47073" data-name="Path 47073" d="M41.965,119.161V92.54a1.765,1.765,0,0,0-1.755-1.755H35.246a1.748,1.748,0,0,0-1.753,1.755v26.621" transform="translate(17.809 27.032)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47074" data-name="Path 47074" d="M48.769,116.25V95.826a1.765,1.765,0,0,0-1.755-1.755H42.052A1.748,1.748,0,0,0,40.3,95.826V116.25" transform="translate(22.081 29.653)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47075" data-name="Path 47075" d="M55.56,119.843V91.769A1.765,1.765,0,0,0,53.8,90.014H48.841a1.747,1.747,0,0,0-1.753,1.755v28.074" transform="translate(26.344 26.419)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47076" data-name="Path 47076" d="M22.283,86.832l2.934-5.482,2.9,5.482" transform="translate(15.192 23.389)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47077" data-name="Path 47077" d="M54.5,104.279l5.427,2.932-5.426,2.96" transform="translate(31.144 37.594)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47078" data-name="Path 47078" d="M92,71.142l.521,5.455,4.989-3.673,4.716,3.345-1.206-4.908" transform="translate(54.393 17.049)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47079" data-name="Path 47079" d="M89.718,71.317l9.814,18.917-.11,4.3" transform="translate(52.998 15.919)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47080" data-name="Path 47080" d="M102.874,72.029,94.923,89.6" transform="translate(56.422 16.767)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47081" data-name="Path 47081" d="M93.949,72.815l1.837,3.207,1.892-3.207" transform="translate(56.166 18.235)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47082" data-name="Path 47082" d="M93.949,83.837l1.837-9.322,1.59,9.322" transform="translate(56.191 18.877)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47083" data-name="Path 47083" d="M45.911,83.208a13.187,13.187,0,1,1,0-26.373" transform="translate(17.198 7.071)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47084" data-name="Path 47084" d="M43.768,62.753H30.992" transform="translate(16.129 12.124)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47085" data-name="Path 47085" d="M43.768,64.9H30.992" transform="translate(16.129 13.475)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47086" data-name="Path 47086" d="M146.54,58.013V73.092" transform="translate(88.911 8.403)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47087" data-name="Path 47087" d="M157.62,62.019H142.541" transform="translate(85.369 11.663)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47088" data-name="Path 47088" d="M67.225,34.936l16.12-13.461L92.227,32.14l17.957-14.969" transform="translate(36.6 -16.376)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47089" data-name="Path 47089" d="M85.473,16.749l8.553.8-.8,8.526" transform="translate(50.437 -16.749)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47090" data-name="Path 47090" d="M110.857,79.382l-5.044-7.731" transform="translate(63.544 17.217)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47091" data-name="Path 47091" d="M104.578,71.623a1.515,1.515,0,0,0,.106.613,1.538,1.538,0,0,0,.336.523,1.565,1.565,0,0,0,1.733.35,1.584,1.584,0,0,0,.513-.352,1.6,1.6,0,0,0,.336-.524,1.583,1.583,0,0,0,.1-.613,1.564,1.564,0,0,0-3.126,0Z" transform="translate(62.943 16.549)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47092" data-name="Path 47092" d="M10.553,75.02a2.7,2.7,0,1,1-.2-1.04A2.713,2.713,0,0,1,10.553,75.02Z" transform="translate(0 17.773)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47093" data-name="Path 47093" d="M78.915,78.616l1.014,1.1" transform="translate(46.845 22.004)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47094" data-name="Path 47094" d="M70.162,83.278c2.823,3.208,12.748-5.511,9.924-8.719" transform="translate(40.707 18.898)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47095" data-name="Path 47095" d="M85.429,86.837l4.495-8.251s.3,1.717.743,4.13" transform="translate(50.665 21.468)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47096" data-name="Path 47096" d="M96.531,95.744l-.822,13.6" transform="translate(57.523 31.551)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47097" data-name="Path 47097" d="M121.012,105.116h.577L138.31,76.769H108.1L91.4,105.116h20.533" transform="translate(50.407 18.746)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47098" data-name="Path 47098" d="M79.16,80.075C73.273,77.922,69.05,76.55,62.19,77c-4.029.269-4.658-.678-.494-2.082,1.535-.519,9.321-1.621,11.021-1.864a66.443,66.443,0,0,1,10.555-.8" transform="translate(32.849 17.589)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47099" data-name="Path 47099" d="M96.493,69.148a7.032,7.032,0,0,1-1.25-.493l1.092,1.517" transform="translate(57.192 15.739)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47100" data-name="Path 47100" d="M96.53,58.491a1.89,1.89,0,0,1-.393,1.268,1.876,1.876,0,0,1,1.479,1.1,2.232,2.232,0,0,1,4.091.652,2.147,2.147,0,0,1,3.752,1.837,2.137,2.137,0,0,1,2.309,3.453A2.15,2.15,0,0,1,108.18,71a2.123,2.123,0,0,1,.739,2.961l-.165-.713c-1.974-8.171-5.922-3.372-6.416-.274-.11.576-.466.6-1.178-.219C99.68,71,97.65,73.792,100.666,76.1a5.713,5.713,0,0,1,.3,2.521l-.081,1.154a2,2,0,0,1-1.206-1.154,2.142,2.142,0,0,1-4.113-.822,2.142,2.142,0,0,1-3.126-2.714,2.165,2.165,0,0,1-2.413-2.165,2.428,2.428,0,0,1,.987-1.837,2.148,2.148,0,0,1-.68-3.787,1.877,1.877,0,0,1-.56-1.129,1.876,1.876,0,0,1-2.659-2.6,1.892,1.892,0,0,1-.055-3.752,1.876,1.876,0,0,1,2.577-2.686,1.954,1.954,0,0,1,3.866-.027,1.885,1.885,0,0,1,3.025,1.385Z" transform="translate(49.116 6.353)" fill="none" stroke="#120071" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47101" data-name="Path 47101" d="M69.581,120.766v2.5" transform="translate(40.979 43.892)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47102" data-name="Path 47102" d="M73.334,123.347h-2.5" transform="translate(41.626 46.178)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47103" data-name="Path 47103" d="M69.581,127.1v-2.5" transform="translate(40.979 47.289)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47104" data-name="Path 47104" d="M67,123.347h2.5" transform="translate(39.195 46.178)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47105" data-name="Path 47105" d="M73.057,124.458a2.359,2.359,0,0,1-2.365-2.366,2.362,2.362,0,0,1-1.459,2.187,2.335,2.335,0,0,1-.906.179A2.38,2.38,0,0,1,70,125.15a2.353,2.353,0,0,1,.693,1.674,2.359,2.359,0,0,1,2.364-2.366Z" transform="translate(39.904 45.067)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
  </g>
</svg>
