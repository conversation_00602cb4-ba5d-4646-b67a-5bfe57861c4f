<svg xmlns="http://www.w3.org/2000/svg" width="225.879" height="193.682" viewBox="0 0 225.879 193.682">
  <g id="Group_21949" data-name="Group 21949" transform="translate(0.385 0.536)">
    <path id="Path_47301" data-name="Path 47301" d="M83.319,57.968c-3.451,2.887-14.142,4.917-24.18,30.247L44.365,74.93A30.727,30.727,0,0,0,29.546,91.418l22.217,19.964a13.045,13.045,0,0,0,18.4-.992l9.206-10.249L75.713,124.4,60.736,141.088a35.178,35.178,0,0,0,16.511,14.838L93.1,138.288a21.1,21.1,0,0,0,4.827-9.18c3.451-14.483,2.278-40.036,4.511-62.05C93.491,65.389,90.176,63.359,83.319,57.968Z" transform="translate(9.362 5.715)" fill="#fecfc4"/>
    <path id="Path_47302" data-name="Path 47302" d="M152.15,74.571l-25.713,27.134,10.2-44.886,25.713-27.134Z" transform="translate(62.764 1.035)" fill="#6153bd"/>
    <path id="Path_47303" data-name="Path 47303" d="M91.06,52.242c-.338-.226-.925-.023-1.692.519l1.781,1.466C91.444,53.189,91.444,52.468,91.06,52.242Z" transform="translate(42.334 8.272)" fill="#6153bd"/>
    <path id="Path_47304" data-name="Path 47304" d="M80.313,61.153c-.475,1.218-.412,2.241-.045,2.481s1.424,0,2.137-.925Z" transform="translate(37.156 12.122)" fill="#6153bd"/>
    <path id="Path_47305" data-name="Path 47305" d="M87.413,52.882c-.338-.226-.925-.022-1.692.519L87.5,54.867C87.793,53.852,87.769,53.13,87.413,52.882Z" transform="translate(40.323 8.546)" fill="#6153bd"/>
    <path id="Path_47306" data-name="Path 47306" d="M54.569,107.975l-4.986,5.548a35.188,35.188,0,0,0,16.511,14.84l5.21-5.8A39.746,39.746,0,0,1,54.569,107.975Z" transform="translate(20.406 31.084)" fill="#6153bd"/>
    <path id="Path_47307" data-name="Path 47307" d="M71.294,55.235,79.64,68.2l2.21-4.015Z" transform="translate(32.372 9.289)" fill="#6153bd"/>
    <path id="Path_47308" data-name="Path 47308" d="M76.805,34.15l1.15,2.481.631-1.692Z" transform="translate(35.409 3.496)" fill="#6153bd"/>
    <path id="Path_47309" data-name="Path 47309" d="M79.812,72.6,62.286,92.085,60.5,104.039Z" transform="translate(26.425 15.789)" fill="#6153bd"/>
    <path id="Path_47310" data-name="Path 47310" d="M47.334,120.917q-2.248,2.408-4.534,4.782c-1.241,7.331-.654,24.156,1.714,31.983,6.292-10.466,8.571-17.1,11.753-27.879A40.1,40.1,0,0,1,47.334,120.917Z" transform="translate(16.318 35.079)" fill="#6153bd"/>
    <path id="Path_47311" data-name="Path 47311" d="M42.545,66.642c-1.083-1.083-2.323-2.413-3.97-4.2-12.769,1.8-26.82,6.067-32.774,8.887a65.944,65.944,0,0,0,28.491,4.06l.5.451A41.881,41.881,0,0,1,42.545,66.642Z" transform="translate(-3.725 12.322)" fill="#6153bd"/>
    <path id="Path_47312" data-name="Path 47312" d="M49.236,73.212,44.368,68.82A30.813,30.813,0,0,0,29.549,85.308l4.94,4.443A43.816,43.816,0,0,1,49.236,73.212Z" transform="translate(9.364 14.71)" fill="#6153bd"/>
    <path id="Path_47313" data-name="Path 47313" d="M97.127,30.688a2.334,2.334,0,0,0-1.534-2.612,2.357,2.357,0,0,0-1.425-4.083,2.375,2.375,0,0,0-2.612-3.338,2.358,2.358,0,0,0-3.541-2.323,2.374,2.374,0,0,0-4.2-1.06,2.479,2.479,0,0,0-4.534.226,2.361,2.361,0,0,0-3.9.75,2.968,2.968,0,0,0-4.712-2.6,3.087,3.087,0,0,0-5.774.293,2.952,2.952,0,0,0-4.332,3.451A2.956,2.956,0,0,0,59.275,24.8a2.957,2.957,0,0,0,1.872,5.255,2.949,2.949,0,0,0,4.669,2.955,2.866,2.866,0,0,0,2.348,1.807,2.36,2.36,0,0,0,2.1,3A2.361,2.361,0,0,0,72.9,41.131a2.361,2.361,0,0,0,3.561,2.3,2.366,2.366,0,0,0,4.2,1.038,3.471,3.471,0,0,0,2.481,1.466,13.4,13.4,0,0,0,1.511-4.392,6.963,6.963,0,0,0-.831-4.556c-3.361-1.105-1.85-5.052.429-3.519,1.083.744,1.493.7,1.579-.022a1.151,1.151,0,0,1,.636-.995,2.374,2.374,0,0,0,4.376-.2,2.375,2.375,0,0,0,3.406.045,21.081,21.081,0,0,1,1.263,4.083,2.374,2.374,0,0,0,.316-3.925,2.451,2.451,0,0,0,1.306-1.763Z" transform="translate(25.012 -7.727)" fill="#6153bd"/>
    <path id="Path_47314" data-name="Path 47314" d="M118.269,21.918a4.685,4.685,0,1,0-.453,2.905A4.688,4.688,0,0,0,118.269,21.918Z" transform="translate(53.131 -5.407)" fill="#6153bd"/>
    <path id="Path_47315" data-name="Path 47315" d="M28.448,88.316l-10.579,3.4,4.371,25.8Z" transform="translate(2.927 22.361)" fill="#6153bd"/>
    <path id="Path_47316" data-name="Path 47316" d="M129.983,59.77l-6.067,5.571a27.251,27.251,0,0,1,6.157,2.1,7.764,7.764,0,0,1,2.255,1.466c.926,1.06-4.036-.451-6.473-.722h-.022a5.387,5.387,0,0,1,1.331.992c.8.915-5.32-1.156-5.865-.767,2.345,1.06,5.977,3.068,6.856,4.63,1.038,1.872-4.286-1.851-7.736-1.964,2.187,1.015,4.579,2.437,5.435,4.016,1.173,2.137-3.067-1.781-8.12-1.94h-.022c4.827,2.932,7.083,5.342.225,2.368a12.488,12.488,0,0,0-3.969-1.038l-13.782,12.6,40.691-1.579,5.977-26.345Z" transform="translate(48.296 10.425)" fill="#6153bd"/>
    <path id="Path_47317" data-name="Path 47317" d="M152.15,74.571l-25.713,27.134,10.2-44.886,25.713-27.134Z" transform="translate(62.764 1.035)" fill="none" stroke="#120071" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47318" data-name="Path 47318" d="M129.983,59.77l-6.067,5.571a27.251,27.251,0,0,1,6.157,2.1,7.764,7.764,0,0,1,2.255,1.466c.926,1.06-4.036-.451-6.473-.722h-.022a5.387,5.387,0,0,1,1.331.992c.8.915-5.32-1.156-5.865-.767,2.345,1.06,5.977,3.068,6.856,4.63,1.038,1.872-4.286-1.851-7.736-1.964,2.187,1.015,4.579,2.437,5.435,4.016,1.173,2.137-3.067-1.781-8.12-1.94h-.022c4.827,2.932,7.083,5.342.225,2.368a12.488,12.488,0,0,0-3.969-1.038l-13.782,12.6,40.691-1.579,5.977-26.345Z" transform="translate(48.296 10.425)" fill="none" stroke="#120071" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47319" data-name="Path 47319" d="M97.127,30.688a2.334,2.334,0,0,0-1.534-2.612,2.357,2.357,0,0,0-1.425-4.083,2.375,2.375,0,0,0-2.612-3.338,2.358,2.358,0,0,0-3.541-2.323,2.374,2.374,0,0,0-4.2-1.06,2.479,2.479,0,0,0-4.534.226,2.361,2.361,0,0,0-3.9.75,2.968,2.968,0,0,0-4.712-2.6,3.087,3.087,0,0,0-5.774.293,2.952,2.952,0,0,0-4.332,3.451A2.956,2.956,0,0,0,59.275,24.8a2.957,2.957,0,0,0,1.872,5.255,2.949,2.949,0,0,0,4.669,2.955,2.866,2.866,0,0,0,2.348,1.807,2.36,2.36,0,0,0,2.1,3A2.361,2.361,0,0,0,72.9,41.131a2.361,2.361,0,0,0,3.561,2.3,2.366,2.366,0,0,0,4.2,1.038,3.471,3.471,0,0,0,2.481,1.466,13.4,13.4,0,0,0,1.511-4.392,6.963,6.963,0,0,0-.831-4.556c-3.361-1.105-1.85-5.052.429-3.519,1.083.744,1.493.7,1.579-.022a1.151,1.151,0,0,1,.636-.995,2.374,2.374,0,0,0,4.376-.2,2.375,2.375,0,0,0,3.406.045,21.081,21.081,0,0,1,1.263,4.083,2.374,2.374,0,0,0,.316-3.925,2.451,2.451,0,0,0,1.306-1.763Z" transform="translate(25.012 -7.727)" fill="none" stroke="#120071" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47320" data-name="Path 47320" d="M81.88,37.708l67.328-11.323" transform="translate(38.206 -0.784)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47321" data-name="Path 47321" d="M10.657,51.488l98.117-16.466" transform="translate(-1.049 0.687)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47322" data-name="Path 47322" d="M77.459,40.519,97.647,58.653" transform="translate(35.77 2.963)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47323" data-name="Path 47323" d="M92.27,54.227c.271-1.015.272-1.739-.09-1.985-.337-.228-.95-.026-1.714.519a18.669,18.669,0,0,0-3.086,3.086" transform="translate(41.237 8.231)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47324" data-name="Path 47324" d="M52.873,75.289c-7.624-6.067-8.255-6.248-14.3-12.857C25.806,64.237,11.755,68.5,5.8,71.319a66.037,66.037,0,0,0,28.51,4.06l10.353,9.315" transform="translate(-3.725 11.995)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47325" data-name="Path 47325" d="M73.112,40.548,94.381,59.63" transform="translate(33.373 2.954)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47326" data-name="Path 47326" d="M85.053,66.9,71.294,55.235,79.627,68.2" transform="translate(32.372 9.289)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47327" data-name="Path 47327" d="M111.82,64.392c.831.181,8.441,2.84,10.038,5.684,1.069,1.893-4.331-1.872-7.827-1.985" transform="translate(54.708 13.393)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47328" data-name="Path 47328" d="M111.34,65.995c2.12.767,7.146,2.889,8.458,5.278,1.188,2.168-3.087-1.8-8.21-1.962" transform="translate(54.443 14.087)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47329" data-name="Path 47329" d="M127.92,71.515c5.369,3.135,8.327,6.038,1.038,2.887-3.765-1.627-31.221-2.458-31.221-2.458a13.781,13.781,0,0,1-6.672-2.551L80.193,61.137" transform="translate(37.276 11.727)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47330" data-name="Path 47330" d="M85.719,53.184,96.771,63s27.7,5.188,29.818,5.793,7.2,1.892,8.685,3.586c.79.9-3.561-.653-6.2-.789" transform="translate(40.322 8.231)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47331" data-name="Path 47331" d="M90.679,54.867c.271-1.015.272-1.739-.09-1.985-.337-.228-.95-.025-1.714.519-3.121,2.221-7.759,9.107-8.729,12.879-.237.926-.195,1.76.113,1.962.356.237,1.376-.045,2.137-.9" transform="translate(37.166 8.183)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47332" data-name="Path 47332" d="M57.945,69.168A23.557,23.557,0,0,0,73.1,64.453" transform="translate(25.014 13.468)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47333" data-name="Path 47333" d="M76.543,65.361l2.413-4.353" transform="translate(35.265 12.005)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47334" data-name="Path 47334" d="M80.064,39.994c-1.489,2.165-4.218,1.218-7.985-2.413" transform="translate(32.804 1.99)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47335" data-name="Path 47335" d="M62.283,92.109,79.812,72.6,60.5,104.039" transform="translate(26.425 15.789)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47336" data-name="Path 47336" d="M51.763,111.368a13.025,13.025,0,0,0,18.4-.992l9.177-10.193L75.713,124.4l-14.977,16.69a35.189,35.189,0,0,0,16.511,14.84L93.1,138.269a21.1,21.1,0,0,0,4.827-9.18c3.452-14.483,2.278-40.059,4.511-62.05-8.977-1.669-12.293-3.7-19.127-9.09C78.9,61.177,69.041,63.572,59.136,88.2L44.365,74.918A30.813,30.813,0,0,0,29.546,91.406Z" transform="translate(9.362 5.707)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47337" data-name="Path 47337" d="M78.554,34.912a15.514,15.514,0,0,1-1.745-.777l1.146,2.493" transform="translate(35.411 3.488)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47338" data-name="Path 47338" d="M86.432,56.263c-1.662-4.8-3.041-12.129-.939-16.727,8.54,3.133,7.817-7.94,5.315-13.791m-26.6,49.938a46.58,46.58,0,0,0,1.94-9.27A42.418,42.418,0,0,1,70.3,52.5c2.125-4.465,7-8.582,9.5-13.33A11.111,11.111,0,0,0,81.2,34.925a6.956,6.956,0,0,0-.831-4.556c-3.361-1.105-1.842-5.062.429-3.519,1.086.74,1.511.7,1.579-.023q.093-.57.227-1.083" transform="translate(28.466 -1.137)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47339" data-name="Path 47339" d="M52.87,107.975a39.741,39.741,0,0,0,16.738,14.593" transform="translate(22.217 31.475)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47340" data-name="Path 47340" d="M32.716,88.175c3.789-6.885,8.526-12.541,14.751-16.466" transform="translate(11.109 16.108)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47341" data-name="Path 47341" d="M56.469,114.184C51.7,120,47.441,124.567,42.8,129.41c-1.241,7.329-.654,24.156,1.714,31.983,6.292-10.466,8.571-17.12,11.753-27.9l9.654-10.737" transform="translate(16.318 31.368)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47342" data-name="Path 47342" d="M24.5,74.332a41.682,41.682,0,0,1,7.759-9.18" transform="translate(6.581 13.615)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47343" data-name="Path 47343" d="M131.9,71.743c2.459.248,7.461,1.781,6.541.722a8,8,0,0,0-2.277-1.489c-3.183-1.555-6.226-2.052-9.609-2.977l-26.236-6.361-10.948-9.1" transform="translate(42.337 7.936)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47344" data-name="Path 47344" d="M76.837,66.925,56.6,96.654l-19.556-15.5" transform="translate(13.498 13.545)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47345" data-name="Path 47345" d="M101.663,24.68a4.689,4.689,0,1,1-1.9-3.044A4.682,4.682,0,0,1,101.663,24.68Z" transform="translate(43.978 -3.884)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47346" data-name="Path 47346" d="M109.893,23.313a4.689,4.689,0,1,1-1.9-3.044A4.676,4.676,0,0,1,109.893,23.313Z" transform="translate(48.515 -4.637)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47347" data-name="Path 47347" d="M118.27,21.918a4.682,4.682,0,1,1-.645-1.711A4.688,4.688,0,0,1,118.27,21.918Z" transform="translate(53.13 -5.408)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47348" data-name="Path 47348" d="M31.473,9.88,22.857,22.015" transform="translate(5.676 -9.88)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47349" data-name="Path 47349" d="M33.85,19.631,21.737,11.015" transform="translate(5.058 -9.255)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47350" data-name="Path 47350" d="M7.395,97.45l-.271.09,8.323,7.376L4.463,116.983l5.887,5.257,10.985-12.09,8.3,7.376,6.2-29.209Z" transform="translate(-4.463 22.065)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47351" data-name="Path 47351" d="M106.2,110.482l-17.458-5.414,14.684-10.195" transform="translate(41.989 25.894)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47352" data-name="Path 47352" d="M110.231,109.007,124.8,98.225l-17.345-4.849" transform="translate(52.304 25.262)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47353" data-name="Path 47353" d="M107.995,89.232l-6.383,31.081" transform="translate(49.081 22.62)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47354" data-name="Path 47354" d="M14.688,70.451,26.39,142.6a19,19,0,0,0,21.924,15.541l9.381-1.579" transform="translate(1.173 7.265)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47355" data-name="Path 47355" d="M175.747,69.944l6.969,42.955a19,19,0,0,1-15.541,21.925l-113.6,19.1" transform="translate(22.607 11.122)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47356" data-name="Path 47356" d="M84.832,21.746,25.756,31.671A19,19,0,0,0,10.215,53.595L16,89.273" transform="translate(-1.441 -3.341)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47357" data-name="Path 47357" d="M79.626,21.113l48.021-8.068a18.992,18.992,0,0,1,21.924,15.541l8.905,54.9" transform="translate(36.964 -8.286)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
    <path id="Path_47358" data-name="Path 47358" d="M45.5,120.917a39.515,39.515,0,0,0,8.932,8.865" transform="translate(18.154 35.079)" fill="none" stroke="#120071" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.769"/>
  </g>
</svg>
