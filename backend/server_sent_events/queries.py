
INSERT_CALLBACK_LOG_AND_CLEAR_LAST_APPROVED_QUERY = """
    with pr_cte as (
        select 
            promo_id,
            last_approved_scenario_id
        from 
            {client_schema}.promo_master 
        where 
            promo_id = {promo_id}
    )
    update {client_schema}.promo_master pm
    set last_approved_scenario_id = null 
    from pr_cte pc
    where 
        pm.promo_id = {promo_id} 
        and pc.last_approved_scenario_id=0;
    INSERT INTO {client_schema}.logs_sim_callback
        (promo_id, scenario_id, user_id, action, status, message, request_data)
    VALUES
        ({promo_id}, {scenario_ids}, {user_id}, {action}, {status}, {message}, {message_dict});
"""

UPDATE_PROMO_STATUS_QUERY = """
    update price_promo.promo_master set status = {status} where promo_id in {promo_id};
"""

REFRESH_STACKED_PROMO_MAPPING_QUERY = """
    select price_promo.fn_update_promo_stacked_offers_mapping_after_finalize(ARRAY[{promo_str}]::int[]);
"""

UPDATE_PROMO_PROCESSING_UPDATE_QUERY = """
    update price_promo.promo_master set is_under_processing = 0 where promo_id in {promo_id}; 
"""