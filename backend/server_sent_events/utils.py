import asyncio
import json
from collections import defaultdict
from common.utils import authenticate_token, get_user_id_from_email
from common_utils import notifications as notify
from configuration.environment import environment
from fastapi import Request
from logger.logger import logger
from server_sent_events.decorators import singleton
from tenant.base import get_tenant_id_context
from pricesmart_common.utils import get_str_repr

@singleton
class SSEController:
    def __init__(self) -> None:
        self.user_notifications = defaultdict(list)
        self.user_guids = defaultdict(set)

    async def message_handler(self, message):
        logger.info(
            f"{environment.LOG_TITLE} : Received request at SSE message handler - payload: {message}"
        )
        user_id = message.get("user_id")
        for _guid in self.user_guids[user_id]:
            self.user_notifications[(user_id,_guid)].append(message)

    async def event_generator(self, **kwargs):
        user_id = -1
        guid = -1
        try:
            request = kwargs["request"]
            guid = kwargs["guid"]
            user_id = kwargs["user_id"]
            self.user_guids[user_id].add(guid)
            while True:
                if await request.is_disconnected():
                    self.cleanup(user_id, guid)
                    break
                
                if (user_id,guid) in self.user_notifications:
                    yield json.dumps({"messages": self.user_notifications.pop((user_id,guid))})
                
                await asyncio.sleep(0.2)
        except asyncio.CancelledError as e:
            if (user_id, guid) in self.user_notifications:
                self.user_notifications.pop((user_id, guid))
            raise e

    async def send_user_notification(self, notification_data: dict, user_id: int):
        """
        sends notification for all the connections the user had
        """
        await notify.create_notification(
            application="promo",
            module=notification_data.get("module", ""),
            message=notification_data.get("message", ""),
            action=notification_data.get("action", ""),
            navigate_to=notification_data.get("navigate_to", ""),
            promo_ids=notification_data.get("promo_ids", None),
            status=notification_data.get("status", True),
            user_id=str(user_id),
            identifier=notification_data.get("identifier", ""),
            header_text=notification_data.get("header_text", ""),
        )

    def cleanup(self, user_id, guid):
        """removes data belongs to the user and guid"""
        self.user_guids[user_id].remove(guid)

    def broadcast(self, message):
        for user_id in self.user_guids:
            for guid in self.user_guids[user_id]:
                self.user_notifications[(user_id, guid)].append(message)

async def mtp_auth_util(request: Request, token: str):
    if environment.is_local:
        import os
        return {"tenant": None,"email_id": None, "user_id": os.getenv("user_id")}
    else:
        tenant = await get_tenant_id_context(request=request)
        tenant = tenant["tenant"]
    logger.info(f"{environment.LOG_TITLE} : Tenant for this application = {tenant}")
    resp = await authenticate_token(token)
    email_id = resp["info"]["email"]
    user_id = (
        request.headers.get("user-id", 0)
        if email_id.endswith("iam.gserviceaccount.com")
        else await get_user_id_from_email(tenant, email_id)
    )

    return {"tenant": tenant, "email_id": email_id, "user_id": user_id}


async def insert_notification(
    request, user_id, module, action, message, promo_id, status, identifier, header_text, promo_ids=None,
    navigate_to: str|None = None
):
    logger.info(
        f"{environment.LOG_TITLE}: Preparing to send notification for user_id: {user_id}"
    )

    sse_controller = SSEController()

    if (not navigate_to) and promo_id is not None:
        navigate_to = str(promo_id)

    # Prepare notification data
    notification_data = {
        "module": module,
        "message": message,
        "action": action,
        "navigate_to": navigate_to,
        "status": status,
        "identifier": identifier,
        "header_text": header_text,
        "promo_ids": promo_ids
    }

    logger.info(
        f"{environment.LOG_TITLE}: Notification data prepared - {notification_data}"
    )

    # Call the message_handler to process the message
    if not isinstance(request,dict):
        request = request.model_dump()
    request["user_id"] = user_id
    
    # Send user notification using SSEController
    try:
        await sse_controller.send_user_notification(notification_data, user_id=user_id)
        logger.info(f"{environment.LOG_TITLE}: Notification sent to user_id: {user_id}")
    except Exception as e:
        logger.error(
            f"{environment.LOG_TITLE}: Failed to send notification to user_id: {user_id} - Error: {str(e)}"
        )


    request = {**request,**notification_data}
    logger.info(
        f"{environment.LOG_TITLE}: Invoking SSE message handler - payload: {request}"
    )
    await sse_controller.message_handler(message=request)
