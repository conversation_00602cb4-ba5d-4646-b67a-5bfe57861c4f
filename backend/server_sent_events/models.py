from typing import Dict, List, Optional

from pydantic import BaseModel


class ServerCallback(BaseModel):
    user_id: int
    guid: Optional[str] = None
    action_log_id: int
    source: Optional[str] = None
    action: str
    promo_id: Optional[int] = None
    status: Optional[int] = None
    new_promo_ids: Optional[List[int]] = None
    success_promo_ids: Optional[List[int]] = None
    failure_promo_ids: Optional[List[int]] = None
    message: Optional[str] = None
    scenario_id: Optional[List[int]] = None
    promo_ids: Optional[List[int]] = None


class NotifyUserRequestModel(BaseModel):
    user_id: int
    notification_data: Dict
