from common_utils.custom_route_handler import CustomRouteHandler
from configuration.environment import environment
from fastapi import Request
from fastapi import status as http_status
from fastapi.routing import APIRouter
from logger.logger import logger
from pricesmart_common import models as common_models
from server_sent_events import models as sse_models
from server_sent_events.utils import SSEController, mtp_auth_util
from sse_starlette.sse import EventSourceResponse


sse_router = APIRouter(route_class=CustomRouteHandler)
sse_controller = SSEController()


@sse_router.get("/sse-output", tags=["server-sent-events"])
async def get_sse_output(request: Request, token: str, guid: str):
    """
    Establish SSE connection for real-time event streaming.
    
    Args:
        request (Request): FastAPI request object
        token (str): Authentication token
        guid (str): Unique identifier for the connection
        
    Returns:
        EventSourceResponse: Server-sent events response object
    """
    token = token.strip()
    token_info = await mtp_auth_util(request=request, token=token)
    event_generator = sse_controller.event_generator(
        request=request, guid=guid, user_id=token_info["user_id"]
    )
    print("event_generator: ", event_generator)
    return EventSourceResponse(event_generator)


@sse_router.post("/user/notify")
async def notify_user(request: sse_models.NotifyUserRequestModel):
    """
    Send notification to a specific user.
    
    Args:
        request (NotifyUserRequestModel): Request model containing user ID and notification data
        
    Returns:
        dict: Response containing:
            - message (str): Success/error message
            - status (bool): Operation status
    """
    logger.info(
        f"{environment.LOG_TITLE} : Received request at /user/notify API - payload: {request.model_dump()}"
    )

    notification_data = request.notification_data
    notification_dict = {
        "guid": None,
        "user_id": request.user_id,
        "action_log_id": None,
        "promo_id": None,
        "action": notification_data["action"],
        "status": http_status.HTTP_200_OK,
        "message": notification_data["message"],
        "scenario_id": [],
    }

    logger.info(
        f"{environment.LOG_TITLE}: Invoking SSE message handler - payload: {notification_dict}"
    )
    await sse_controller.message_handler(message=notification_dict)

    return common_models.BaseResponseBody(message="Success", status=True)
