[tool.ruff]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
select = ["E", "F","PL","TCH","FLY","PYI"]
ignore = ["E501","PLR2004"]

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["A", "B", "C", "D", "E", "F", "G", "I", "N", "Q", "S", "T", "W", "ANN", "ARG", "BLE", "COM", "DJ", "DTZ", "EM", "ERA", "EXE", "FBT", "ICN", "INP", "ISC", "NPY", "PD", "PGH", "PIE", "PL", "PT", "PTH", "PYI", "RET", "RSE", "RUF", "SIM", "SLF", "TCH", "TID", "TRY", "UP", "YTT"]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv"
]

line-length = 120

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.12
target-version = "py312"

[tool.ruff.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.pylint]
max-args = 6

[tool.mypy]
pretty = true


[tool.pylint."MESSAGES CONTROL"]
disable = [
    "missing-module-docstring",
    "import-error"
]

[tool.poetry]
name = "mfe-pricesmart-promo"
version = "0.1.0"
description = "Pricesmart Saksoff5th Promo"
authors = ["Anoop <<EMAIL>>"]
repository = "https://bitbucket.org/insideinsight/mfe-pricesmart-promo.git"

[tool.poetry.dependencies]
python = "^3.12.1"
aiopg = "1.4.0"
aiosqlite = "0.20.0"
async-lru = "2.0.4"
authentication = "1.1.0"
basic-auth-middleware = "1.0.3"
bcrypt = "4.2.0"
cryptography = "43.0.1"
elastic-apm = "6.23.0"
fastapi = "<0.111.0 , >=0.110.0"
fastapi-authz = "1.0.0"
fastapi-scheduler = "0.0.15"
fastapi-utils = "0.7.0"
firebase-admin = "6.5.0"
google-cloud = "0.34.0"
google-cloud-bigquery = "3.25.0"
google-cloud-logging = "3.11.2"
google-cloud-secret-manager = "2.20.2"
greenlet = "3.1.0"
httpcore = "1.0.5"
httpx = "0.27.2"
middleware = "1.2.3"
msgpack = "1.1.0"
multimethod = "1.12"
multipledispatch = "1.0.0"
nest-asyncio = "1.6.0"
openai = "1.46.0"
openpyxl = "3.1.5"
pandas = "2.2.2"
passlib = "1.7.4"
pre-commit = "3.8.0"
prometheus-client = "0.20.0"
proto-plus = "1.24.0"
psutil = "5.9.8"
py-linq = "1.4.0"
pydantic = "2.9.2"
pyjwt = {version = "2.9.0", extras = ["crypto"]}
python-dotenv = "1.0.1"
python-logstash-async = "3.0.0"
python3-saml = "1.16.0"
slack-logger = "0.3.1"
sqlalchemy = "<3.0.0 , >=2.0.29"
sse-starlette = "2.1.3"
starlette = "0.36.3"
starlette-exporter = "^0.21.0"
typing-extensions = "^4.10.0"
urllib3 = "<3.0.0 , >=2.2.1"
uvicorn = "<0.30.0 , >=0.29.0"
xlsxwriter = "3.2.0"
xmlsec = "1.3.14"
zipp = "3.20.2"
mtputils = {git = "*****************:insideinsight/mtp-utils-backend.git", rev = "feature/mtp-utils-lib-promo"}
