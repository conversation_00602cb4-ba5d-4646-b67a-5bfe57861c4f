from landing_page import models as landing_page_models
from landing_page import data as landing_page_module

    
async def get_landing_page_data(
    request_payload: landing_page_models.GetLandingPageTableDataRequest,
):
    
    return await landing_page_module.get_landing_page_data(
        request_payload
    )


async def get_top_and_bottom_performing_offers(
    request_payload: landing_page_models.GetLandingPageTableDataRequest,
):
    
    return await landing_page_module.get_top_and_bottom_performing_offers(
        request_payload
    )