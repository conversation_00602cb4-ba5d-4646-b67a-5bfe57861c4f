from landing_page import models as landing_page_models
from pricesmart_common.utils import async_execute_query,get_str_repr,get_key_value_str
from landing_page import queries as landing_page_queries
from pricesmart_common import constants as common_constants
from promotions import constants as promo_constants
from pricesmart_common.data import get_config_value
from enums.Enums import ConfigModuleEnum,ConfigKeyEnum
from typing import Any, List

async def get_landing_page_data(
    request_payload: landing_page_models.GetLandingPageTableDataRequest
):
    
    finalised_promo_status_list: List[Any] = await get_config_value(ConfigModuleEnum.PROMO,ConfigKeyEnum.FINALISED_PROMO_STATUS_LIST)

    request_payload=request_payload.model_dump()
    
    request_payload["timezone"] = common_constants.CLIENT_TIMEZONE
    request_payload["deleted_promo_status_list"] = get_str_repr([promo_constants.OFFER_STATUS[promo_constants.ARCHIVED]])
    request_payload["finalised_promo_status_list"] = get_str_repr(finalised_promo_status_list)
    request_payload["is_download"] = False
    query = landing_page_queries.FETCH_LANDING_PAGE_TABLE_DATA.format(
        request_payload=get_str_repr(request_payload)
    )
    print(query)
    
    data = await async_execute_query(query)
    return data


async def get_top_and_bottom_performing_offers(
    request_payload: landing_page_models.GetLandingPageTableDataRequest
):
    
    request_payload=request_payload.model_dump()

    query = landing_page_queries.GET_TOP_AND_BOTTOM_PERFORMING_OFFERS.format(
        request_payload=get_str_repr(request_payload)
    )
    print(query)
    data = await async_execute_query(query)
    return data[0] if data else {}
