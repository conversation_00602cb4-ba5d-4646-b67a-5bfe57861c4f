

FETCH_LANDING_PAGE_TABLE_DATA = """
    SELECT 
        * 
    FROM
        price_promo.fn_fetch_landing_page_data_with_filters_wrapper({request_payload}::jsonb)
        
"""


GET_TOP_AND_BOTTOM_PERFORMING_OFFERS = """
    WITH sorted_reporting_data AS (
        SELECT *
        FROM price_promo.fn_fetch_landing_page_data_with_filters_wrapper({request_payload}::jsonb)
        WHERE actualized_incremental_margin IS NOT NULL
        and status_id = 8
    ),
    ranked AS (
        SELECT 
            srd.*,
            ROW_NUMBER() OVER (ORDER BY actualized_incremental_margin DESC) AS top_rank,
            ROW_NUMBER() OVER (ORDER BY actualized_incremental_margin ASC)  AS bottom_rank
        FROM sorted_reporting_data srd
    ),
    currency_aggregated AS (
        SELECT 
            currency_id,
            currency_symbol,
            currency_name,
            JSON_AGG(r ORDER BY actualized_incremental_margin DESC) FILTER (WHERE top_rank <= 10) AS top10,
            JSON_AGG(r ORDER BY actualized_incremental_margin ASC)  FILTER (WHERE bottom_rank <= 10) AS bottom10
        FROM ranked r
        GROUP BY currency_id, currency_symbol, currency_name
    )
    SELECT 
        currency_id,
        currency_symbol,
        currency_name,
        top10,
        bottom10
    FROM currency_aggregated;
        
"""