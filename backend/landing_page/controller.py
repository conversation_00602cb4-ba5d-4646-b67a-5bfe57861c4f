from fastapi import APIRouter
from landing_page import models as landing_page_models
from landing_page import service as landing_page_service
from pricesmart_common import utils as common_utils

landing_page_router = APIRouter(tags=["LandingPageData"])

@landing_page_router.post("/landing-page-data")
async def get_landing_page_data(
    get_promos_request: landing_page_models.GetLandingPageTableDataRequest,
):
    """
    Get data for the workbench view, calendar view and decision dashboard view.
    
    Args:
        get_landing_page_data (GetLandingPageTableDataRequest): Request model containing filters 
            and parameters for fetching Landing page table data
            
    Returns:
        dict: Response containing:
            - data: list of rows of tables data
            - message: Success/error message
            - status: HTTP status code
    """

    data = await landing_page_service.get_landing_page_data(
        get_promos_request,
    )

    return common_utils.create_response(
        data = data
    )


@landing_page_router.post("/get-top-and-bottom-performing-offers")
async def get_top_and_bottom_performing_offers(
    get_promos_request: landing_page_models.GetLandingPageTableDataRequest,
):
    """
    Get data for the reporting landing page.
    
    Args:
        get_landing_page_reporting_data (GetLandingPageTableDataRequest): Request model containing filters 
            and parameters for fetching Landing page table data
            
    Returns:
        dict: Response containing:
            - top 10 and bottom 10 promo list as per inc margin: list of rows of tables data
            - message: Success/error message
            - status: HTTP status code
    """

    data = await landing_page_service.get_top_and_bottom_performing_offers(
        get_promos_request,
    )

    return common_utils.create_response(
        data = data
    )