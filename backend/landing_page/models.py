from pricesmart_common import models as common_models
from pydantic import BaseModel
from typing import Any, Optional

class Aggregation(BaseModel):
    marketing: list[str] = None
    timeline: int = -1
    product_hierarchy_levels: list[int] = []
    store_hierarchy_levels: list[int] = []
    target_currency_id: Optional[int] = None

class GetLandingPageTableDataRequest(common_models.OptionalBaseFilters):
    event_ids: list[int] = []
    promo_ids: list[int] = []
    target_currency_id: Optional[int] = None
    screen_type: Optional[str] = None
    aggregation: Optional[Aggregation] = None

