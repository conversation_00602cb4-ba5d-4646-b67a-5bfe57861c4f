from enum import Enum
from client_configuration import constants as client_configuration_constants
class LogLevel(Enum):
    CRITICAL = 50
    FATAL = CRITICAL
    ERROR = 40
    WARNING = 30
    WARN = WARNING
    INFO = 20
    DEBUG = 10
    NOTSET = 0

class CacheType(Enum):
    DISTRIBUTED = 1
    LOCAL = 2

class CompressionType(Enum):
    gzip = 1
    bz2 = 2
    lzma = 3
    zipfile = 4
    lz4 = 5

class DataTypeSize(Enum):
    ORIGINAL = 1
    PICKLED = 2
    COMPRESSED = 3
    NONE = 4


class FailedRequestStatus(Enum):
    BAD_REQUEST = 400
    INTERNAL_SERVER_ERROR = 500
    UNAUTHORIZED = 401
    UNDER_MAINTENANCE = 503
    COMMON_EXCEPTION = 400
    INVALID_ACCESS = 402
    CONFLICT = 409

    def message(self):
        """
        The message function is a helper function that returns the message for
        a given Enum. It is used to return the error messages in JSON format.
        
        :param self: Access the class variables
        :return: The message for the corresponding error
        :author: <PERSON><PERSON>[1533]
        """
        
        # self is the member here
        if self.name == "BAD_REQUEST":
            return "Bad request, check the endpoints and the contract attributes name and datatype"
        elif self.name == "INTERNAL_SERVER_ERROR":
            return "Internal Server Error"
        elif self.name == "UNAUTHORIZED":
            return "Invalid token."
        elif self.name == "UNDER_MAINTENANCE":
                    return "Under maintenance. will be back very soon"
        elif self.name == "COMMON_EXCEPTION":
                    return "un-known error, contact to the support team."
        elif self.name == "INVALID_ACCESS":
                    return "Invalid access to the resources."
        elif self.name == "CONFLICT":
            return "Key already exists, UniqueConstraintViolation"

class CRUDOperation(Enum):
    NONE = 0
    INSERT = 1
    UPDATE = 2
    DELETE = 3
    GET = 4
    LOCKED =5
    UNLOCKED =7
    MULTI_OPERATION=6

class ConditionOperator(Enum):
    AND=1
    OR=2
    IN=3
    NOT_IN =4
    LIKE_START =5
    LIKE_END =6
    LIKE =7

class Operator(Enum):
    EQUAL=1
    NOT_EQUAL=2
    GREATER_THEN=3
    LESS_THEN =4
    GREATER_THEN_EQUAL =5
    LESS_THEN_EQUAL =6

class OperationStatus(Enum):
    SUCCESS=200
    SUCCESS_WITH_NO_RETURN=201
    OPERATION_ABORTED = 202
    FAIL= 400
    PARTIAL = 210

class DownloadType(Enum):
    CSV = 0
    XLSX =1
    TXT = 2

class QueryType(Enum):
     SELECT= "select"
     UPDATE ="update"
     INSERT="insert"
     DELETE ="delete"
     TRUNCATE ="truncate"
     DROP="drop"
    
class PromoStatusEnum(Enum):
    DRAFT_COPIED =  0
    TO_FINALIZE =  2
    FINALIZED =  4
    ARCHIVED =  6
    EXECUTION_APPROVED =  8
    PLACEHOLDER =  -1

class ConfigModuleEnum(Enum):
    EVENT = "event"
    PRODUCT = "product"
    STORE = "store"
    PROMO = "promo"

class ConfigKeyEnum(Enum):
    ALLOW_EVENT_DELETE_AFTER_EVENT_LOCK = "allow_event_delete_after_event_lock"
    ALLOW_PROMO_APPROVAL_AFTER_EVENT_LOCK = "allow_promo_approval_after_event_lock" 
    ALLOW_PROMO_DELETE_AFTER_EVENT_LOCK = "allow_promo_delete_after_event_lock"
    ALLOW_PROMO_EXECUTION_AFTER_EVENT_LOCK = "allow_promo_execution_after_event_lock"
    ALLOW_PROMO_FINALIZE_AFTER_EVENT_LOCK = "allow_promo_finalize_after_event_lock"
    CLIENT_PRODUCT_ID_KEY = "client_product_id_key"
    HIERARCHY_FILTERS = "hierarchy_filters"
    EXCEL_UPLOAD_CONFIGURATION = "excel_uplod_configuration"
    DISCOUNT_LEVEL_SKU_ADDITIONAL_COLUMNS = "discount_level_sku_additional_columns"
    DISCOUNT_LEVEL_STORE_ADDITIONAL_COLUMNS = "discount_level_store_additional_columns"
    EXCLUSION_UPLOAD_CONFIGURATION = "exclusion_upload_configuration"
    STORE_DOWNLOADS_SELECT_COLUMNS = "store_downloads_select_columns"
    FINALISED_PROMO_STATUS_LIST = "finalised_promo_status_list"

class MaximizationParameterEnum(str, Enum):
     REVENUE="revenue"
     GROSS_MARGIN="gross_margin"
     GROSS_MARGIN_PERCENT="gross_margin_percent"
     UNITS ="units"

class EnhancedFilteringMetricsEnum(str, Enum):
     ENTIRE_OFFER_DURATION="entire_offer_duration"
     SELECTED_DATE_RANGE="selected_date_range"


class IdentifierEnum(Enum):
    PRIMARY = "primary"
    EXPANDED = "expanded"
    STANDARD = "standard"
    PLURAL = "plural"
    STANDARD_PLURAL = "standard_plural"
