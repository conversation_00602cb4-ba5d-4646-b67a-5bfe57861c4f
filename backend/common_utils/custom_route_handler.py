import json
import time
from datetime import datetime
from typing import Callable

import pytz
from configuration.environment import environment
from exceptions import exception_handlers as exc_handlers
from exceptions import utils as excep_utils
from fastapi import File, Form, Request, Response, UploadFile
from fastapi.routing import APIRoute
from logger.logger import logger
from pricesmart_common.utils import async_execute_query
from server_sent_events.utils import mtp_auth_util


class CustomRouteHandler(APIRoute):
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def capture_api_details(request: Request) -> Response:
            api_execution_time = datetime.now(pytz.timezone("Asia/Kolkata"))
            api_method = request.method
            api_endpoint = request.url
            api_endpoint = str(api_endpoint).split("v1", 1)[1]
            if api_endpoint.startswith("/sse-output"):
                api_endpoint = api_endpoint[
                    : api_endpoint.index("/sse-output") + len("/sse-output")
                ]
            api_request_payload = await request.body()

            if api_endpoint in [
                "/get-products-from-file",
                "/get-stores-from-file",
                "/upload-exclusion",
            ]:  # Handle file uploads
                form = await request.form()
                file: UploadFile = form.get("file")
                if file:
                    file_content = await file.read()
                    try:
                        api_request_payload = json.loads(file_content.decode("utf-8"))
                    except UnicodeDecodeError:
                        api_request_payload = {
                            "error": "File content is not UTF-8 encoded"
                        }
                    except json.JSONDecodeError:
                        api_request_payload = {
                            "error": "File content is not valid JSON"
                        }
            else:
                api_request_payload = api_request_payload.decode()
                if api_request_payload:
                    api_request_payload = json.loads(api_request_payload)

            if api_endpoint == "/sse-output":
                token_info = await mtp_auth_util(
                    request=request, token=request.query_params.get("token")
                )
                user_id = token_info["user_id"]
            elif api_endpoint == "/user/notify":
                user_id = api_request_payload.get("user_id")
            else:
                user_id = request.state.user_id

            user_name_query = f"""
                SELECT name FROM global.user_master WHERE user_code = {user_id}
            """
            user_name_res = await async_execute_query(query=user_name_query)
            user_name = [row["name"] for row in user_name_res][0]

            start = time.time()

            try:
                response: Response = await original_route_handler(request)
            except Exception as e:
                elapsed_time = time.time() - start
                formatted_duration = datetime.utcfromtimestamp(elapsed_time).strftime(
                    "%H:%M:%S.%f"
                )[:-3]
                try:
                    # Log the error details
                    excep_utils.log_error_details(e)
                    response = await exc_handlers.custom_exception_handler(request, e)
                except Exception as logging_exception:
                    # Handle any error that occurs while logging
                    response = await exc_handlers.custom_exception_handler(
                        request, logging_exception
                    )

                # Decode the response body and convert it to a dictionary
                response_str = response.body.decode()
                response_dict = json.loads(response_str)

                error = response_dict.get("error", {}) or {}
                error_details = {
                    "type": error.get("type", "Unknown"),
                    "detail": error.get("detail", "No details available"),
                }
                api_details = create_api_details(
                    response,
                    api_method,
                    api_endpoint,
                    user_id,
                    user_name,
                    api_execution_time,
                    formatted_duration,
                    api_request_payload,
                    error_details,
                )
                logger.error(f"{environment.LOG_TITLE} : api_details = {api_details}")

                return response

            elapsed_time = time.time() - start
            formatted_duration = datetime.utcfromtimestamp(elapsed_time).strftime(
                "%H:%M:%S.%f"
            )[:-3]

            api_details = create_api_details(
                response,
                api_method,
                api_endpoint,
                user_id,
                user_name,
                api_execution_time,
                formatted_duration,
                api_request_payload,
            )

            if response.status_code != 200:
                error_details = response.body.decode()
                api_details["error"] = " ".join(error_details.split())
                logger.error(f"{environment.LOG_TITLE} : api_details = {api_details}")

            if response.status_code == 400:
                response.headers["X-Response-Error"] = str(response.body.decode())

            logger.info(f"{environment.LOG_TITLE} : api_details = {api_details}")

            return response

        def create_api_details(
            response,
            api_method,
            api_endpoint,
            user_id,
            user_name,
            api_execution_time,
            formatted_duration,
            api_request_payload,
            error_details=None,
        ):
            api_details = {
                "api_response_status": response.status_code,
                "api_method": api_method,
                "api_endpoint": api_endpoint,
                "user_id": user_id,
                "user_name": user_name,
                "environment": environment.LOG_TITLE,
                "api_response_duration": formatted_duration,
                "api_execution_time": str(api_execution_time),
                "api_request_payload": api_request_payload,
            }
            if error_details:
                api_details["error"] = error_details
            return api_details

        return capture_api_details
