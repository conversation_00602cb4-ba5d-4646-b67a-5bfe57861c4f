import app.main as server
import pricesmart.modules.reports.constants as report_constants
import pricesmart_common.constants as global_constants
from configuration.environment import environment
from fastapi import APIRouter
from logger.logger import logger
from pricesmart.modules.reports.controllers import (
    reports_controller,
    sap_extract_controller,
)

server.app.include_router(
    reports_controller.router,
    prefix=f"{global_constants.API_PREFIX}",
    tags=[report_constants.MODULE_NAME],
)
server.app.include_router(
    sap_extract_controller.router,
    prefix=f"{global_constants.API_PREFIX}",
    tags=[report_constants.MODULE_NAME],
)
logger.info(f" {environment.LOG_TITLE}: {report_constants.MODULE_NAME}")
