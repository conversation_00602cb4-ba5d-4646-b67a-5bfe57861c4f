ROUNDING = [
    {
        "function_name": "round_number",
        "kwargs": {
            "ndigits": 0
        }
    },
]

BASE_FORMATTING_FUNCTIONS = [
    *ROUNDING,
    # {
    #     "function_name": "get_comma_seperated_number"
    # },
    # {
    #     "function_name": "add_dollar_sign"
    # }
]


EVENT_METRICS = {
    "event_name": {"display_name": "event"},
    "weekly_start_date": {"display_name":"start_date","functions": [{"function_name": "to_date_string"}]},
    "weekly_end_date": {"display_name":"end_date","functions": [{"function_name": "to_date_string"}]},
    "start_date": {"functions":[{"function_name":"to_date_string"}]},
    "end_date": {"functions":[{"function_name":"to_date_string"}]},
    "performance": {},
    "ia_recommended": {"display_name": "IA Recommended Discount"},
    "products": {},
    "event_division_ids": {"display_name":"Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_division_names": {"display_name":"Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_ids": {"display_name":"Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_names": {"display_name":"Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_avg_basket_size": {"display_name": "Avg Basket Size","functions": ROUNDING},
    "event_units_per_txn": {"display_name": "Units Per Txn","functions": ROUNDING},
    "event_txn": {"display_name": "# of Txn","functions": ROUNDING},
    "event_created_by": {"display_name": "created_by"},
    "actuals_markdown": {"display_name": "Actual Markdown","functions": BASE_FORMATTING_FUNCTIONS},
    "mark_down_projected_budget": {"display_name": "Markdown_$","functions": BASE_FORMATTING_FUNCTIONS},
    "actuals_sales_unit": {"display_name": "Actualized units","functions": ROUNDING},
    "actuals_margin_rate": {"display_name": "Actualised Margin Rate %"},
    "finalized_sales_unit": {"display_name": "Finalized Units","functions": ROUNDING},
    "finalized_baseline_sales_units": {"display_name": "Baseline Units","functions": ROUNDING},
    "finalized_incremental_sales_units": {"display_name": "Incremental Units","functions": ROUNDING},
    "finalized_margin_rate": {"display_name": "IA Projected Margin Rate %"},
    "actuals_sales": {"display_name": "Actualized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_sales": {"display_name": "Finalized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_revenue": {"display_name": "Baseline Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_revenue": {"display_name": "Incremental Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "actuals_gm": {"display": "Actualized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_gm": {"display_name": "Finalized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_margin": {"display_name": "Baseline Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_margin": {"display_name": "Incremental Margin","functions": BASE_FORMATTING_FUNCTIONS}
}


PROMO_METRICS = {
    "name": {"display_name": "offer"},
    "event_name": {"display_name": "event"},
    "weekly_start_date": {"display_name":"start_date","functions": [{"function_name": "to_date_string"}]},
    "start_date": {"functions":[{"function_name":"to_date_string"}]},
    "weekly_end_date": {"display_name":"end_date","functions": [{"function_name": "to_date_string"}]},
    "end_date": {"functions":[{"function_name":"to_date_string"}]},
    "offer_comment": {"display_name": "comments"},
    "performance": {},
    "ia_recommended": {"display_name": "IA Recommended Discount"},
    "offer_value": {"display_name": "Finalized Discount"},
    "products": {},
    "promo_division_ids": {"display_name":"Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_division_names": {"display_name":"Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_ids": {"display_name":"Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_names": {"display_name":"Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_offer_avg_basket_size": {"display_name": "Avg Basket Size","functions": ROUNDING},
    "event_offer_units_per_txn": {"display_name": "Units Per Txn","functions": ROUNDING},
    "event_offer_txn": {"display_name": "# of Txn","functions": ROUNDING},
    "promo_created_by": {"display_name": "created_by"},
    "actuals_markdown": {"display_name": "Actual Markdown","functions": BASE_FORMATTING_FUNCTIONS},
    "mark_down_projected_budget": {"display_name": "Markdown_$","functions": BASE_FORMATTING_FUNCTIONS},
    "actuals_sales_unit": {"display_name": "Actualized units","functions": ROUNDING},
    "actuals_margin_rate": {"display_name": "Actualised Margin Rate %"},
    "finalized_sales_unit": {"display_name": "Finalized Units","functions": ROUNDING},
    "finalized_baseline_sales_units": {"display_name": "Baseline Units","functions": ROUNDING},
    "finalized_incremental_sales_units": {"display_name": "Incremental Units","functions": ROUNDING},
    "finalized_margin_rate": {"display_name": "IA Projected Margin Rate %"},
    "actuals_sales": {"display_name": "Actualized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_sales": {"display_name": "Finalized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_revenue": {"display_name": "Baseline Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_revenue": {"display_name": "Incremental Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "actuals_gm": {"display": "Actualized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_gm": {"display_name": "Finalized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_margin": {"display_name": "Baseline Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_margin": {"display_name": "Incremental Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "vendor_funding_amount": {"display_name": "Vendor Funding Amount"},
    "vendor_funding_per_unit": {"display_name": "Vendor Funding Per Unit $"},
    "vendor_funding_type": {"display_name": "Vendor Funding Type"}
}


PROMO_EVENT_METRICS = {
    "event_name": {"display_name": "event"},
    "name": {"display_name": "offer"},
    "weekly_start_date": {"display_name":"start_date","functions": [{"function_name": "to_date_string"}]},
    "weekly_end_date": {"display_name":"end_date","functions": [{"function_name": "to_date_string"}]},
    "start_date": {"functions":[{"function_name":"to_date_string"}]},
    "end_date": {"functions":[{"function_name":"to_date_string"}]},
    "offer_comment": {"display_name": "comments"},
    "performance": {},
    "ia_recommended": {"display_name": "IA Recommended Discount"},
    "offer_value": {"display_name": "Finalized Discount"},
    "products": {},
    "event_division_ids": {"display_name":"Event Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_division_names": {"display_name":"Event Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_ids": {"display_name":"Event Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_names": {"display_name":"Event Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_division_ids": {"display_name":"Promo Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_division_names": {"display_name":"Promo Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_ids": {"display_name":"Promo Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_names": {"display_name":"Promo Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_offer_avg_basket_size": {"display_name": "Avg Basket Size","functions": ROUNDING},
    "event_offer_units_per_txn": {"display_name": "Units Per Txn","functions": ROUNDING},
    "event_offer_txn": {"display_name": "# of Txn","functions": ROUNDING},
    "promo_created_by": {"display_name": "created_by"},
    "actuals_markdown": {"display_name": "Actual Markdown","functions": BASE_FORMATTING_FUNCTIONS},
    "mark_down_projected_budget": {"display_name": "Markdown_$","functions": BASE_FORMATTING_FUNCTIONS},
    "actuals_sales_unit": {"display_name": "Actualized units","functions": ROUNDING},
    "actuals_margin_rate": {"display_name": "Actualised Margin Rate %"},
    "finalized_sales_unit": {"display_name": "Finalized Units","functions": ROUNDING},
    "finalized_baseline_sales_units": {"display_name": "Baseline Units","functions": ROUNDING},
    "finalized_incremental_sales_units": {"display_name": "Incremental Units","functions": ROUNDING},
    "actuals_sales": {"display_name": "Actualized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_sales": {"display_name": "Finalized Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_revenue": {"display_name": "Baseline Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_revenue": {"display_name": "Incremental Revenue","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_margin_rate": {"display_name": "IA Projected Margin Rate %"},
    "actuals_gm": {"display": "Actualized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_gm": {"display_name": "Finalized Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_baseline_margin": {"display_name": "Baseline Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "finalized_incremental_margin": {"display_name": "Incremental Margin","functions": BASE_FORMATTING_FUNCTIONS},
    "promo_created_by": {"display_name": "promo created_by"},
    "event_created_by": {"display_name": "event created_by"},
    "vendor_funding_amount": {"display_name": "Vendor Funding Amount"},
    "vendor_funding_per_unit": {"display_name": "Vendor Funding Per Unit $"},
    "vendor_funding_type": {"display_name": "Vendor Funding Type"}
}

EVENTS_CALENDAR_REPORT = {
    "name": {
        "display_name": "Event Name"
    },
    "start_date": {},
    "end_date": {},
    "event_lifecycle_status": {"display_name":"status"},
    "event_promo_count": {"display_name":"No. of Promos"},
    "customer_segment": {"display_name": "Customer Segment"},
    "performance": {"functions": [{"function_name": "get_performance"}]},
    "event_products_count": {"display_name": "Products"},
    "event_division_ids": {"display_name":"Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_division_names": {"display_name":"Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_ids": {"display_name":"Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "event_department_names": {"display_name":"Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "finalized_sales_units": {"display_name": "IA Projected Sales Units","functions": [*ROUNDING,]},
    "finalized_baseline_sales_units": {"display_name": "Baseline Sales Units","functions" : [*ROUNDING,]},
    "finalized_incremental_sales_units": {
        "display_name": "Incremental Sales Units",
        "functions": [*ROUNDING,]
    },
    "finalized_revenue": {
        "display_name": "IA Projected Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized_baseline_revenue": {
        "display_name": "Baseline Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized_incremental_revenue": {
        "display_name": "Incremental Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized_margin": {
        "display_name": "IA Projected Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized_baseline_margin": {
        "display_name": "Baseline Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized_incremental_margin": {
        "display_name": "Incremental Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    }
}

PROMO_CALENDAR_REPORT = {
    "event_name": {"display_name": "Event Name"},
    "promo_name": {"display_name": "Promo Name"},
    "start_date": {},
    "end_date": {},
    "offer_status": {"display_name": "Offer Status"},
    "status": {"functions": [{"function_name":"get_offer_status"}]},
    "products": {},
    "promo_division_ids": {"display_name":"Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_division_names": {"display_name":"Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_ids": {"display_name":"Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_names": {"display_name":"Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "performance": {"functions": [{"function_name": "get_performance"}]},
    "finalized.sales_units": {"display_name": "IA Projected Sales Units","functions": [*ROUNDING,]},
    "finalized.baseline_sales_units": {"display_name": "Baseline Sales Units","functions" : [*ROUNDING,]},
    "finalized.incremental_sales_units": {
        "display_name": "Incremental Sales Units",
        "functions": [*ROUNDING,]
    },
    "finalized.revenue": {
        "display_name": "IA Projected Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.baseline_revenue": {
        "display_name": "Baseline Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.incremental_revenue": {
        "display_name": "Incremental Revenue",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.margin": {
        "display_name": "IA Projected Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.baseline_margin": {
        "display_name": "Baseline Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.incremental_margin": {
        "display_name": "Incremental Margin",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "finalized.effective_discount": {
        "display_name": "IA Projected Effective Discount",
        "functions": BASE_FORMATTING_FUNCTIONS
    },
    "offer_comment": {
        "display_name": "Offer Comment"
    }
}

BASE_REPORTING_METRICS = {
    "actual_markdown": {"display_name": "Actual Markdown $","functions": BASE_FORMATTING_FUNCTIONS},
    "total_offers": {},
    "under_performing_offers": {},
    "underperforming_percentage": {
        "display_name": "under_performing_percentage",
        "functions": [
            {"function_name": "convert_to_percentage"}
        ]
    },
    "total_margin": {"functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_margin": {"functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_margin_percentage": {
        "functions": [
            {"function_name": "convert_to_percentage"}
        ]
    },
    "positive_incremental_margin": {"functions": BASE_FORMATTING_FUNCTIONS},
    "negative_incremental_margin": {"functions": BASE_FORMATTING_FUNCTIONS},
    "total_revenue": {"functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_revenue": {"functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_revenue_percentage": {
        "functions": [
            {"function_name": "convert_to_percentage"}
        ]
    },
    "positive_incremental_revenue": {"functions": BASE_FORMATTING_FUNCTIONS},
    "negative_incremental_revenue": {"functions": BASE_FORMATTING_FUNCTIONS},
    "total_sales_units": {"functions": []},
    "incremental_sales_units": {"functions": [*ROUNDING, ]},
    "incremental_sales_units_percentage": {"functions": []},
    "positive_increment_sales_units": {"functions": [*ROUNDING, ]},
}


REPORTING_METRICS_PRODUCT_H1 = {
    "product_h1": {"display_name": "Division"},
    **BASE_REPORTING_METRICS

}

REPORTING_METRICS_PRODUCT_H2 = {
    "product_h1": {"display_name": "Division"},
    "product_h2": {"display_name": "Department"},
    **BASE_REPORTING_METRICS

}

REPORTING_METRICS_PRODUCT_H3 = {
    "product_h1": {"display_name": "Division"},
    "product_h2": {"display_name": "Department"},
    "product_h3": {"display_name": "Class"},
    **BASE_REPORTING_METRICS


}

REPORTING_METRICS_PRODUCT_H4 = {
    "product_h1": {"display_name": "Division"},
    "product_h2": {"display_name": "Department"},
    "product_h3": {"display_name": "Class"},
    "product_h4": {"display_name": "SubClass"},
    **BASE_REPORTING_METRICS
}

REPORTING_METRICS_ALL = {
    **BASE_REPORTING_METRICS
}

TOP_AND_BOTTOM_OFFERS = {
    "offer_name": {"display_name": "Offer Name"},
    "promo_division_ids": {"display_name":"Division #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_division_names": {"display_name":"Division Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_ids": {"display_name":"Department #","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "promo_department_names": {"display_name":"Department Name","functions":[{"function_name":"sorted_arr"},{"function_name":"array_to_str"}]},
    "incremental_margin": {"display_name": "Incremental Margin", "functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_sales": {"display_name": "Incremental Revenue", "functions": BASE_FORMATTING_FUNCTIONS},
    "incremental_units": {"display_name": "Incremental Sales Unit", "functions": [*ROUNDING, ]},
    "actual_markdown": {"display_name": "Actual Markdown","functions": BASE_FORMATTING_FUNCTIONS},
    "actual_margin": {"display_name": "Actual Margin", "functions": BASE_FORMATTING_FUNCTIONS},
    "actual_sales": {"display_name": "Actual Revenue", "functions": BASE_FORMATTING_FUNCTIONS},
    "actual_unit": {"display_name": "Actual Sales Unit", "functions": [*ROUNDING, ]},
    "row_num": {"display_name": "Rank"}
}


EVENT_OFFER_CHANGE = {
    "event_name": {"display_name": "Event Name"},
    "promo_name": {"display_name": "Offer Name"},
    "offer_change": {"display_name": "Offer Change"},
    "offer_type": {"display_name": "Offer Type Change"},
    "offer_depth": {"display_name": "Offer Depth Change"},
    "hero_sku_change": {"display_name": "Hero SKU Change"},
    "skus_change": {"display_name": "SKU Change"}
}

EVENT_OFFER_CHANGE_SKU_LEVEL = {
    "event_name": {"display_name": "Event Name"},
    "promo_name": {"display_name": "Offer Name"},
    "sku": {"display_name": "SKU"},
    "sku_change": {"display_name": "SKU Change"},
    "offer_type_change": {"display_name": "Offer Type Change"},
    "offer_depth_change": {"display_name": "Offer Depth Change"},
    "hero_sku_change": {"display_name": "Hero SKU Change"}
}