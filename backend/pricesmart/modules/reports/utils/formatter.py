from typing import Dict, List, Optional
from pydantic import BaseModel
from pandas import DataFrame

from .function_util import Function, FunctionUtil




class ReportColumns(BaseModel):
    display_name : Optional[str] = None 
    functions : Optional[List[Function]] = []






class ReportFormatter(BaseModel):
    columns : Dict[str,ReportColumns]
    df : DataFrame

    class Config:
        arbitrary_types_allowed = True

    def remove_unwanted_columns(self):
        columns = [column for column in self.columns if column in self.df]
        self.df =  self.df[columns].copy()
    
    def rename_columns(self):
        self.df.rename(
            columns={
                column : column_config.display_name
                for column,column_config in self.columns.items()
                if column_config.display_name is not None
            },
            inplace=True
        )
        self.df = self.df.loc[:,~self.df.columns.duplicated()].copy() # to remove duplicate columns -- first columns will be kept

    def apply_functions(self):
        for column,column_config in self.columns.items():
            for function in column_config.functions:
                try:
                    function_instance = FunctionUtil.get_function_instance(function)
                    self.df[column] = self.df[column].apply(function_instance)
                except Exception as err:
                    print(f"error : {repr(err)}",f"column: {column}",f"function: {function}",sep = "\t")

    def format(self):
        self.remove_unwanted_columns()
        self.apply_functions()
        self.rename_columns()
        return self.df
    
    

