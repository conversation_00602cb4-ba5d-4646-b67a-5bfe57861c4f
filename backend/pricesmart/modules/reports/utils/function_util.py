from pydantic import BaseModel
from typing import Dict,Callable, Optional
from functools import partial,wraps

class Function(BaseModel):
    function_name : str
    kwargs : Optional[Dict] = {}


class FunctionUtil:
    function_instances = {
        "int" : int,
        "float" : float,
        "str" : str,
        "lower" : str.lower,
        "upper" : str.upper
    }


    @classmethod
    def get_function_instance(cls,function : Function) -> Callable:
        function_instance = cls.function_instances.get(function.function_name)
        if function_instance is None:
            raise Exception(f"Unknown function : {function.function_name}. It might be unimplemented or unregistered with FunctionUtil")
        partial_function = partial(
            function_instance,
            **function.kwargs
            )
        
        return partial_function

    @classmethod
    def register(cls,func):
        name = func.__name__
        cls.function_instances[name]=func

        @wraps(func)
        def wrapper(*args, **kwargs):
            """A wrapper function"""
            return func(*args,**kwargs)
    
        return wrapper

