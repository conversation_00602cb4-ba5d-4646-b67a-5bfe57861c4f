from .function_util import FunctionUtil
from promotions import constants as promo_constants
import math
from datetime import datetime,date


@FunctionUtil.register
def get_comma_seperated_number(number:int):
    if number is None or math.isnan(number):
        return None
    
    if number-int(number) == 0:
        # to remove trailing zeros ex: 4.0 -> 4, 3454.0 -> 3454
        number = int(number)
        
    return f"{number:,}"


@FunctionUtil.register
def append_string(string: str, string_to_append : str):
    if string is None:
        return None

    return string + string_to_append

@FunctionUtil.register
def round_number(number : int, ndigits : int = 0):
    if number is None or math.isnan(number):
        return None

    return round(number,ndigits=ndigits)

@FunctionUtil.register
def add_dollar_sign(string: str):
    if string is None or string == "nan":
        return None
    
    if string[0] == "-":
        # for negative number
        return f"(${string[1:]})"
    
    return "$"+string

@FunctionUtil.register
def convert_to_percentage(number: float):
    if number is None or math.isnan(number):
        return None

    return round(number*100,2)


@FunctionUtil.register
def get_performance(number):
    if number is None or math.isnan(number):
        return None
    return ('toxic'
            if number < -promo_constants.PERFORMANCE_COMPARISON_VALUE 
            else 
            'average' 
            if -promo_constants.PERFORMANCE_COMPARISON_VALUE <= number <= promo_constants.PERFORMANCE_COMPARISON_VALUE 
            else 
            'good'
    )
    

@FunctionUtil.register
def to_int(val):
    if val is None or math.isnan(val):
        return None
    return int(val)

@FunctionUtil.register
def to_string(val):
    if val is None:
        return None
    return str(val)


@FunctionUtil.register
def to_date(val,format="%m/%d/%Y"):
    if val is None:
        return None
    if isinstance(val,date):
        return val
    return datetime.strptime(val,format).date()


@FunctionUtil.register
def to_date_string(val,format="%m/%d/%Y"):
    if val is None:
        return None
    if isinstance(val,date):
        return val.strftime(format)
    elif isinstance(val,datetime):
        return val.strftime(format)
    return val

@FunctionUtil.register
def get_offer_status(status):
    return promo_constants.OFFER_STATUS.get(status)


@FunctionUtil.register
def array_to_str(arr):
    value =   ", ".join([str(i) for i in arr] or [])
    if len(arr):
        value+=","
    return value



@FunctionUtil.register
def sorted_arr(arr):
    return sorted(arr or [])



