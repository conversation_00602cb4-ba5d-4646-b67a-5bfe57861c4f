MODULE_NAME = "Download reports"
download_table_to_xlsx = {
    "Dashboard_download": "experiments-list",
    "Experiment_summary": "experiment-summary",
}
DashboardDownloadKey = "Dashboard_download"
CsvKey = ".csv"
experiment_summary_file_name = "Experiment_summary_"
experiment_summary_key = "Experiment_summary"
ExperimentListKey = "Experiment_List_"
IdColumKey = "id"
ExportsDirectoryKey="DownloadedReports"
USER_REPORTS_DIRECTORY = "UserDownloads"

DOWNLOAD_REPORTS_URL = "/reports/download"
DOWNLOAD_METRICS_URL = "/reports/metrics"
DOWNLOAD_SCENARIO_URL = "/reports/scenario"
GET_EVENT_DOWNLOAD_URL = "/reports/get-events/download"
GET_PROMO_DOWNLOAD_URL = "/reports/get-promo/download"
SAP_EXTRACT_DOWNLOAD_URL = "/reports/sap/extract"
SAP_MARKETING_DOWNLOAD_URL = "/reports/sap/marketing-extract"
EVENT_LOCK_AUDIT_PROMO_LEVEL = "/reports/event-lock-audit/promo"
EVENT_LOCK_AUDIT_SKU_LEVEL = "/reports/event-lock-audit/sku"
DOWNLOAD_BACKGROUND_PROCESSED_REPORTS = "/downloaded-report"