import os
import httpx
from pricesmart_common.models import BaseResponseBody
from pricesmart_common.staticclass import StaticClass
from pricesmart_common.utils import get_str_repr
from enums.Enums import DownloadType, OperationStatus
from pricesmart.database.operations.exception_details import CodeException
from pricesmart_common.utils import async_execute_query
from pricesmart.database.operations.postgres.query_executor import QueryExecutorPostgres
from pricesmart.modules.reports.download_data import create_user_xlsx_workbook, create_xlsx_workbook
from server_sent_events.utils import SSEController
from fastapi import APIRouter, Depends, BackgroundTasks, Request
import pricesmart.modules.reports.constants as report_constants
from pricesmart.modules.data_models.datamodel_models import DownloadExtractRequestModel, DownloadReportsInput
import pricesmart.modules.data_models.controllers.data_model_controller as data_model
from pricesmart.modules.notification.notification_operations import NotificationOperations
import pricesmart.modules.notification.constants as notification_constants
from pricesmart.modules.data_models.datamodel_store.data_models_store import data_models as model_data
from configuration.environment import environment
from fastapi.responses import FileResponse
import pandas as pd
import numpy as np
import xlsxwriter
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from typing import Dict, List
from json import dumps,loads
import logging
import asyncio
import google.oauth2.id_token
import google.auth.transport.requests


router = APIRouter()
query_executor_postgres = QueryExecutorPostgres()

sse_controller = SSEController()


@router.get(report_constants.DOWNLOAD_BACKGROUND_PROCESSED_REPORTS)
async def get_downloaded_report(
    download_report_id: int,
    request: Request
    ):
    try:
        report_path_query = f'''
            with update_notification_status as (
                update {environment.client_schema}.tb_notifications set read_by = {request.state.user_id} , read_at = CURRENT_TIMESTAMP
                where navigate_to = {get_str_repr(str(download_report_id))} and module = {get_str_repr(notification_constants.REPORT_MODULE)}
            )
            select report_path from {environment.client_schema}.downloaded_reports where download_report_id = {download_report_id}
        '''
        res = await async_execute_query(report_path_query)

        report_path = res[0]["report_path"]
        report_name = os.path.basename(report_path)

        return FileResponse(
            path = report_path,
            filename =  report_name
        )
    except IndexError:
        raise Exception("Download Report Id not found in Database")



async def save_download_report_notification(notification_message,report_path: str,user_id: int):
    query = f'''
        INSERT into {environment.client_schema}.downloaded_reports (report_path)
        values ({get_str_repr(report_path)})
        returning download_report_id
    '''
    download_report_id = await async_execute_query(query)
    notification_operations = NotificationOperations()
    notification_data =  await notification_operations.create_notification(
        module = notification_constants.REPORT_MODULE,
        action= notification_constants.DOWNLOAD_ACTION_TYPE,
        navigate_to=download_report_id[0]["download_report_id"],
        user_id = user_id,
        message= notification_message
    )

    return loads(dumps(notification_data.data,default=str))
    



async def process_extract_in_background(user_id: int,download_reports_input: DownloadReportsInput):
    try:
        print("started background task for report")
        report_name = download_reports_input.parameters.get('report_name') if download_reports_input.parameters.get('report_name') else download_reports_input.report_name
        objCommonResponseDataModel = await data_model.get_data_using_query(download_reports_input)
        
        download_report_name = get_report_name(report_name)

        sheet_wise_data = get_sheet_wise_data(download_reports_input.id,objCommonResponseDataModel)
        file_complete_path = create_user_xlsx_workbook(download_report_name,user_id)
        report_path = generate_combined_excel_file(file_complete_path, sheet_wise_data)
        notification_message = f"{report_name} is ready for download"
        notification_data = await save_download_report_notification(notification_message,report_path,user_id)
        print("saved file locally")
        notification_data["status"] = 200 # needed in frontend
        sse_controller.send_user_notification(
            notification_data,
            user_id
        )



    except Exception :
        logging.exception("background report processing failed")
        sse_controller.send_user_notification(
            {
                "message": "Something went wrong while processing the report",
                "user_id":user_id,
                "module": notification_constants.ERROR_MODULE,
                "status": 500
            },
            user_id
        )

        notification_operations = NotificationOperations()
        await notification_operations.create_notification(
            module = notification_constants.ERROR_MODULE,
            message= "Something went wrong while processing the report",
            action= notification_constants.ERROR_ACTION_TYPE,
            navigate_to=None,
            user_id = user_id
        )
        

async def call_report_generator_cloud_function(model_ids: List,parameters: Dict,user_id: int):
    creds, _ = google.auth.default()

    if not creds.valid or creds.token is None:
        auth_req = google.auth.transport.requests.Request()
        creds.refresh(auth_req)
    
    token = creds.token

    request = {
        "user_id": user_id,
        "parameters": parameters,
        "id": model_ids
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            url=environment.REPORT_GENERATOR_API,
            json=request,
            headers={
                "Authorization" : f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
    print(response.status_code)
    print(response.content)

@router.post(report_constants.SAP_EXTRACT_DOWNLOAD_URL)
async def download_sap_controller(
    request_input: DownloadExtractRequestModel,
    background_task :BackgroundTasks,
    request: Request
):
    id = str(request_input.id)
    model_id_data = model_data["models"]
    pre_actions = model_id_data[id].get("pre_actions",[])

    model_ids = [request_input.id, *[i["identifier"] for i in pre_actions if i["is_active"] == True]]

    background_task.add_task(
        call_report_generator_cloud_function,
        model_ids,
        request_input.parameters,
        request.state.user_id
    )

    obj =  BaseResponseBody(
        message="Report is being processed in the background",
        status=True
    )
    return obj

    # is_asynchronous_download = request_input.id in (20,22)
    # if is_asynchronous_download:
    #     # for asynchronous report download
    #     background_task.add_task(process_extract_in_background,token_info["user_id"],request_input)
    #     return BaseResponseBody(
    #         message="Report is being processed in the background",
    #         status=True
    #     )
    

    # report_name = request_input.parameters.get('report_name') if request_input.parameters.get('report_name') else request_input.report_name
    # objCommonResponseDataModel = await data_model.get_data_using_query(request_input)
    # download_report_name = get_report_name(report_name)
    
    # if objCommonResponseDataModel.status == OperationStatus.SUCCESS :
    #     objCommonResponseDataModel.message = "SAP data fetched successfully "
    #     objCommonResponseDataModel.exception = CodeException()

    # file_complete_path = create_xlsx_workbook(download_report_name)
    # path_to_excel = generate_combined_excel_file(file_complete_path, {"SAP-Data":objCommonResponseDataModel.data})
    # return FileResponse(
    #     path=path_to_excel,
    #     filename=download_report_name,
    # )


def get_sheet_wise_data(id: int,model_data : Dict) -> Dict:
    '''
        return sheet order along with the data in a dict based on the model id
    '''
    if id == 20:
        # marketing extract
        return { 
            "SKU level" : model_data["pre_action_data"][0],
            "Offer level" : model_data["model_response_data"].data,
            "DAM" : model_data["pre_action_data"][1]
        }
    elif id == 22:
        # signage extract
        return { 
            "SKU level" : model_data["model_response_data"].data,
            "Offer level" : model_data["pre_action_data"][0],
            "Set" : model_data["pre_action_data"][1]
        }
    raise Exception(f"no sheet config for model-id {id}")

def get_report_name(report_name : str = "sap"):
    return report_name + '_' + StaticClass.get_current_date_and_time("%m-%d-%Y-%H-%M-%S")

def fill_data_to_workbook(file_complete_path, data):
    with pd.ExcelWriter(file_complete_path, engine='xlsxwriter') as writer:
        data.to_excel(writer, sheet_name='sheet-1', index=False)
    #writer.save()
    return file_complete_path

def generate_combined_excel_file(file_complete_path, data_dict):
    # Create a new Excel workbook
    workbook = Workbook(write_only=True)
    for index,(sheet_name, data_model) in enumerate(data_dict.items()):
        df = data_model if isinstance(data_model, pd.DataFrame) else data_model.data
        sheet = workbook.create_sheet(sheet_name,index = index)

        # Write the headers for the query
        for r in dataframe_to_rows(df.head(0), index=False, header=True):
            sheet.append(r)
        # Write the data from the query to the Excel workbook
        for r in dataframe_to_rows(df, index=False, header=False):
            sheet.append(r)

    workbook.save(file_complete_path)
    return file_complete_path

