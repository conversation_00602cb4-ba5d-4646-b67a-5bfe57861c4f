import json
from pricesmart_common.staticclass import StaticClass
from enums.Enums import CRUD<PERSON>peration, OperationStatus,DownloadType
from fastapi import APIRouter, Depends, Request
from pricesmart.database.operations.exception_details import CodeException
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pricesmart.database.operations.postgres.query_executor import QueryExecutorPostgres
from pricesmart.modules.data_models.datamodel_models import DownloadReportsInput
from pricesmart.modules.events.events_model import EventMasterGet, EventEditValidate
import pricesmart.modules.events.events_constants as events_constants
import pricesmart.modules.reports.constants as report_constants
from user_management.login.CustomRouteClass import ValidationErrorLoggingRoute
from pricesmart.modules.reports.download_data import convert_to_file
from fastapi.responses import FileResponse
from configuration.environment import environment
import pricesmart.modules.data_models.controllers.data_model_controller as data_model

from decision_dashboard import models as dashboards_models
from decision_dashboard import service as dashboards_service
from pricesmart_common import constants as dashboards_constants
import pandas as pd
from pandas import DataFrame

from promotions import models as promo_models
from promotions import service as promo_service
from pricesmart.modules.events.process_events_request import ProcessEventsRequests

processRequest = ProcessEventsRequests()

router = APIRouter()
query_executor_postgres = QueryExecutorPostgres()


@router.post(report_constants.DOWNLOAD_REPORTS_URL)
async def download_report_controller(
    request_body: DownloadReportsInput
):
    """
    The download_report_controller function is used to download the reports in csv format.
    The function accepts a DownloadReportsInput object as an argument and returns a FileResponse object.
    
    :param request_input: DownloadReportsInput: Pass the input parameters to the function
    :param : Get the data from the database
    :return: A fileresponse object
    :author: Arun Kumar
    """
    # for top10-bottom10 report_name is inside parameters. for other reports it is outside parameters
    report_name = request_body.parameters.get('report_name') if request_body.parameters.get('report_name') else request_body.report_name
    objCommonResponseDataModel = await data_model.get_data_using_query(request_body)
    download_report_name = get_report_name(report_name)
    if objCommonResponseDataModel.status == OperationStatus.SUCCESS :
        objCommonResponseDataModel.message = "Model data fetched successfully "
        objCommonResponseDataModel.exception = CodeException()
    if not report_name.startswith("reporting_metrics"):
        data = objCommonResponseDataModel.data.to_dict('records')
        await add_event_and_promo_hierarchies(data)
        objCommonResponseDataModel.data = pd.DataFrame(data)
    objCommonResponseDataModel.data = dashboards_service.format_report(report_name,objCommonResponseDataModel.data)
    path_to_excel, file_name = await convert_to_file(
    objCommonResponseDataModel.data, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )

def get_report_name(report_name : str):
    return report_name + '_' + StaticClass.get_current_date_and_time("%m-%d-%Y-%H-%M-%S")


async def add_event_and_promo_hierarchies(promo_data):
    if not promo_data:
        return
    
    is_promo_included = "promo_id" in promo_data[0]
    is_event_included = "event_id" in promo_data[0]
    event_ids = {i["event_id"] for i in promo_data} if is_event_included else {}
    promo_ids = {i["promo_id"] for i in promo_data} if is_promo_included else {}
    event_hierachies, promo_hierarchies = await dashboards_service.get_event_and_promo_hierarchies_service(
        event_ids,
        promo_ids
    )
    for i in promo_data:
        if is_event_included:
            i["event_division_ids"] = event_hierachies[i["event_id"]].get("divisions",{}).get("ids",[])
            i["event_department_ids"] = event_hierachies[i["event_id"]].get("departments",{}).get("ids",[])
            i["event_division_names"] = event_hierachies[i["event_id"]].get("divisions",{}).get("names",[])
            i["event_department_names"] = event_hierachies[i["event_id"]].get("departments",{}).get("names",[])
        if is_promo_included:
            i["promo_division_ids"] = promo_hierarchies[i["promo_id"]].get("divisions",{}).get("ids",[])
            i["promo_department_ids"] = promo_hierarchies[i["promo_id"]].get("departments",{}).get("ids",[])
            i["promo_division_names"] = promo_hierarchies[i["promo_id"]].get("divisions",{}).get("names",[])
            i["promo_department_names"] = promo_hierarchies[i["promo_id"]].get("departments",{}).get("names",[])


@router.post(
    path=report_constants.DOWNLOAD_METRICS_URL
)
async def decision_dashboard(
        request_body: dashboards_models.Metrics,
        request : Request
):
    user_id = request.state.user_id
    key = request_body.aggregations.marketing

    request_dict = request_body.dict()
    if "event_id" not in request_dict.get("aggregations",{}).get("marketing",[]):
        request_dict["aggregations"]["marketing"] = ["event_id"] + request_dict["aggregations"]["marketing"]

    promo_data = await dashboards_service.get_all_decision_dashboard_details(request=request_dict, user_id=user_id)
    await add_event_and_promo_hierarchies(promo_data)

    df = DataFrame(promo_data)

    df = dashboards_service.format_report(tuple(key),df)

    download_report_name = get_report_name("metrics")
    path_to_excel, file_name = await convert_to_file(
    df, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )


@router.post(
    path=report_constants.DOWNLOAD_SCENARIO_URL
)
async def scenario_metrics(
        request_body: promo_models.ScenarioMetricsRequest,
        request: Request
):
    user_id = request.state.user_id
    data = await promo_service.scenario_metrics_service(request=request_body.dict(), user_id=user_id , download = True)
    df = DataFrame(data)
    download_report_name = get_report_name("scenario-data")
    path_to_excel, file_name = await convert_to_file(
        df, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )



@router.post(report_constants.GET_EVENT_DOWNLOAD_URL)
async def get_events_controller(
    event_data: EventMasterGet
):
    """
    The get_event_controller function is used to get the event details from the database.
    The function accepts a JSON object as input and returns a JSON object as output.

    :param event_data: EventMaster: Pass the event data to the api_calling_interface function
    :param : Get the event data from the database
    :return: The event data from the database
    :author: Arun Kumar
    """
    event_incoming_data = processRequest.prepare_data(event_data, CRUDOperation.GET, -1)
    objCommonResponseDataModel = await processRequest.api_calling_interface(event_incoming_data)
    data = objCommonResponseDataModel.data.to_dict('records')
    await add_event_and_promo_hierarchies(data)
    df = DataFrame(data)
    df = dashboards_service.format_report("get-events",df=df)
    download_report_name = get_report_name("event-data")
    path_to_excel, file_name = await convert_to_file(
        df, DownloadType.XLSX, download_report_name
        )
    return FileResponse(
            path=path_to_excel,
            filename=file_name,
        )


@router.post(
    report_constants.GET_PROMO_DOWNLOAD_URL
)
async def promotions(
        request_body: promo_models.Promotions,
        request: Request
):
    user_id = request.state.user_id
    promo_data = await promo_service.get_all_promo_details(request=request_body.dict(), user_id=user_id)
    await add_event_and_promo_hierarchies(promo_data)
    
    df = DataFrame(promo_data)

    # flattening ia_projected data in df
    ia_proj_details = df.pop('ia_projected')
    df1 = pd.DataFrame(ia_proj_details.values.tolist())
    df1.columns = 'ia_projected.' + df1.columns

    # flattening ia_finalized data in df
    ia_fin_details = df.pop('ia_finalized')
    df2 = pd.DataFrame(ia_fin_details.values.tolist())
    df2.columns = 'finalized.' + df2.columns

    # combining flattened data with orginal df
    col = df.columns
    df = pd.concat([df[col], df1, df2], axis=1)

    df = dashboards_service.format_report("get-promos",df)
    download_report_name = get_report_name("promo-data")
    path_to_excel, file_name = await convert_to_file(
        df, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )


@router.post(
    path=report_constants.EVENT_LOCK_AUDIT_PROMO_LEVEL
)
async def get_event_change_audit(
    request_body:  promo_models.EventChangeAudit
):
    data = await promo_service.get_event_change_audit_service(request_body)

    report_name = "event-offer-change"
    
    df = pd.DataFrame(data)
    df = dashboards_service.format_report(report_name,df)

    download_report_name = get_report_name(report_name)
    path_to_excel, file_name = await convert_to_file(
            df, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )


@router.post(
    path= report_constants.EVENT_LOCK_AUDIT_SKU_LEVEL
)
async def get_event_change_audit_sku_level(
    request_body: promo_models.EventChangeAudit
):
    data = await promo_service.get_event_change_audit_sku_level_service(request_body)
    report_name = "event-offer-change-sku-level"
    df = pd.DataFrame(data)
    df = dashboards_service.format_report(report_name,df)
    download_report_name = get_report_name(report_name)
    path_to_excel, file_name = await convert_to_file(
            df, DownloadType.XLSX, download_report_name
    )
    
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )


@router.post(events_constants.DOWNLOAD_VALIDATE_EVENT_EDIT_DATA_URL)
async def validate_edit_event(
    request_body: EventEditValidate
):
    event_incoming_data = processRequest.prepare_data(request_body, CRUDOperation.GET, -1)
    objCommonResponseDataModel = await processRequest.api_calling_interface(event_incoming_data)
    data = objCommonResponseDataModel.data.to_dict('records')
    df = DataFrame(data[0]['unadv_blocking_promos'])
    download_report_name = get_report_name("Unadvertised Event edit conflicts")
    path_to_excel, file_name = await convert_to_file(
        df, DownloadType.XLSX, download_report_name
    )
    return FileResponse(
        path=path_to_excel,
        filename=file_name,
    )
