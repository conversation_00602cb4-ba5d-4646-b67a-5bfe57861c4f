import base64
import os
from typing import Any
import pandas as pd
import xlsxwriter
from pricesmart_common.staticclass import StaticClass
import pricesmart.modules.reports.constants as Constants
from enums.Enums import DownloadType


def get_type(value: Any):
    try:
        value = float(value)
        if value.is_integer():
            return int
        return float
    except (ValueError,TypeError):
        return str

def create_excel(workbook: xlsxwriter.Workbook, data_dict):
    sheet = workbook.add_worksheet()
    
    sheet.write_row(row=0,col=0,data=list(data_dict.keys()))  # write headers

    # write data
    for col_index, column_data in enumerate(data_dict.values()):

        for row_index, cell_value in column_data.items():
            cell_format = workbook.add_format()
            data_type = get_type(cell_value)
            if data_type is int:
                cell_format.set_num_format(1) # 1 means number format in excel
                write_func = sheet.write_number
            elif data_type is float:
                cell_format.set_num_format("0.00")
                write_func = sheet.write_number
            else:
                write_func = sheet.write
            write_func(row_index+1,col_index,data_type(cell_value),cell_format)


async def convert_to_file(
    data: pd.DataFrame,
    type_of_download: DownloadType = DownloadType.CSV,
    file_name=None,
):
    """
    The convert_to_csv function converts the dataframe to csv file.
    It takes three parameters:
    data - DataFrame object, type_of_download - string, experiment_id - int.
    The function returns a tuple of two values: path to the exported file and its name.

    :param data=None: Pass the data to the convert_to_csv function
    :param type_of_download=None: Pass the type of download
    :param experiment_id=None: Download the template file
    :param file_name=None: Create a new file name for the download
    :return: The path to the file and the name of the file
    :author: Arun kumar
    """
    dir_to_download = StaticClass.get_current_directory(Constants.ExportsDirectoryKey)
    path_to_excel = os.path.join(dir_to_download,file_name + "." + type_of_download.name.lower())
    data.columns = [i.upper() for i in data.columns]
    if os.path.exists(path_to_excel):
        os.remove(path_to_excel)

    if type_of_download == DownloadType.XLSX:

        workbook = xlsxwriter.Workbook(path_to_excel)
        data_dict = data.to_dict()
        create_excel(workbook,data_dict)
        workbook.close()

    else:
        data.to_csv(path_to_excel, index=False, encoding='utf-8')
    file_name = await encode_string_to_base64(file_name)
    return path_to_excel, file_name


async def encode_string_to_base64(file_name):
    """
    The encode_string_to_base64 function encodes a string to base64.

    :param file_name: Encode the file name into base64 format
    :return: A base64 encoded string
    :author: Arun kumar
    """
    sample_string_bytes = file_name.encode("ascii")
    base64_bytes = base64.b64encode(sample_string_bytes)
    base64_string = base64_bytes.decode("ascii")
    return base64_string

def create_workbook(filename, file_type:DownloadType):
    """
    The create_workbook function creates a workbook object and returns it.
    It also creates the file path to the excel file that will be created in the next step.
    The function takes two parameters: filename, which is a string representing what you want your excel file to be called, and type, which is an enum of type DownloadType.

    :param filename: Name the excel file
    :param file_type:DownloadType: Determine which directory to save the file in
    :return: A tuple of the workbook and the path to that excel file
    :author: Arun kumar
    """

    file_name = f"{filename}.xlsx"
    path_to_excel = StaticClass.get_file_with_path(file_name,file_type.name.lower(),Constants.ExportsDirectoryKey)
    workbook = xlsxwriter.Workbook(path_to_excel)
    return workbook, path_to_excel

def create_xlsx_workbook(filename):
    path_to_excel = StaticClass.get_file_with_path(filename,"xlsx",Constants.ExportsDirectoryKey)
    return path_to_excel


def create_user_xlsx_workbook(filename,user_id):
    user_path = os.path.join(os.curdir,Constants.USER_REPORTS_DIRECTORY,str(user_id))
    if not os.path.exists(user_path):
        os.makedirs(user_path)
    return os.path.join(user_path,f"{filename}.xlsx")
    