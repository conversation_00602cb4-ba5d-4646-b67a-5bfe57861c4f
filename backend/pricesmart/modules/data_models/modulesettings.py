import app.main as server
import pricesmart.modules.data_models.constants as model_constants
import pricesmart_common.constants as global_constants
from configuration.environment import environment
from fastapi import APIRouter
from logger.logger import logger
from pricesmart.modules.data_models.controllers import (
    data_model_controller,
    entity_update_controller,
)

server.app.include_router(
    data_model_controller.router,
    prefix=f"{global_constants.API_PREFIX}",
    tags=[model_constants.MODULE_NAME],
)

server.app.include_router(
    entity_update_controller.router,
    prefix=f"{global_constants.API_PREFIX}",
    tags=[model_constants.MODULE_NAME],
)
logger.info(f" {environment.LOG_TITLE}: {model_constants.MODULE_NAME}")
