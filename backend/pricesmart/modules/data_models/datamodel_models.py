from typing import List, Optional
from pydantic import BaseModel
from enums.Enums import QueryType

class ParameterValidation(BaseModel):
    expression : str
    message : str


class ModelParameters(BaseModel):
    name:str
    data_type : str
    formate : str
    default : str
    validation : ParameterValidation


class ModelActions(BaseModel):
    type:str
    is_active: bool
    identifier : str
    pass_parameters : bool
    output_parameter : str
    output_type: str
    output_format: str
    output_data_type:str


class DataModel(BaseModel):
    query : str
    pre_actions : List[ModelActions]
    post_actions : List[ModelActions]
    parameters : List[ModelParameters]

class QueryData(BaseModel):
    query : str
    query_type:QueryType = QueryType.SELECT

class DataModelInput(BaseModel):
    id : int
    parameters : Optional[dict]

class DownloadReportsInput(BaseModel):
    id : int
    report_name : Optional[str]
    parameters : Optional[dict]


class DownloadExtractRequestModel(BaseModel):
    id : str
    report_name : Optional[str]
    parameters : Optional[dict]




