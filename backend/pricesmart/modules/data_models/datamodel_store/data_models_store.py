data_models = {
    "models": {
        "1": {
            "pre_actions": [{
                "type": "model",
                "is_active": True,
                "identifier": "2",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "query": "1",
            "parameters":
                [
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "2": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": """4  if len(parameter:product_h4) > 0 else 3 if len(parameter:product_h3) >0 else 2 if len(parameter:product_h2) > 0 else 0""",
            "parameters":
                [
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": """default_start_date""",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": """default_end_date""",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in a the proper date format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "3": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "promo_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": """5  if parameter:aggregation == "weekly" else 6 if parameter:aggregation =="monthly" else  7 if parameter:aggregation =="quarterly" else 19 """,
            "parameters":
                [
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                    ,
                    {
                        "name": "aggregation",
                        "data_type": "str",
                        "formate": "",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        }
        ,
        "4": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": """8  if parameter:aggregation == "weekly" else 9 if parameter:aggregation =="monthly" else  10 if parameter:aggregation =="quarterly" else 11 """,
            "parameters":
                [
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "aggregation",
                        "data_type": "str",
                        "formate": "",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        },
        "5": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "12",
            "parameters":
                [
                    {
                        "name": "data",
                        "data_type": "record",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        }
        ,
        "6": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "13",
            "parameters":
                [
                    {
                        "name": "data",
                        "data_type": "record",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        }
        ,
        "7": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "14",
            "parameters":
                [
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        }
        ,
        "8": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "15",
            "parameters":
                [
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "9": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "20",
            "parameters":
                [
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "10": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "21",
            "parameters":
                [
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "11": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "22",
            "parameters":
                [
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        },
        "12": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "23",
            "parameters":
                [
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                    ,
                    {
                        "name": "aggregation",
                        "data_type": "str",
                        "formate": "",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        },
        "13": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "24",
            "parameters":
                [ 
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                    ,
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        },
        "14": {
            "pre_actions": [{
                "type": "model",
                "is_active": False,
                "identifier": "3",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "ARRAY",
                "output_format": "list",
                "output_data_type": "int"
            }],
            "post_actions": [{
                "type": "python",
                "is_active": False,
                "identifier": "pricesmart.modules.custom_functions.process_input",
                "pass_parameters": True,
                "output_parameter": "event_id",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "str"
            }],
            "query": "25",
            "parameters":
                [
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "15": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": """26  if parameter:report_name == "top_offers" else 27 """,
            "parameters":
                [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    }
                ]
        },
        "16": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "28",
            "parameters":
                [
                    {
                        "name": "event_ad_type",
                        "data_type": "bool",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "offer_types",
                        "data_type": "bool",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_channel",
                        "data_type": "bool",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "17": {
                "pre_actions": [{
                    "type": "model",
                    "is_active": False,
                    "identifier": "3",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "ARRAY",
                    "output_format": "list",
                    "output_data_type": "int"
                }],
                "post_actions": [{
                    "type": "python",
                    "is_active": False,
                    "identifier": "pricesmart.modules.custom_functions.process_input",
                    "pass_parameters": True,
                    "output_parameter": "event_id",
                    "output_type": "dataframe",
                    "output_format": "list",
                    "output_data_type": "str"
                }],
            "query": "29",
            "parameters":
                [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "18": {
            "pre_actions": [],
            "post_actions": [],
        "query": "30",
        "parameters":
            [
                {
                    "name": "report_name",
                    "data_type": "str",
                    "formate": "str",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h1",
                    "data_type": "str",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h2",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h3",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h4",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "event_id",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "validate_date(startDate)",
                        "message": "provided parameter value must be numeric"
                    }
                },
                {
                    "name": "promo_id",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "validate_date(startDate)",
                        "message": "provided parameter value must be numeric"
                    }
                },
                {
                    "name": "start_date",
                    "data_type": "date",
                    "formate": "MM/DD/YYYY",
                    "default": "default_start_date",
                    "validation": {
                        "expression": "(start_date)",
                        "message": "provided parameter must be numeric"
                    }
                },
                {
                    "name": "end_date",
                    "data_type": "date",
                    "formate": "MM/DD/YYYY",
                    "default": "default_end_date",
                    "validation": {
                        "expression": "(end_date)",
                        "message": "provided parameter must be in the proper format"
                    }
                }
            ]
        },
        "19": {
            "pre_actions": [],
            "post_actions": [],
        "query": "31",
        "parameters":
            [
                {
                    "name": "report_name",
                    "data_type": "str",
                    "formate": "str",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h1",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h2",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h3",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "product_h4",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "A",
                        "message": "provided parameter must be in the proper format"
                    }
                },
                {
                    "name": "event_id",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "validate_date(startDate)",
                        "message": "provided parameter value must be numeric"
                    }
                },
                {
                    "name": "promo_id",
                    "data_type": "int",
                    "formate": "list",
                    "default": "",
                    "validation": {
                        "expression": "validate_date(startDate)",
                        "message": "provided parameter value must be numeric"
                    }
                },
                {
                    "name": "start_date",
                    "data_type": "date",
                    "formate": "MM/DD/YYYY",
                    "default": "default_start_date",
                    "validation": {
                        "expression": "(start_date)",
                        "message": "provided parameter must be numeric"
                    }
                },
                {
                    "name": "end_date",
                    "data_type": "date",
                    "formate": "MM/DD/YYYY",
                    "default": "default_end_date",
                    "validation": {
                        "expression": "(end_date)",
                        "message": "provided parameter must be in the proper format"
                    }
                }
            ]
        },
        "20": {
            "pre_actions": [{
                "type": "model",
                "is_active": True,
                "identifier": "18",
                "pass_parameters": True,
                "output_parameter": "",
                "output_type": "dataframe",
                "output_format": "list",
                "output_data_type": "int"
            },
            {
                "type": "model",
                "is_active": True,
                "identifier": "21",
                "pass_parameters": True,
                "output_parameter": "",
                "output_type" : "dataframe",
                "output_format": "list",
                "output_data_type": "int"
            }
            ],
            "post_actions": [],
            "query": "31",
            "parameters":
                [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "21": {
            "pre_actions": [],
            "post_actions": [],
            "query": "32",
            "parameters": [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "22":{
            "pre_actions":[
                {
                    "type": "model",
                    "is_active": True,
                    "identifier": "23",
                    "pass_parameters": True,
                    "output_parameter": "",
                    "output_type" : "dataframe",
                    "output_format": "list",
                    "output_data_type": "int"
                },
                {
                    "type": "model",
                    "is_active": True,
                    "identifier": "24",
                    "pass_parameters": True,
                    "output_parameter": "",
                    "output_type" : "dataframe",
                    "output_format": "list",
                    "output_data_type": "int"
                }
            ],
            "post_actions":[],
            "query": "33",
            "parameters": [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "23":{
            "pre_actions":[],
            "post_actions":[],
            "query": "34",
            "parameters": [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "24": {
            "pre_actions":[],
            "post_actions":[],
            "query":"35",
            "parameters":[
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "25" : {
            "pre_actions": [],
            "post_actions": [],
            "query": "36",
            "parameters": [
                    {
                        "name": "report_name",
                        "data_type": "str",
                        "formate": "str",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h1",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h2",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h3",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "product_h4",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "A",
                            "message": "provided parameter must be in the proper format"
                        }
                    },
                    {
                        "name": "event_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "promo_id",
                        "data_type": "int",
                        "formate": "list",
                        "default": "",
                        "validation": {
                            "expression": "validate_date(startDate)",
                            "message": "provided parameter value must be numeric"
                        }
                    },
                    {
                        "name": "start_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_start_date",
                        "validation": {
                            "expression": "(start_date)",
                            "message": "provided parameter must be numeric"
                        }
                    },
                    {
                        "name": "end_date",
                        "data_type": "date",
                        "formate": "MM/DD/YYYY",
                        "default": "default_end_date",
                        "validation": {
                            "expression": "(end_date)",
                            "message": "provided parameter must be in the proper format"
                        }
                    }
                ]
        },
        "26" : { # for report cloud function
            "pre_actions": [],
            "post_actions": [],
            "query": "",
            "parameters": []
        },
    }

}
