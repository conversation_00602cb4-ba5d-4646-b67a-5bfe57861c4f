query_store= {
    "1" :{
        "query_type":"select",
        "query": """    select 
                            jsonb_build_object(
                                'gross_margin',ARRAY[jsonb_build_object('target',gross_margin_target),
                                        jsonb_build_object('finalized',finalized_margin),
                                        jsonb_build_object('finalized_baseline',finalized_baseline_margin),
                                        jsonb_build_object('finalized_incremental',finalized_incremental_margin),
                                        jsonb_build_object('projected',projected_margin),
                                        jsonb_build_object('projected_baseline',projected_baseline_margin),
                                        jsonb_build_object('projected_incremental',projected_incremental_margin),
                                        jsonb_build_object('actuals',actuals_margin),
                                        jsonb_build_object('actuals_baseline',actuals_baseline_margin),
                                        jsonb_build_object('actuals_incremental',actuals_incremental_margin) ],
                                'revenue', ARRAY[jsonb_build_object('target',revenue_target),
                                        jsonb_build_object('finalized',finalized_revenue),
                                        jsonb_build_object('finalized_baseline',finalized_baseline_revenue),
                                        jsonb_build_object('finalized_incremental',finalized_incremental_revenue),
                                        jsonb_build_object('projected',projected_revenue),
                                        jsonb_build_object('projected_baseline',projected_baseline_revenue),
                                        jsonb_build_object('projected_incremental',projected_incremental_revenue),
                                        jsonb_build_object('actuals',actuals_revenue),
                                        jsonb_build_object('actuals_baseline',actuals_baseline_revenue),
                                        jsonb_build_object('actuals_incremental',actuals_incremental_revenue) ] ,
                                'sales_units', ARRAY[jsonb_build_object('target',units_target),
                                        jsonb_build_object('finalized',finalized_sales_units),
                                        jsonb_build_object('finalized_baseline',finalized_baseline_sales_units),
                                        jsonb_build_object('finalized_incremental',finalized_incremental_sales_units),
                                        jsonb_build_object('projected',projected_sales_units),
                                        jsonb_build_object('projected_baseline',projected_baseline_sales_units),
                                        jsonb_build_object('projected_incremental',projected_incremental_sales_units),
                                        jsonb_build_object('actuals',actuals_sales_units),
                                        jsonb_build_object('actuals_baseline',actuals_baseline_sales_units),
                                        jsonb_build_object('actuals_incremental',actuals_incremental_sales_units) ] 
                            ) as metrics
                        from (
                                Select  
                                    sum(revenue_target) as revenue_target , 
                                    sum(gross_margin_target) as gross_margin_target, 
                                    sum(units_target) as units_target,
                                    SUM(A.sales_units) as finalized_sales_units,
                                    SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                    SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                    SUM(A.margin) as finalized_margin,
                                    SUM(A.baseline_margin) as finalized_baseline_margin,
                                    SUM(A.incremental_margin) as finalized_incremental_margin,
                                    SUM(A.revenue) as finalized_revenue,
                                    SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                    SUM(A.incremental_revenue) as finalized_incremental_revenue
                                from 
                                    simulation.ps_recommended_finalized A 
                                left join 
                                    public.ps_rules B on A.Promo_id=B.Promo_id
                                where 
                                    A.promo_id in ({?promo_id}) 
                                    and A.event_id in ({?event_id}) 
                            ) finalized , 
                            (
                                Select 
                                    SUM(A.sales_units) as projected_sales_units,
                                    SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                    SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                    SUM(A.margin) as projected_margin,
                                    SUM(A.baseline_margin) as projected_baseline_margin,
                                    SUM(A.incremental_margin) as projected_incremental_margin,
                                    SUM(A.revenue) as projected_revenue,
                                    SUM(A.baseline_revenue) as projected_baseline_revenue,
                                    SUM(A.incremental_revenue) as projected_incremental_revenue
                                from 
                                    simulation.ps_recommended_ia_projected A 
                                where 
                                    promo_id in ({?promo_id})
                                    and A.event_id in ({?event_id})
                            ) projected ,
                            (
                                Select 
                                    sum(revenue_target) as actuals_revenue_target , 
                                    sum(gross_margin_target) as actuals_gross_margin_target, 
                                    sum(units_target) as actuals_units_target,
                                    SUM(A.sales_units) as actuals_sales_units,
                                    SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                    SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                    SUM(A.margin) as actuals_margin,
                                    SUM(A.baseline_margin) as actuals_baseline_margin,
                                    SUM(A.incremental_margin) as actuals_incremental_margin,
                                    SUM(A.revenue) as actuals_revenue,
                                    SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                    SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                    max(C.event_txn) as event_txn ,
                                    max(C.event_units_per_txn) as event_units_per_txn,
                                    max (C.event_avg_basket_size) as event_avg_basket_size
                                from 
                                    simulation.ps_recommended_actuals A 
                                left join 
                                    public.ps_rules B on A.Promo_id=B.Promo_id
                                left join 
                                    simulation.tb_event_basketdetails C on A.event_id = C.event_id
                                where 
                                    A.promo_id in ({?promo_id})
                                    and A.event_id in ({?event_id})
                            ) actuals  """
    },
    "4":{
        "query_type":"select",
        "query" :""" SELECT A.promo_id
                                   FROM promo_hierarchy A ,public.promo_master C
                                   WHERE A.hierarchy_level::text = 'product_h4'::text
                                   AND A.hierarchy_type::text = 'product_hierarchy'::text
                                   and A.hierarchy_value in ({?product_h4}) and 
                                   A.Promo_id =C.promo_id
                                   and C.status = 4
                                   and C.start_date >={?start_date} and C.end_date <={?end_date}
                                   and C.event_id in ({?event_id})
                                   group by 1
                            """},
    "3":{
        "query_type":"select",
        "query" :""" SELECT A.promo_id
                                   FROM promo_hierarchy A ,public.promo_master C
                                   WHERE A.hierarchy_level::text = 'product_h3'::text
                                   AND A.hierarchy_type::text = 'product_hierarchy'::text
                                   and A.hierarchy_value in ({?product_h3}) and 
                                   A.Promo_id =C.promo_id
                                   and C.status = 4
                                   and C.start_date >={?start_date} and C.end_date <={?end_date}
                                   and C.event_id in ({?event_id})
                                   group by 1
                            """},
    "2":{
        "query_type":"select",
        "query" :""" SELECT A.promo_id
                                   FROM promo_hierarchy A ,public.promo_master C
                                   WHERE A.hierarchy_level::text = 'product_h2'::text
                                   AND A.hierarchy_type::text = 'product_hierarchy'::text
                                   and A.hierarchy_value in ({?product_h2}) and 
                                   A.Promo_id =C.promo_id
                                   and C.status = 4
                                   and C.start_date >={?start_date} and C.end_date <={?end_date}
                                   and C.event_id in ({?event_id})
                                   group by 1
                        union
                        select 0
                            """},
    "5":{
        "query_type":"select",
        "query":""" $$$ select  data.fiscal_week, data.fiscal_month,data.fiscal_year,
                                  jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',round(gross_margin_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_margin::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_margin::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_margin::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_margin::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_margin::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_margin::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_margin::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_margin::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_margin::decimal,2))], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',round(revenue_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_revenue::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_revenue::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_revenue::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_revenue::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_revenue::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_revenue::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_revenue::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_revenue::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_revenue::decimal,2))] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',round(units_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_sales_units::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_sales_units::decimal,2))
                                   ] 
                                   ) as metrics
                                          from 
                                         (Select dd.*,
                                         actuals_margin, 
                                            actuals_baseline_margin, 
                                            actuals_incremental_margin ,
                                            actuals_revenue, 
                                            actuals_baseline_revenue, 
                                            actuals_incremental_revenue,
                                            actuals_sales_units, 
                                            actuals_baseline_sales_units, 
                                            actuals_incremental_sales_units
                                         from (
                                          (Select A.fiscal_week, A.fiscal_month,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_finalized A
                                            left join public.promo_master P on P.promo_id = A.promo_id, 
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by A.fiscal_week, A.fiscal_month,A.fiscal_year
                                          ) finalized left join 
                                          (Select 
						A.fiscal_week as projected_fiscal_week , A.fiscal_month as projected_fiscal_month ,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_ia_projected A
                                                left join public.promo_master P on P.promo_id = A.promo_id,
												public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates )  A 
										        group by A.fiscal_week, A.fiscal_month,A.fiscal_year ) projected  
												on (projected.projected_fiscal_week = finalized.fiscal_week 
												and  projected.projected_fiscal_month = finalized.fiscal_month 
												and projected.projected_fiscal_year  = finalized.fiscal_year)) dd
                                          left join 
                                          (Select A.fiscal_week, A.fiscal_month,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_actuals A
                                            left join public.promo_master P on P.promo_id = A.promo_id ,
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by A.fiscal_week, A.fiscal_month,A.fiscal_year
                                          ) actuals
                                          on (actuals.fiscal_week = dd.fiscal_week and  actuals.fiscal_month = dd.fiscal_month 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                                          order by data.fiscal_week, data.fiscal_month,data.fiscal_year $$$.format(
                                          p1="  Z.product_h4_id in ({?product_h4})" if len(parameter:product_h4) > 0 else "",
                                          p2="  Z.product_h3_id in ({?product_h3})" if len(parameter:product_h3) > 0 else "",
                                          p3="  Z.product_h2_id in ({?product_h2})" if len(parameter:product_h2) > 0 else "",
                                          p4=" and A.promo_id in ({?promo_id})" if len(parameter:promo_id) > 0 else "" ) """
    },
    "6":{
        "query_type":"select",
        "query":""" $$$select data.fiscal_month,data.fiscal_year,
                                  jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',round(gross_margin_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_margin::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_margin::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_margin::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_margin::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_margin::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_margin::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_margin::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_margin::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_margin::decimal,2))], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',round(revenue_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_revenue::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_revenue::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_revenue::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_revenue::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_revenue::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_revenue::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_revenue::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_revenue::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_revenue::decimal,2))] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',round(units_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_sales_units::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_sales_units::decimal,2))] 
                                   ) as metrics
                                          from  
                                          (Select dd.*,
                                          actuals_margin, 
                                            actuals_baseline_margin, 
                                            actuals_incremental_margin ,
                                            actuals_revenue, 
                                            actuals_baseline_revenue, 
                                            actuals_incremental_revenue,
                                            actuals_sales_units, 
                                            actuals_baseline_sales_units, 
                                            actuals_incremental_sales_units
                                          from (
                                                 (Select  A.fiscal_month,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* ,c.fiscal_month,c.fiscal_year from simulation.ps_recommended_finalized A
                                            left join public.promo_master P on P.promo_id = A.promo_id, 
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_month,A.fiscal_year
                                          ) finalized left join 
                                          (Select 
						A.fiscal_month as projected_fiscal_month ,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , c.fiscal_month,c.fiscal_year from simulation.ps_recommended_ia_projected A
                                                left join public.promo_master P on P.promo_id = A.promo_id, 
												public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates )  A 
										        group by A.fiscal_month,A.fiscal_year ) projected 
												on ( projected.projected_fiscal_month = finalized.fiscal_month 
												and projected.projected_fiscal_year  = finalized.fiscal_year)) dd
                                          left join 
                                          (Select  A.fiscal_month,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget
                                          from (Select A.* ,c.fiscal_month,c.fiscal_year from simulation.ps_recommended_actuals A
                                            left join public.promo_master P on P.promo_id = A.promo_id,
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_month,A.fiscal_year
                                          ) actuals
                                          on ( actuals.fiscal_month = dd.fiscal_month 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                                          order by data.fiscal_month,data.fiscal_year $$$.format(
                                          p1="  Z.product_h4_id in ({?product_h4})" if len(parameter:product_h4) > 0 else "",
                                          p2="  Z.product_h3_id in ({?product_h3})" if len(parameter:product_h3) > 0 else "",
                                          p3="  Z.product_h2_id in ({?product_h2})" if len(parameter:product_h2) > 0 else "",
                                          p4=" and A.promo_id in ({?promo_id})" if len(parameter:promo_id) > 0 else "" ) """
    },
    "7":{
        "query_type":"select",
        "query":""" $$$ select  data.fiscal_quarter,data.fiscal_year,
                                   jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',round(gross_margin_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_margin::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_margin::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_margin::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_margin::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_margin::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_margin::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_margin::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_margin::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_margin::decimal,2))], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',round(revenue_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_revenue::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_revenue::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_revenue::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_revenue::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_revenue::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_revenue::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_revenue::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_revenue::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_revenue::decimal,2))] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',round(units_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_markdown_budget',round(finalized_markdown_budget::decimal,2)),
                                   jsonb_build_object('projected',round(projected_sales_units::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_sales_units::decimal,2))] 
                                   ) as metrics
                                          from  
                                          (Select dd.*,
                                          actuals_margin, 
                                            actuals_baseline_margin, 
                                            actuals_incremental_margin ,
                                            actuals_revenue, 
                                            actuals_baseline_revenue, 
                                            actuals_incremental_revenue,
                                            actuals_sales_units, 
                                            actuals_baseline_sales_units, 
                                            actuals_incremental_sales_units
                                          from (
                                                 (Select A.fiscal_quarter,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* ,c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_finalized A
                                            left join public.promo_master P on P.promo_id = A.promo_id,  
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_quarter,A.fiscal_year
                                          ) finalized left join 
                                          (Select 
						A.fiscal_quarter as projected_fiscal_quarter ,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_ia_projected A
                                                left join public.promo_master P on P.promo_id = A.promo_id,  
												public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates )  A 
										        group by A.fiscal_quarter,A.fiscal_year ) projected 
												on ( projected.projected_fiscal_quarter = finalized.fiscal_quarter 
												and projected.projected_fiscal_year  = finalized.fiscal_year)) dd
                                                left join 
                                          (Select A.fiscal_quarter,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget
                                          from (Select A.* ,c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_actuals A
                                            left join public.promo_master P on P.promo_id = A.promo_id ,
											public.fiscal_date_mapping C , (Select product_h5_id as item_id from public.product_master Z
                                                                             where {p1} {p2} {p3}
                                                                             ) X
											where x.item_id = A.item_id and P.status = 4 and A.event_id in ({?event_id}) {p4} and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_quarter,A.fiscal_year
                                          ) actuals
                                          on ( actuals.fiscal_quarter = dd.fiscal_quarter 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                                          order by data.fiscal_quarter,data.fiscal_year $$$.format(
                                          p1="  Z.product_h4_id in ({?product_h4})" if len(parameter:product_h4) > 0 else "",
                                          p2="  Z.product_h3_id in ({?product_h3})" if len(parameter:product_h3) > 0 else "",
                                          p3="  Z.product_h2_id in ({?product_h2})" if len(parameter:product_h2) > 0 else "",
                                          p4=" and A.promo_id in ({?promo_id})" if len(parameter:promo_id) > 0 else "" ) """
    },
    "8":{
        "query_type":"select",
        "query":""" select data.fiscal_week, data.fiscal_month,data.fiscal_year,
                                  jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',gross_margin_target),
                                   jsonb_build_object('finalized',finalized_margin),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_margin),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_margin),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_margin),
                                   jsonb_build_object('projected_baseline',projected_baseline_margin),
                                   jsonb_build_object('projected_incremental',projected_incremental_margin),
                                   jsonb_build_object('actuals',actuals_margin),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_margin),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_margin)], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',revenue_target),
                                   jsonb_build_object('finalized',finalized_revenue),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_revenue),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_revenue),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_revenue),
                                   jsonb_build_object('projected_baseline',projected_baseline_revenue),
                                   jsonb_build_object('projected_incremental',projected_incremental_revenue),
                                   jsonb_build_object('actuals',actuals_revenue),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_revenue),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_revenue)] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',units_target),
                                   jsonb_build_object('finalized',finalized_sales_units),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_sales_units),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_sales_units),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_sales_units),
                                   jsonb_build_object('projected_baseline',projected_baseline_sales_units),
                                   jsonb_build_object('projected_incremental',projected_incremental_sales_units),
                                   jsonb_build_object('actuals',actuals_sales_units),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_sales_units),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_sales_units)] 
                                   ) as metrics
                                          from  
                                          (Select dd.*,
                                          actuals_margin, 
                                            actuals_baseline_margin, 
                                            actuals_incremental_margin ,
                                            actuals_revenue, 
                                            actuals_baseline_revenue, 
                                            actuals_incremental_revenue,
                                            actuals_sales_units, 
                                            actuals_baseline_sales_units, 
                                            actuals_incremental_sales_units
                                          from (
                                                 (Select A.fiscal_week, A.fiscal_month,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_finalized A, 
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by A.fiscal_week, A.fiscal_month,A.fiscal_year
                                          ) finalized left join 
                                          (Select  
						A.fiscal_week as projected_fiscal_week, A.fiscal_month as projected_fiscal_month,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_ia_projected A, 
												public.fiscal_date_mapping C
												where A.event_id in ({?event_id}) and A.recommendation_date = C.dates )  A 
										        group by A.fiscal_week, A.fiscal_month,A.fiscal_year ) projected 
												on (projected.projected_fiscal_week = finalized.fiscal_week 
												and  projected.projected_fiscal_month = finalized.fiscal_month 
												and projected.projected_fiscal_year  = finalized.fiscal_year)) dd
                                          left join 
                                          (Select A.fiscal_week, A.fiscal_month,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget
                                          from (Select A.* , C.fiscal_week, c.fiscal_month,c.fiscal_year from simulation.ps_recommended_actuals A ,
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by A.fiscal_week, A.fiscal_month,A.fiscal_year
                                          ) actuals
                                          on (actuals.fiscal_week = dd.fiscal_week 
												and  actuals.fiscal_month = dd.fiscal_month 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                                          order by data.fiscal_week, data.fiscal_month,data.fiscal_year
                     """},
    "9":{
        "query_type":"select",
        "query":""" select data.fiscal_month,data.fiscal_year,
                                   jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',gross_margin_target),
                                   jsonb_build_object('finalized',finalized_margin),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_margin),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_margin),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_margin),
                                   jsonb_build_object('projected_baseline',projected_baseline_margin),
                                   jsonb_build_object('projected_incremental',projected_incremental_margin),
                                   jsonb_build_object('actuals',actuals_margin),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_margin),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_margin)], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',revenue_target),
                                   jsonb_build_object('finalized',finalized_revenue),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_revenue),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_revenue),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_revenue),
                                   jsonb_build_object('projected_baseline',projected_baseline_revenue),
                                   jsonb_build_object('projected_incremental',projected_incremental_revenue),
                                   jsonb_build_object('actuals',actuals_revenue),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_revenue),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_revenue)] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',units_target),
                                   jsonb_build_object('finalized',finalized_sales_units),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_sales_units),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_sales_units),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_sales_units),
                                   jsonb_build_object('projected_baseline',projected_baseline_sales_units),
                                   jsonb_build_object('projected_incremental',projected_incremental_sales_units),
                                   jsonb_build_object('actuals',actuals_sales_units),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_sales_units),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_sales_units)] 
                                   ) as metrics
                                          from  
                                          (Select dd.*,
                                          actuals_margin, 
                                            actuals_baseline_margin, 
                                            actuals_incremental_margin ,
                                            actuals_revenue, 
                                            actuals_baseline_revenue, 
                                            actuals_incremental_revenue,
                                            actuals_sales_units, 
                                            actuals_baseline_sales_units, 
                                            actuals_incremental_sales_units
                                          from (
                                                 (Select  A.fiscal_month,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* ,c.fiscal_month,c.fiscal_year from simulation.ps_recommended_finalized A, 
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_month,A.fiscal_year
                                          ) finalized left join 
                                          (Select 
						A.fiscal_month as projected_fiscal_month,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , c.fiscal_month,c.fiscal_year from simulation.ps_recommended_ia_projected A, 
												public.fiscal_date_mapping C
												where A.event_id in ({?event_id}) and A.recommendation_date = C.dates )  A 
										        group by A.fiscal_month,A.fiscal_year ) projected 
												on ( projected.projected_fiscal_month = finalized.fiscal_month 
												and projected.projected_fiscal_year  = finalized.fiscal_year))dd
                                          left join 
                                          (Select  A.fiscal_month,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget 
                                          from (Select A.* ,c.fiscal_month,c.fiscal_year from simulation.ps_recommended_actuals A,
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_month,A.fiscal_year
                                          ) actuals 
                                          on ( actuals.fiscal_month = dd.fiscal_month 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                     """},
    "10":{
        "query_type":"select",
        "query":""" select  data.fiscal_quarter,data.fiscal_year,
                                   jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',gross_margin_target),
                                   jsonb_build_object('finalized',finalized_margin),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_margin),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_margin),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_margin),
                                   jsonb_build_object('projected_baseline',projected_baseline_margin),
                                   jsonb_build_object('projected_incremental',projected_incremental_margin),
                                   jsonb_build_object('actuals',actuals_margin),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_margin),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_margin)], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',revenue_target),
                                   jsonb_build_object('finalized',finalized_revenue),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_revenue),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_revenue),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_revenue),
                                   jsonb_build_object('projected_baseline',projected_baseline_revenue),
                                   jsonb_build_object('projected_incremental',projected_incremental_revenue),
                                   jsonb_build_object('actuals',actuals_revenue),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_revenue),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_revenue)] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',units_target),
                                   jsonb_build_object('finalized',finalized_sales_units),
                                   jsonb_build_object('finalized_baseline',finalized_baseline_sales_units),
                                   jsonb_build_object('finalized_incremental',finalized_incremental_sales_units),
                                   jsonb_build_object('finalized_markdown_budget',finalized_markdown_budget),
                                   jsonb_build_object('projected',projected_sales_units),
                                   jsonb_build_object('projected_baseline',projected_baseline_sales_units),
                                   jsonb_build_object('projected_incremental',projected_incremental_sales_units),
                                   jsonb_build_object('actuals',actuals_sales_units),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_sales_units),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_sales_units)] 
                                   ) as metrics
                                          from  
                                          (Select dd.*,actuals_margin, 
                                                    actuals_baseline_margin, 
                                                    actuals_incremental_margin ,
                                                    actuals_revenue, 
                                                    actuals_baseline_revenue, 
                                                    actuals_incremental_revenue,
                                                    actuals_sales_units, 
                                                    actuals_baseline_sales_units, 
                                                    actuals_incremental_sales_units from (
                                          (Select A.fiscal_quarter,A.fiscal_year, sum(revenue_target) as revenue_target , 
                                          sum(gross_margin_target) as gross_margin_target, 
                                          sum(units_target) as units_target,
                                          SUM(A.sales_units) as finalized_sales_units,
                                          SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                          SUM(A.margin) as finalized_margin,
                                          SUM(A.baseline_margin) as finalized_baseline_margin,
                                          SUM(A.incremental_margin) as finalized_incremental_margin,
                                          SUM(A.revenue) as finalized_revenue,
                                          SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                          SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                          SUM(B.markdown_budget) as finalized_markdown_budget
                                          from (Select A.* ,c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_finalized A, 
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_quarter,A.fiscal_year
                                          ) finalized left join 
                                          (Select 
						A.fiscal_quarter as projected_fiscal_quarter,A.fiscal_year as projected_fiscal_year,
                                          SUM(A.sales_units) as projected_sales_units,
                                          SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                          SUM(A.margin) as projected_margin,
                                          SUM(A.baseline_margin) as projected_baseline_margin,
                                          SUM(A.incremental_margin) as projected_incremental_margin,
                                          SUM(A.revenue) as projected_revenue,
                                          SUM(A.baseline_revenue) as projected_baseline_revenue,
                                          SUM(A.incremental_revenue) as projected_incremental_revenue
                                          from (Select A.* , c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_ia_projected A, 
												public.fiscal_date_mapping C
												where A.event_id in ({?event_id}) and A.recommendation_date = C.dates )  A 
										        group by  A.fiscal_quarter,A.fiscal_year ) projected 
												on ( projected.projected_fiscal_quarter = finalized.fiscal_quarter 
												and projected.projected_fiscal_year  = finalized.fiscal_year)) dd
                                          left join 
                                          (Select A.fiscal_quarter,A.fiscal_year, sum(revenue_target) as actuals_revenue_target , 
                                          sum(gross_margin_target) as actuals_gross_margin_target, 
                                          sum(units_target) as actuals_units_target,
                                          SUM(A.sales_units) as actuals_sales_units,
                                          SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                          SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                          SUM(A.margin) as actuals_margin,
                                          SUM(A.baseline_margin) as actuals_baseline_margin,
                                          SUM(A.incremental_margin) as actuals_incremental_margin,
                                          SUM(A.revenue) as actuals_revenue,
                                          SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                          SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                          SUM(B.markdown_budget) as actuals_markdown_budget
                                          from (Select A.* ,c.fiscal_quarter,c.fiscal_year from simulation.ps_recommended_actuals A,
											public.fiscal_date_mapping C
											where A.event_id in ({?event_id}) and A.recommendation_date = C.dates 
												) A left join   public.ps_rules B
                                          on (A.Promo_id=B.Promo_id)
										   group by  A.fiscal_quarter,A.fiscal_year
                                          ) actuals
                                          on ( actuals.fiscal_quarter = dd.fiscal_quarter 
												and actuals.fiscal_year  = dd.fiscal_year)) data
                     
                     """},
    "11" :{
        "query_type":"select",
        "query": """ select 
                            jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',gross_margin_target),
                            jsonb_build_object('finalized',finalized_margin),
                            jsonb_build_object('finalized_baseline',finalized_baseline_margin),
                            jsonb_build_object('finalized_incremental',finalized_incremental_margin),
                            jsonb_build_object('projected',projected_margin),
                            jsonb_build_object('projected_baseline',projected_baseline_margin),
                            jsonb_build_object('projected_incremental',projected_incremental_margin),
                            jsonb_build_object('actuals',actuals_margin),
                                   jsonb_build_object('actuals_baseline',actuals_baseline_margin),
                                   jsonb_build_object('actuals_incremental',actuals_incremental_margin)], 
                            'revenue',
                            ARRAY[jsonb_build_object('target',revenue_target),
                            jsonb_build_object('finalized',finalized_revenue),
                            jsonb_build_object('finalized_baseline',finalized_baseline_revenue),
                            jsonb_build_object('finalized_incremental',finalized_incremental_revenue),
                            jsonb_build_object('projected',projected_revenue),
                            jsonb_build_object('projected_baseline',projected_baseline_revenue),
                            jsonb_build_object('projected_incremental',projected_incremental_revenue),
                            jsonb_build_object('actuals',actuals_revenue),
                            jsonb_build_object('actuals_baseline',actuals_baseline_revenue),
                            jsonb_build_object('actuals_incremental',actuals_incremental_revenue)] ,
                            'sales_units',
                            ARRAY[jsonb_build_object('target',units_target),
                            jsonb_build_object('finalized',finalized_sales_units),
                            jsonb_build_object('finalized_baseline',finalized_baseline_sales_units),
                            jsonb_build_object('finalized_incremental',finalized_incremental_sales_units),
                            jsonb_build_object('projected',projected_sales_units),
                            jsonb_build_object('projected_baseline',projected_baseline_sales_units),
                            jsonb_build_object('projected_incremental',projected_incremental_sales_units),
                            jsonb_build_object('actuals',actuals_sales_units),
                            jsonb_build_object('actuals_baseline',actuals_baseline_sales_units),
                            jsonb_build_object('actuals_incremental',actuals_incremental_sales_units)] 
                            ) as metrics
                                   from 
                                   (Select * from
                                          (Select  sum(revenue_target) as revenue_target , 
                                   sum(gross_margin_target) as gross_margin_target, 
                                   sum(units_target) as units_target,
                                   SUM(A.sales_units) as finalized_sales_units,
                                   SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                   SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                   SUM(A.margin) as finalized_margin,
                                   SUM(A.baseline_margin) as finalized_baseline_margin,
                                   SUM(A.incremental_margin) as finalized_incremental_margin,
                                   SUM(A.revenue) as finalized_revenue,
                                   SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                   SUM(A.incremental_revenue) as finalized_incremental_revenue
                                   from simulation.ps_recommended_finalized A left join   public.ps_rules B
                                   on A.Promo_id=B.Promo_id
                                   where A.event_id in ({?event_id})) finalized , 
                                   (Select 
                                   SUM(A.sales_units) as projected_sales_units,
                                   SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                   SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                   SUM(A.margin) as projected_margin,
                                   SUM(A.baseline_margin) as projected_baseline_margin,
                                   SUM(A.incremental_margin) as projected_incremental_margin,
                                   SUM(A.revenue) as projected_revenue,
                                   SUM(A.baseline_revenue) as projected_baseline_revenue,
                                   SUM(A.incremental_revenue) as projected_incremental_revenue
                                   from simulation.ps_recommended_ia_projected  A where event_id in 
                                   ({?event_id})) projected ,
                                   (Select  sum(revenue_target) as actuals_revenue_target , 
                                   sum(gross_margin_target) as actuals_gross_margin_target, 
                                   sum(units_target) as actuals_units_target,
                                   SUM(A.sales_units) as actuals_sales_units,
                                   SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                   SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                   SUM(A.margin) as actuals_margin,
                                   SUM(A.baseline_margin) as actuals_baseline_margin,
                                   SUM(A.incremental_margin) as actuals_incremental_margin,
                                   SUM(A.revenue) as actuals_revenue,
                                   SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                   SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                   max(C.event_txn) as event_txn ,
                                   max(C.event_units_per_txn) as event_units_per_txn,
                                   max (C.event_avg_basket_size) as event_avg_basket_size
                                   from simulation.ps_recommended_actuals A left join   public.ps_rules B
                                   on A.Promo_id=B.Promo_id
                                   left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id
                                   where A.event_id in ({?event_id})) actuals ) foo 
                                                 """
    },
    "12":{
        "query_type":"update",
        "query" : """ update public.promo_master set {updatable_columns}  where 
                                   {condition}
                            """
    }
    ,
    "13":{
        "query_type":"update",
        "query" : """ update public.event_master set {updatable_columns} where 
                                  {condition}
                            """
    }
    ,
    "14":{
        "query_type":"select",
        "query" : """ select A.item_id, A.is_hero ,B.brand, B.product_h1_name,B.product_h2_name,
                                   B.product_h3_name,B.product_h4_name,B.product_h5_name, B.cost, B.price from public.promo_products A 
                                   , public.product_master B where A.item_id = B.product_h5_id
                                   and a.promo_id in ({?promo_id})
                            """
    }
    ,
    "15":{
        "query_type":"select",
        "query" : """ $$$with item_data as (select distinct A.item_id, A.is_hero ,B.brand, B.product_h1_name,B.product_h2_name,
                                   B.product_h3_name,B.product_h4_name,B.product_h5_name, B.cost, B.price from public.promo_products A 
                                   , public.product_master B, public.promo_master C where A.item_id = B.product_h5_id
                                   and a.promo_id = C.promo_id 
                                   {p1} {p2} {p3} {p4} {p5}
                                   and C.status =4 
                                   and c.start_date >= {?start_date} 
                                   and C.end_date <= {?end_date} ),
                                inventory_data as (
                                    select 
                                        item_id, SUM((value::numeric)) AS inventory
                                    FROM 
                                        public.store_level_po_inventory, json_each_text(public.store_level_po_inventory.inventory::json) AS json_data	
                                    WHERE 
                                        key::integer IN (select store_id from public.event_store es where {p6}) 
                                        and item_id in (select item_id from item_data)
                                    group by item_id
                                )
                                select A.*, B.inventory from 
                                item_data A left join inventory_data B on A.item_id = B.item_id $$$.format(
                                   p1=" and c.event_id in ({?event_id})" if len(parameter:event_id) > 0 else "" , 
                                   p2=" and a.promo_id in ({?promo_id})" if len(parameter:promo_id) > 0 else "" ,
                                   p3=" and B.product_h4_id in ({?product_h4})" if len(parameter:product_h4) > 0 else "",
                                   p4=" and B.product_h3_id in ({?product_h3})" if len(parameter:product_h3) > 0 else "",
                                   p5=" and B.product_h2_id in ({?product_h2})" if len(parameter:product_h2) > 0 else "",
                                   p6=" event_id in ({?event_id})" if len(parameter:event_id) > 0 else ""
                                ) """
    },
    "16":{
        "query_type":"select",
        "query" : """ select distinct A.item_id, A.is_hero ,B.brand, B.product_h1_name,B.product_h2_name,
                                   B.product_h3_name,B.product_h4_name,B.product_h5_name, B."cost", B.price from public.promo_products A 
                                   , public.product_master B, public.promo_master C where A.item_id = B.product_h5_id
                                   and a.promo_id = C.promo_id and c.event_id in ({?event_id}) and C.status =4
                                   and B.product_h3_id in ({?product_h3}) and c.start_date >= {?start_date} 
                                   and C.end_date <= {?end_date}
                                   """
    },
    "17":{
        "query_type":"select",
        "query" : """ select distinct A.item_id, A.is_hero ,B.brand, B.product_h1_name,B.product_h2_name,
                                   B.product_h3_name,B.product_h4_name,B.product_h5_name, B."cost", B.price from public.promo_products A 
                                   , public.product_master B, public.promo_master C where A.item_id = B.product_h5_id
                                   and a.promo_id = C.promo_id and c.event_id in ({?event_id}) and C.status =4
                                   and B.product_h4_id in ({?product_h4}) and c.start_date >= {?start_date} 
                                   and C.end_date <= {?end_date}
                                   """
    },
    "18":{
        "query_type":"select",
        "query" : """ select distinct A.item_id, A.is_hero ,B.brand, B.product_h1_name,B.product_h2_name,
                                   B.product_h3_name,B.product_h4_name,B.product_h5_name, B."cost", B.price from public.promo_products A 
                                   , public.product_master B, public.promo_master C where A.item_id = B.product_h5_id
                                   and a.promo_id = C.promo_id and c.event_id in ({?event_id}) and C.status =4
                                   and c.start_date >= {?start_date} 
                                   and C.end_date <= {?end_date}
                                   """
    },
    "19" :{
        "query_type":"select",
        "query": """ $$$
                        with filtered_promo_products as (
                            select item_id from public.promo_master pm 
                            inner join public.promo_products pp using(promo_id)
                            inner join public.product_master Z on Z.product_h5_id = pp.item_id
                            where pm.status = 4 and pm.event_id in ({?event_id}) and {p1} {p2} {p3}
                        ) 
                        select 
                                   jsonb_build_object('gross_margin',ARRAY[jsonb_build_object('target',round(gross_margin_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_margin::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_margin::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_margin::decimal,2)),
                                   jsonb_build_object('projected',round(projected_margin::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_margin::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_margin::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_margin::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_margin::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_margin::decimal,2))
                                   ], 
                                   'revenue',
                                   ARRAY[jsonb_build_object('target',round(revenue_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_revenue::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_revenue::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_revenue::decimal,2)),
                                   jsonb_build_object('projected',round(projected_revenue::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_revenue::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_revenue::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_revenue::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_revenue::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_revenue::decimal,2))
                                   ] ,
                                   'sales_units',
                                   ARRAY[jsonb_build_object('target',round(units_target::decimal,2)),
                                   jsonb_build_object('finalized',round(finalized_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_baseline',round(finalized_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('finalized_incremental',round(finalized_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('projected',round(projected_sales_units::decimal,2)),
                                   jsonb_build_object('projected_baseline',round(projected_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('projected_incremental',round(projected_incremental_sales_units::decimal,2)),
                                   jsonb_build_object('actuals',round(actuals_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_baseline',round(actuals_baseline_sales_units::decimal,2)),
                                   jsonb_build_object('actuals_incremental',round(actuals_incremental_sales_units::decimal,2))
                                   ] ,
                                   'markdown',
                                   ARRAY[
                                    jsonb_build_object('finalized', round(finalized_markdown::decimal,2)),
                                    jsonb_build_object('projected',round(projected_markdown::decimal,2)),
                                    jsonb_build_object('actuals',round(actuals_markdown::decimal,2))
                                   ]
                                   ) as metrics
                                        from 
                                        (Select  sum(revenue_target) as revenue_target , 
                                                sum(gross_margin_target) as gross_margin_target, 
                                                sum(units_target) as units_target,
                                                SUM(A.sales_units) as finalized_sales_units,
                                                SUM(A.baseline_sales_units) as finalized_baseline_sales_units,
                                                SUM(A.incremental_sales_units) as finalized_incremental_sales_units,
                                                SUM(A.margin) as finalized_margin,
                                                SUM(A.baseline_margin) as finalized_baseline_margin,
                                                SUM(A.incremental_margin) as finalized_incremental_margin,
                                                SUM(A.revenue) as finalized_revenue,
                                                SUM(A.baseline_revenue) as finalized_baseline_revenue,
                                                SUM(A.incremental_revenue) as finalized_incremental_revenue,
                                                SUM(A.promo_spend) as finalized_markdown
                                            from simulation.ps_recommended_finalized A 
                                            left join public.promo_master P on p.promo_id = A.promo_id 
                                            left join public.ps_rules B on A.Promo_id=B.Promo_id
                                            where A.item_id in (
                                                select item_id from filtered_promo_products
                                            )
                                            and P.status=4 and A.event_id in ({?event_id}) {p4}) finalized , 
                                        (Select 
                                                SUM(A.sales_units) as projected_sales_units,
                                                SUM(A.baseline_sales_units) as projected_baseline_sales_units,
                                                SUM(A.incremental_sales_units) as projected_incremental_sales_units,
                                                SUM(A.margin) as projected_margin,
                                                SUM(A.baseline_margin) as projected_baseline_margin,
                                                SUM(A.incremental_margin) as projected_incremental_margin,
                                                SUM(A.revenue) as projected_revenue,
                                                SUM(A.baseline_revenue) as projected_baseline_revenue,
                                                SUM(A.incremental_revenue) as projected_incremental_revenue,
                                                SUM(A.promo_spend) as projected_markdown
                                            from simulation.ps_recommended_ia_projected  A 
                                            left join public.promo_master P on p.promo_id = A.promo_id 
                                            where A.item_id in (
                                                select item_id from filtered_promo_products
                                            )
                                            and P.status=4 and A.event_id in ({?event_id}) {p4}) projected ,
                                        (Select  sum(revenue_target) as actuals_revenue_target , 
                                            sum(gross_margin_target) as actuals_gross_margin_target, 
                                            sum(units_target) as actuals_units_target,
                                            SUM(A.sales_units) as actuals_sales_units,
                                            SUM(A.baseline_sales_units) as actuals_baseline_sales_units,
                                            SUM(A.incremental_sales_units) as actuals_incremental_sales_units,
                                            SUM(A.margin) as actuals_margin,
                                            SUM(A.baseline_margin) as actuals_baseline_margin,
                                            SUM(A.incremental_margin) as actuals_incremental_margin,
                                            SUM(A.revenue) as actuals_revenue,
                                            SUM(A.baseline_revenue) as actuals_baseline_revenue,
                                            SUM(A.incremental_revenue) as actuals_incremental_revenue,
                                            SUM(A.promo_spend) as actuals_markdown,
                                            max(q.event_txn) as event_txn ,
                                            max(q.event_units_per_txn) as event_units_per_txn,
                                            max (q.event_avg_basket_size) as event_avg_basket_size
                                            from simulation.ps_recommended_actuals A 
                                            left join public.promo_master P on p.promo_id = A.promo_id 
                                            left join public.ps_rules B on A.Promo_id=B.Promo_id 
                                            left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id
                                            where A.item_id in (
                                                select item_id from filtered_promo_products
                                            )
                                            and  P.status=4 and A.event_id in ({?event_id}) {p4}) actuals $$$.format(
                                                        p1="  Z.product_h4_id in ({?product_h4})" if len(parameter:product_h4) > 0 else "",
                                                        p2="  Z.product_h3_id in ({?product_h3})" if len(parameter:product_h3) > 0 else "",
                                                        p3="  Z.product_h2_id in ({?product_h2})" if len(parameter:product_h2) > 0 else "",
                                                        p4=" and A.promo_id in ({?promo_id})" if len(parameter:promo_id) > 0 else "" ) """
    },
    "20" :{
        "query_type":"select",
        "query":  """ $$$with events as (
                                          select A.name , A.event_id , A.start_date, A.end_date,A.channel_type , A.ad_type  from public.event_master A , public.event_hierarchy B where 
                                          A.event_id = B.event_id  and A.is_deleted = 0
                                          {p1} {p2} {p3} {p4} {p8}
                                          group by 1, 2,3,4,5,6 order by A.start_date desc ),
                                          promos as 
                                          (select A.event_id, A.name , A.promo_id , A.start_date, A.end_date, C.discount_level  
                                          from 
                                          public.promo_master A 
                                          left join public.ps_rules C on A.promo_id = C.promo_id
                                          left join public.promo_products D on A.promo_id = D.promo_id
                                          left join public.product_master Q on D.item_id = Q.product_h5_id
                                          where 
                                          A.status = 4
                                          {p5} {p6} {p7}
                                          group by 1, 2,3,4,5,6  order by A.start_date desc)
                                          select event_name , event_id  , to_char(start_date, 'mm/dd/yyyy') as start_date,  to_char(end_date, 'mm/dd/yyyy') as end_date,channel_type , ad_type, event_promos from (select A.name as event_name , A.event_id  , min(A.start_date) as start_date, max(A.end_date) as end_date , A.channel_type , A.ad_type,
                                          json_agg( distinct jsonb_build_object('promo_name',B.name,'promo_id',B.promo_id ,'start_date'  , to_char(B.start_date, 'mm/dd/yyyy'), 'end_date', to_char(B.end_date, 'mm/dd/yyyy'), 'discount_level', B.discount_level ) ) as event_promos
                                          from events A, promos B
                                          where A.event_id = B.event_id
                                          group by A.name  , A.event_id , A.channel_type , A.ad_type) foo order by start_date desc $$$.format(
                                                 p1=" and B.hierarchy_level_name  = 'product_h2' and B.hierarchy_name in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p2=" and B.hierarchy_level_name  = 'product_h3' and B.hierarchy_name in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                                 p3=" and B.hierarchy_level_name  = 'product_h4' and B.hierarchy_name in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                                 p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                                 p5=" and Q.product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p6=" and Q.product_h3_id in ({?product_h3})  " if len(parameter:product_h3) > 0 else "",
                                                 p7=" and Q.product_h4_id in ({?product_h4})  " if len(parameter:product_h4) > 0 else "",
                                                 p8=" and A.start_date >= {?start_date} and  A.end_date <={?end_date} " if len(parameter:start_date) > 0 and len(parameter:end_date) > 0 else "" ) """
                     }
                     ,
                     "21" :{
                     "query_type":"select",
                     "query":  """ $$$  Select * from (select A.name as event_name , A.event_id , to_char(A.start_date, 'mm/dd/yyyy') as start_date, to_char(A.end_date, 'mm/dd/yyyy') as end_date, A.status as is_locked,A.channel_type , A.ad_type, A.marketing_notes, A.min_percent_value from public.event_master A , public.event_hierarchy B where 
                                          A.event_id = B.event_id  and A.is_deleted = 0
                                          {p1} {p2} {p3} {p4} 
                                          group by 1, 2,3,4,5,6,7,8
                                          union
                                          select A.name as event_name , A.event_id , to_char(A.start_date, 'mm/dd/yyyy') as start_date, to_char(A.end_date, 'mm/dd/yyyy') as end_date, A.status as is_locked,A.channel_type , A.ad_type, A.marketing_notes, A.min_percent_value from public.event_master A , public.event_hierarchy B where 
                                          A.event_id = B.event_id  and A.is_deleted = 0 and ad_type =2 and end_date >= now()
                                          group by 1, 2,3,4,5,6,7,8) foo order by start_date desc  $$$.format(
                                                 p1=" and B.hierarchy_level_name  = 'product_h2' and B.hierarchy_name in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p2=" and B.hierarchy_level_name  = 'product_h3' and B.hierarchy_name in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                                 p3=" and B.hierarchy_level_name  = 'product_h4' and B.hierarchy_name in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                                 p4=" and A.start_date >= {?start_date} and  A.end_date <={?end_date} " if len(parameter:start_date) > 0 and len(parameter:end_date) > 0 else "" ) """
    }
    ,
    "22" :{
        "query_type":"select",
        "query":  """ $$$ with top10 as ( select top.*,
                                          row_number() over (order by incremental_margin desc ) as row_num from 
                                          (select 
                                          name as offer_name,
                                          round(sum(sales_units)::decimal, 2) as actual_unit,
                                          round(sum(margin)::decimal, 2) as actual_margin,
                                          round(sum(revenue)::decimal, 2) as actual_sales,
                                          round(sum(incremental_sales_units)::decimal, 2) as incremental_units,
                                          round(sum(incremental_margin)::decimal, 2) as incremental_margin,
                                          round(sum(incremental_revenue)::decimal, 2) as incremental_sales,
                                          max(q.event_txn) as event_txn ,
                                          max(q.event_units_per_txn) as event_units_per_txn,
                                          max (q.event_avg_basket_size) as event_avg_basket_size,
                                          round(sum(promo_spend)::decimal, 2) as actual_markdown
                                          from simulation.ps_recommended_actuals a  
                                          left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id, public.product_master  b ,
                                          public.promo_master  c
                                          where a.promo_id  = c.promo_id and a.event_id  = c.event_id and a.item_id=b.product_h5_id 
                                          {p1} {p2} {p3} {p4} {p5}
                                          and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                                          group by 1) top),
                                          bottom10 as (select bottom.* ,
                                          row_number() over (order by incremental_margin  ) as row_num	from
                                          (select 
                                          name as offer_name,
                                          round(sum(sales_units)::decimal, 2) as actual_unit,
                                          round(sum(margin)::decimal, 2) as actual_margin,
                                          round(sum(revenue)::decimal, 2) as actual_sales,
                                          round(sum(incremental_sales_units)::decimal, 2) as incremental_units,
                                          round(sum(incremental_margin)::decimal, 2) as incremental_margin,
                                          round(sum(incremental_revenue)::decimal, 2) as incremental_sales,
                                          max(q.event_txn) as event_txn ,
                                          max(q.event_units_per_txn) as event_units_per_txn,
                                          max (q.event_avg_basket_size) as event_avg_basket_size,
                                          round(sum(promo_spend)::decimal, 2) as actual_markdown
                                          from simulation.ps_recommended_actuals a 
                                          left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id , public.product_master  b ,
                                          public.promo_master  c
                                          where a.promo_id  = c.promo_id and a.event_id  = c.event_id and a.item_id=b.product_h5_id 
                                          {p1} {p2} {p3} {p4} {p5}
                                          and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                                          group by 1) bottom  )
                                          select json_agg(a.*) as top10,json_agg(b.*) as bottom10
                                          from top10 A,bottom10 b
                                          where A.row_num = B.row_num and A.row_num <=10 and  B.row_num <=10 $$$.format(
                                                 p1=" and B.product_h2_id   in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p2=" and B.product_h3_id   in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                                 p3=" and B.product_h4_id   in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                                 p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                                 p5=" and A.promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "" ) """
    },
    "23" :{
        "query_type":"select",
        "query":  """ $$$ with base_table as 
                                   (select *,
                                   case when Total_offers <> 0 then round(under_performing_offers/Total_offers::decimal, 2) else null end as underperforming_percentage,
                                   case when total_revenue <> 0 then round(incremental_revenue/total_revenue::decimal, 2)  else null end  as incremental_revenue_percentage,
                                   case when total_margin <> 0 then round(incremental_margin/total_margin::decimal, 2)  else null end  as incremental_margin_percentage ,
                                   case when total_sales_units <> 0 then round(incremental_sales_units/total_sales_units::decimal, 2)  else null end  as incremental_sales_units_percentage
                                   from 
                                   (select {p7} 
                                   round(sum(incremental_sales_units)::decimal, 2) as incremental_sales_units,
                                   round(SUM(sales_units)::decimal, 2)  as total_sales_units  ,
                                   count(distinct a.promo_id) as Total_offers,
                                   count (distinct (case when (case when baseline_margin <> 0 then (incremental_margin/baseline_margin) else null end)<0 then name end)) as under_performing_offers ,
                                   round(sum(revenue)::decimal, 2) as total_revenue,
                                   round(sum(incremental_revenue)::decimal, 2) as incremental_revenue,
                                   round(sum(margin)::decimal, 2) as total_margin,
                                   round(sum(incremental_margin)::decimal, 2) as incremental_margin,
                                   round(sum(case when incremental_sales_units>0 then incremental_sales_units else 0 end)::decimal, 2) as positive_increment_sales_units,
                                   round(sum(case when incremental_sales_units<0 then incremental_sales_units else 0 end)::decimal, 2) as negative_increment_sales_units,
                                   round(sum(case when incremental_margin > 0 then incremental_margin else 0 end)::decimal, 2) as positive_incremental_margin,
                                   round(sum(case when incremental_margin < 0 then incremental_margin else 0 end)::decimal, 2) as negative_incremental_margin,
                                   round(sum(case when incremental_revenue > 0 then incremental_revenue else 0 end)::decimal, 2) as positive_incremental_revenue,
                                   round(sum(case when incremental_revenue < 0 then incremental_revenue else 0 end)::decimal, 2) as negative_incremental_revenue,
                                   max(q.event_txn) as event_txn ,
                                   max(q.event_units_per_txn) as event_units_per_txn,
                                   max (q.event_avg_basket_size) as event_avg_basket_size,
                                   round(sum(promo_spend)::decimal, 2) as actual_markdown
                                   from  simulation.ps_recommended_actuals  a 
                                   left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id,
                                   public.product_master  b ,  public.promo_master c 
                                   where a.promo_id  = c.promo_id and a.event_id  = c.event_id and a.item_id=b.product_h5_id
                                   {p1} {p2} {p3} {p4} {p5}
                                   and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                                   {p6}) foo )
                                   select {p8} total_offers,
                                   under_performing_offers,underperforming_percentage,total_revenue,
                                   incremental_revenue,incremental_revenue_percentage,total_margin,
                                   incremental_margin,incremental_margin_percentage,positive_increment_sales_units,
                                   negative_increment_sales_units,positive_incremental_margin,negative_incremental_margin,positive_incremental_revenue,negative_incremental_revenue,
                                   incremental_sales_units,total_sales_units,incremental_sales_units_percentage, actual_markdown
                                   from base_table $$$.format(
                                          p1=" and B.product_h2_id   in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                          p2=" and B.product_h3_id   in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                          p3=" and B.product_h4_id   in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                          p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                          p5=" and A.promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                                          p6=" group by 1,2,3,4" if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h4" else " group by 1,2,3" if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h3" else " group by 1,2" if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h2" else " group by 1" if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h1" else "",
                                          p7=" product_h1_name as product_h1 ,product_h2_name as product_h2,product_h3_name as product_h3,product_h4_name as product_h4 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h4" else " product_h1_name as product_h1 ,product_h2_name as product_h2,product_h3_name as product_h3 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h3" else " product_h1_name as product_h1 ,product_h2_name as product_h2 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h2" else " product_h1_name as product_h1 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h1" else "",
                                          p8=" product_h1 , product_h2, product_h3, product_h4," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h4" else "  product_h1 ,product_h2,product_h3 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h3" else " product_h1 ,product_h2 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h2" else " product_h1 ," if len(parameter:aggregation) > 0 and parameter:aggregation =="product_h1" else "") """
    },
    "24" :{
        "query_type":"select",
        "query":  """$$$select * ,(baseline_margin+incremental_margin) as net_margin,
                                          (baseline_revenue+incremental_revenue) as net_revenue from
                                          (select 
                                          round(sum(baseline_margin)::decimal, 2) as baseline_margin,
                                          round(sum(incremental_margin)::decimal, 2) as incremental_margin, 
                                          round(sum(case when incremental_margin>0 then incremental_margin else  0 end)::decimal, 2) as margin_positive,
                                          round(sum(case when incremental_margin<0 then incremental_margin else  0 end)::decimal, 2) as margin_negative,
                                          round(sum(baseline_revenue)::decimal, 2) as baseline_revenue,
                                          round(sum(incremental_revenue)::decimal, 2) as incremental_revenue,
                                          round(sum(case when incremental_revenue>0 then incremental_revenue else 0 end )::decimal, 2) as revenue_positive,
                                          round(sum(case when incremental_revenue<0 then incremental_revenue else 0 end )::decimal, 2) as revenue_negative,
                                          max(q.event_txn) as event_txn ,
                                          max(q.event_units_per_txn) as event_units_per_txn,
                                          max (q.event_avg_basket_size) as event_avg_basket_size
                                          from simulation.ps_recommended_actuals  a 
                                          left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id
                                          ,public.product_master b 
                                          where a.item_id=b.product_h5_id
                                          {p1} {p2} {p3} {p4} {p5}
                                          ) foo $$$.format(
                                          p1=" and B.product_h2_id   in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                          p2=" and B.product_h3_id   in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                          p3=" and B.product_h4_id   in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                          p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "" ,
                                          p5=" and A.promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "" ) """
    },
    "25" :{
        "query_type":"select",
        "query":  """ $$$with events as (
                                          select A.name , A.event_id , A.start_date, A.end_date, A.channel_type , A.ad_type  
                                          from public.event_master A , public.event_hierarchy B where 
                                          A.event_id = B.event_id  and A.is_deleted = 0
                                          {p1} {p2} {p3} {p4} {p8}
                                          group by 1, 2,3,4,5,6 order by A.start_date desc ),
                                          promos as 
                                          (select A.event_id, A.name , A.promo_id , A.start_date, A.end_date  
                                          from
                                            public.promo_master A ,
                                            public.promo_products D,
                                            public.product_master C
                                          where 
                                          A.promo_id = D.promo_id  
                                          and D.item_id = C.product_h5_id  
                                          and A.status = 4
                                          {p5} {p6} {p7}
                                          group by 1, 2,3,4,5  order by A.start_date desc)
                                          select event_name , event_id  , to_char(start_date, 'mm/dd/yyyy') as start_date,  to_char(end_date, 'mm/dd/yyyy') as end_date, channel_type , ad_type , event_promos from (select A.name as event_name , A.event_id  , min(A.start_date) as start_date, max(A.end_date) as end_date , A.channel_type , A.ad_type,
                                          json_agg( distinct jsonb_build_object('promo_name',B.name,'promo_id',B.promo_id ,'start_date'  , to_char(B.start_date, 'mm/dd/yyyy'), 'end_date', to_char(B.end_date, 'mm/dd/yyyy') ) ) as event_promos
                                          from events A, promos B
                                          where A.event_id = B.event_id and A.event_id in (select event_id from simulation.ps_recommended_actuals where recommendation_date >={?start_date} and recommendation_date <= {?end_date} group by 1 )
                                          group by A.name  , A.event_id , A.channel_type , A.ad_type) foo order by start_date desc $$$.format(
                                                 p1=" and B.hierarchy_level_name  = 'product_h2' and B.hierarchy_name in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p2=" and B.hierarchy_level_name  = 'product_h3' and B.hierarchy_name in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                                 p3=" and B.hierarchy_level_name  = 'product_h4' and B.hierarchy_name in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                                                 p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                                 p5=" and C.product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                                 p6=" and C.product_h3_id in ({?product_h3})  " if len(parameter:product_h3) > 0 else "",
                                                 p7=" and C.product_h4_id in ({?product_h4})  " if len(parameter:product_h4) > 0 else "",
                                                 p8=" and A.start_date <= {?end_date} and  A.end_date >= {?start_date} " if len(parameter:start_date) > 0 and len(parameter:end_date) > 0 else "" ) """
    },
    "26": {
        "query_type": "select",
        "query": """ $$$ with top10 as (
                            select
                                top.*,
                                row_number() over(order by incremental_margin desc ) as row_num
                            from (
                                select
                                    name as offer_name,
                                    a.promo_id,
                                    round(sum(sales_units)::decimal, 2) as actual_unit,
                                    round(sum(margin)::decimal, 2) as actual_margin,
                                    round(sum(revenue)::decimal, 2) as actual_sales,
                                    round(sum(incremental_sales_units)::decimal, 2) as incremental_units,
                                    round(sum(incremental_margin)::decimal, 2) as incremental_margin,
                                    round(sum(incremental_revenue)::decimal, 2) as incremental_sales,
                                    round(sum(promo_spend)::decimal,2) as actual_markdown,
                                    max(q.event_txn) as event_txn ,
                                    max(q.event_units_per_txn) as event_units_per_txn,
                                    max (q.event_avg_basket_size) as event_avg_basket_size
                                from
                                    simulation.ps_recommended_actuals a  
                                    left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id,
                                    public.product_master b ,
                                    public.promo_master c
                                where
                                    a.promo_id = c.promo_id
                                    and a.event_id = c.event_id
                                    and a.item_id = b.product_h5_id
                                    {p1} {p2} {p3} {p4} {p5}
                                    and a.recommendation_date >= c.start_date
                                    and a.recommendation_date <= c.end_date
                                group by
                                    1,2
                            ) top
                        )
                        select top10.* from top10 where row_num <= 10  
                $$$.format(p1=" and B.product_h2_id   in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                         p2=" and B.product_h3_id   in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                         p3=" and B.product_h4_id   in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                         p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                         p5=" and A.promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "" ) """
    },
    "27": {
            "query_type":"select",
            "query":  """ $$$ with bottom10 as (
                                select
                                    bottom.* ,
                                    row_number() over (
                                order by
                                    incremental_margin ) as row_num
                                from (
                                    select
                                        name as offer_name,
                                        a.promo_id,
                                        round(sum(sales_units)::decimal, 2) as actual_unit,
                                        round(sum(margin)::decimal, 2) as actual_margin,
                                        round(sum(revenue)::decimal, 2) as actual_sales,
                                        round(sum(incremental_sales_units)::decimal, 2) as incremental_units,
                                        round(sum(incremental_margin)::decimal, 2) as incremental_margin,
                                        round(sum(incremental_revenue)::decimal, 2) as incremental_sales,
                                        round(sum(promo_spend)::decimal,2) as actual_markdown,
                                        max(q.event_txn) as event_txn ,
                                        max(q.event_units_per_txn) as event_units_per_txn,
                                        max (q.event_avg_basket_size) as event_avg_basket_size
                                    from
                                        simulation.ps_recommended_actuals a 
                                        left join simulation.tb_event_basketdetails q
	                                        on A.event_id = q.event_id,
                                        public.product_master b ,
                                        public.promo_master c
                                    where
                                        a.promo_id = c.promo_id
                                        and a.event_id = c.event_id
                                        and a.item_id = b.product_h5_id
                                        {p1} {p2} {p3} {p4} {p5}
                                        and a.recommendation_date >= c.start_date
                                        and a.recommendation_date <= c.end_date
                                    group by
                                        1,2
                                ) bottom
                            )
                            select bottom10.* from bottom10 where row_num <= 10
                    $$$.format(p1=" and B.product_h2_id   in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                             p2=" and B.product_h3_id   in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                             p3=" and B.product_h4_id   in ({?product_h4}) " if len(parameter:product_h4) > 0 else "",
                             p4=" and A.event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                             p5=" and A.promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "" ) """
        },
        "28":{
            "query_type":"select",
            "query":  """ 
             select A.event_ad_type, B.offer_type , C.event_channel from 
                (SELECT json_agg(distinct jsonb_build_object('id',id,'name' , NAME ,'sub_parent', sub_parent, 'level',level,'is_default',is_default) ) as event_ad_type
				FROM metaschema.fn_app_getmastervalues(1) where sub_parent = 0 ) A,
                (SELECT json_agg(distinct jsonb_build_object('id',id,'name' , NAME ,'sub_parent', sub_parent, 'level',level,'is_default',is_default) )  as offer_type
                FROM metaschema.fn_app_getmastervalues(2) ) B,
                (SELECT json_agg(distinct jsonb_build_object('id',id,'name' , NAME ,'sub_parent', sub_parent, 'level',level,'is_default',is_default) )  as event_channel
                FROM metaschema.fn_app_getmastervalues(3) ) C """
        },
        "29":{
            "query_type":"select",
            "query":  """ $$$ select Ad_Zone_Code as "Ad Zone Code", SAP_Subclass as "SAP Subclass", Ad_Zone as "Ad Zone (Chain or specific DC)", offer_name as "Preliminary Offer Name", Offer_Type as "Offer Type",
                                div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",
                                AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
                                current_retail as "Current Retail",
                                "IMU" as "IMU %",
                                "disc_from_Current_Retail" as "% Discount from Current Retail",
                                event_retail as "Event Retail",
                                case when "Discount_from_Event_Retail"<0 then 0 else  "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                                case when aur>event_retail then event_retail else aur end  as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                                baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                                forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                                unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                                AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                                Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
                        from
                        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type, sap_promo_level,
                                div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,
                            AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,
                                round((Cost::Decimal),2) as Cost,
                                round((current_retail::Decimal),2) as current_retail,
                                round((IMU::Decimal),2) as "IMU",
                                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                                round((event_retail::Decimal),2) as event_retail,
                                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                                coalesce (round((aur::Decimal),2),0) as aur,
                                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                                round((baseline_sales::Decimal),2) as baseline_sales,
                                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                                round(( forecast_sales::Decimal),2) as forecast_sales,
                                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                                coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                                round(( sales_diff::Decimal),2) as sales_diff,
                                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                                round((margin_diff::Decimal),2) as margin_diff,
                                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                                Comments_m, Marketing_Specific_Comments
                        from (
                        select Ad_Zone_Code, SAP_Subclass,
                                'CHAIN' as Ad_Zone,
                                offer_name, sap_promo_level,
                                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                                    when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                                    when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                                    else 'BOGO (APR Offer)' end as Offer_Type,
                                div_cd, dept_cd , dept_nm ,
                                class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,
                                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                                "Status",GRM_Flow,
                                cost, current_retail,
                        (1 - (cost/current_retail)) as IMU,
                                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                                when  Offer_Type = 'fixed_price' then offer_value 
                                when  Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                                else  current_retail end as event_retail,
                        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                                aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                        from (
                        select case when sap_promo_level in (1,2,3) then null else SAP_Subclass end as SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,
                        div_cd, 
                        case when sap_promo_level=1 then null else dept_cd end as dept_cd, 
                        case when sap_promo_level=1 then null else dept_nm end as dept_nm,
                        case when sap_promo_level in (1,2) then null else class_cd end as  class_cd, 
                        case when sap_promo_level in (1,2) then null else class_nm end as class_nm,
                        case when sap_promo_level in (1,2,3) then null else subcls_cd end as subcls_cd,
                        case when sap_promo_level in(1,2,3) then null else subcls_nm end as subcls_nm,
                        case when sap_promo_level=5 then Sku else null end as Sku,
                        case when sap_promo_level=5 then descr else null end as descr,promo_id, sap_promo_level,
                        case when sap_promo_level=5 then AD_Feature_Item else null end as AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
                        avg(offer_value) as offer_value,
                        avg(effective_discount) as effective_discount,
                        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                        else avg(cost) end as cost,
                        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                        else avg(current_retail) end as current_retail,
                                max(Ad_Zone_Code) as Ad_Zone_Code,
                                max(IMU) as IMU ,
                                max(disc_from_CR) as disc_from_Current_Retail,
                                max(event_retail) as event_retail,
                                max(disc_from_ER) as disc_from_ER,
                                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                                sum(baseline_sales_units) as baseline_sales_units,
                                avg(baseline_sales_all_store) as baseline_sales_all_store,
                                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                                sum(baseline_sales) as baseline_sales ,
                                sum(baseline_GM_$) as baseline_GM_$,
                                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                                sum(forecast_sales_units) as forecast_sales_units,
                                avg(forecast_sales_all_store) as forecast_sales_all_store,
                                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                                sum(forecast_sales) as forecast_sales,
                                sum(forecast_event_MD) as forecast_event_MD,
                                sum(forecast_GM_$) as forecast_GM_$,
                                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                                sum(sales_diff) as sales_diff,
                                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                                sum(margin_diff) as margin_diff,
                                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                                max(Sell_Through) as Sell_Through,
                                max(Comments_m) as Comments_m,
                                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                        from (
                        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                                pm.name as offer_name,
                                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                                tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id, case when pm.sap_promo_level is null then 5 when pm.sap_promo_level<=3 then 3 else pm.sap_promo_level end as sap_promo_level,case when tasm.name  in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                                im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                                im.product_h5_id as Sku, im.item_nm as descr,
                                null as AD_Feature_Item,
                                null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                                t.recommendation_date as dt,
                                im.cost as cost,
                                im.price as current_retail,
                                '1 - cost/CR' as IMU,
                                'use case when on %off' as disc_from_CR,
                                'CR * (1-%offfromCR)' as event_retail,
                                '1 - AUR/ER' as disc_from_ER,
                                t.aur as aur, t.baseline_sales_units,
                                1435 as baseline_sales_all_store,
                                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                                t.baseline_revenue as baseline_sales,
                                t.baseline_margin as baseline_GM_$,
                                0 as UPAs_build,
                                t.sales_units as forecast_sales_units,
                                1435 as forecast_sales_all_store,
                                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                                'forecast units / bs units' as unit_lift,
                        (t.revenue - t.baseline_revenue) as sales_diff,
                                'forecast sales / bs sales' as sales_lift,
                        (t.margin - t.baseline_margin ) as margin_diff,
                                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                                null as Comments_m, null as Marketing_Specific_Comments
                        from simulation.ps_recommended_finalized as t
                        inner join ( select event_id , start_date , end_date from public.event_master {p1} group by 1,2,3) as em
                        on t.event_id = em.event_id
                        inner join (select * from public.promo_master where status=4 {p2}) as pm
                        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                            and t.recommendation_date between pm.start_date and pm.end_date
                        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                                            product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price
                                    From public.product_master {p3} {p4} {p5} {p6}) as im
                        on cast(im.product_h5_id as integer) = t.item_id
                        left join metaschema.tb_app_sub_master as tasm 
                        on t.offer_type_id = tasm.id 
                        ) as a
                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19
                        ) as b
                        left join  
                        promo_products as pp
                        on b.promo_id=pp.promo_id
                        and cast(b.Sku as integer)=pp.item_id
                        )as c
                        ) as d
                        where current_retail >0.01 $$$.format(
                                p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                                p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                                p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
                            )
                """
            },
            "30":{
                "query_type":"select", # marketing-sku-level
                "query":  """ $$$ 
                    with base as
                    ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
                    div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                    AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
                    current_retail as "Current Retail",effective_discount,
                    "IMU" as "IMU %",
                    "disc_from_Current_Retail" as "% Discount from Current Retail",
                    event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
                    round((percent_Message:: Decimal),2) as "% Message",
                    round(abs(dollar_Message :: Decimal),2) as "$ Message",
                    case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                    case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                    baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                    forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                    unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                    AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                    Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
                    from
                    (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
                    div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                    AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
                    (1-("event_retail_with_100%_redemption"/current_retail)) as "% Off From Current Retail (With 100% Redemption)",
                    case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
                    when "Offer Type Promosmart"='reg_price' then 0
                    else ("event_retail_with_100%_redemption" - current_retail)
                    end as dollar_Message,
                    case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/comp_value)
                    when "Offer Type Promosmart"='reg_price' then 0
                    else (1-"event_retail_with_100%_redemption"/current_retail)
                    end as percent_Message,
                    round((Cost::Decimal),2) as Cost,
                    effective_discount,
                    round((current_retail::Decimal),2) as current_retail,
                    round((IMU::Decimal),2) as "IMU",
                    coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                    round((event_retail::Decimal),2) as event_retail,
                    coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                    coalesce (round((aur::Decimal),2),0) as aur,
                    round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                    round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                    coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                    round((baseline_sales::Decimal),2) as baseline_sales,
                    round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                    coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                    round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                    round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                    coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                    round(( forecast_sales::Decimal),2) as forecast_sales,
                    round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                    round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                    coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                    round(( sales_diff::Decimal),2) as sales_diff,
                    coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                    round((margin_diff::Decimal),2) as margin_diff,
                    AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                    Comments_m, Marketing_Specific_Comments
                    from (
                    select Ad_Zone_Code, SAP_Subclass,
                    'CHAIN' as Ad_Zone,
                    offer_name,b.promo_id,
                    case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                    when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                    when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                    else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
                    div_cd, dept_cd , dept_nm ,
                    class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                    case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                    "Status",GRM_Flow,
                    cost, current_retail,
                    (1 - (cost/current_retail)) as IMU,
                    case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                    case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                            when Offer_Type = 'fixed_price' then offer_value
                    when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                    else current_retail end as event_retail,
                    case when Offer_Type='fixed_price' then offer_value
                    when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                    else current_retail*(1-effective_discount/100)
                    end as "event_retail_with_100%_redemption",
                    (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                    aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                    forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                    AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                    from (
                    select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
                    descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                    AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
                    avg(offer_value) as offer_value,
                    avg(effective_discount) as effective_discount,
                    case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                    else avg(cost) end as cost,
                    case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                    else avg(current_retail) end as current_retail,
                    max(Ad_Zone_Code) as Ad_Zone_Code,
                    max(IMU) as IMU ,
                    max(disc_from_CR) as disc_from_Current_Retail,
                    max(event_retail) as event_retail,
                    max(disc_from_ER) as disc_from_ER,
                    sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                    sum(baseline_sales_units) as baseline_sales_units,
                    avg(baseline_sales_all_store) as baseline_sales_all_store,
                    sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                    sum(baseline_sales) as baseline_sales ,
                    sum(baseline_GM_$) as baseline_GM_$,
                    sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                    sum(forecast_sales_units) as forecast_sales_units,
                    avg(forecast_sales_all_store) as forecast_sales_all_store,
                    sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                    sum(forecast_sales) as forecast_sales,
                    sum(forecast_event_MD) as forecast_event_MD,
                    sum(forecast_GM_$) as forecast_GM_$,
                    sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                    sum(sales_diff) as sales_diff,
                    sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                    sum(margin_diff) as margin_diff,
                    max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                    max(Sell_Through) as Sell_Through,
                    max(Comments_m) as Comments_m,
                    max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                    from (
                    select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                    pm.name as offer_name,
                    concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                    tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                    im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                    im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                    im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
                    null as AD_Feature_Item,
                    null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                    t.recommendation_date as dt,
                    im.cost as cost,
                    im.price as current_retail,
                    '1 - cost/CR' as IMU,
                    'use case when on %off' as disc_from_CR,
                    'CR * (1-%offfromCR)' as event_retail,
                    '1 - AUR/ER' as disc_from_ER,
                    t.aur as aur, t.baseline_sales_units,
                    1435 as baseline_sales_all_store,
                    'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                    t.baseline_revenue as baseline_sales,
                    t.baseline_margin as baseline_GM_$,
                    0 as UPAs_build,
                    t.sales_units as forecast_sales_units,
                    1435 as forecast_sales_all_store,
                    'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                    t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                    'forecast units / bs units' as unit_lift,
                    (t.revenue - t.baseline_revenue) as sales_diff,
                    'forecast sales / bs sales' as sales_lift,
                    (t.margin - t.baseline_margin ) as margin_diff,
                    null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                    null as Comments_m, null as Marketing_Specific_Comments
                    from simulation.ps_recommended_finalized as t 
                    inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master {p1} group by 1,2,3,4) as em
                    on t.event_id = em.event_id
                    inner join (select * from public.promo_master where status=4 {p2}) as pm 
                    on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                    and t.recommendation_date between pm.start_date and pm.end_date
                    inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                    product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD=1 then 'web enabled' else null end as "web designation",
                    case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
                    From public.product_master {p3} {p4} {p5} {p6}) as im 
                    on cast(im.product_h5_id as integer) = t.item_id
                    left join metaschema.tb_app_sub_master as tasm
                    on t.offer_type_id = tasm.id
                    ) as a
                    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
                    ) as b
                    left join
                    promo_products as pp
                    on b.promo_id=pp.promo_id
                    and cast(b.Sku as integer)=pp.item_id
                    )as c
                    ) as d
                    where current_retail >0.01)),
                    base_2 as
                    (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
                    from base a
                    left join public.mkt_store_inventory msi
                    on a."Sku"=msi.item_id
                    left join
                    (
                    select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
                    from public.mkt_dam group by 1
                    ) as md
                    on a."Sku"=md."Sku"),
                    offer_x_y_value as
                    (WITH promo_product_store as (
                    SELECT
                    cast(sm.scenario_id as int4) as scenario_id ,
                    sm.promo_id,
                    pp.item_id,
                    pm2."name",
                    CASE
                    WHEN discount_level = -200 THEN -200
                    WHEN discount_level = 1 THEN pm.product_h1_id
                    WHEN discount_level = 2 THEN pm.product_h2_id
                    WHEN discount_level = 3 THEN pm.product_h3_id
                    WHEN discount_level = 4 THEN pm.product_h4_id
                    WHEN discount_level = 5 THEN pm.product_h5_id
                    END AS discount_level_value
                    from
                    (select * from public.promo_master
                    where status= 4 and last_approved_scenario_id is not null
                    )pm2
                    inner join
                    public.scenario_master sm
                    on pm2.promo_id=sm.promo_id
                    and pm2.last_approved_scenario_id=sm.scenario_id
                    LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
                    LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
                    )
                    SELECT
                    pps.*,
                    psd.offer_type,
                    psd.offer_value,
                    psd.offer_type_id,
                    offer_x_value,
                    offer_y_value
                    FROM
                    promo_product_store pps
                    INNER JOIN (
                    SELECT
                    discount_level_value,
                    scenario_id,
                    offer_value,
                    offer_x_value,
                    offer_y_value,
                    offer_type_id,
                    tasm."name" as offer_type
                    FROM public.ps_scenario_discounts pd
                    INNER JOIN
                    metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
                    ) psd USING (discount_level_value, scenario_id) where offer_type in
                    ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
                    final as 
                    (select a.*,
                    case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
                    when "offer_type" = 'bxgy' then b.offer_value
                    when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
                    else null end as "BOGO_Bundle_BMSM_Message"
                    from base_2 a
                    left join offer_x_y_value b
                    on a.promo_id=b.promo_id
                    and a."Sku"=b.item_id),
                    combo as 
                    (With Product_Combo as
                    (Select a.id as "Sku", a.combo,
                    REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
                    b. Total_SKUS_in_Combo
                    from (select * from public.mkt_combo_sku_attribute
                    where combo like '%combo%'
                    ) a
                    inner join
                    (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
                    public.mkt_combo_sku_attribute
                    where combo like '%combo%'
                    group by 1
                    ) b
                    using (combo)
                    ),
                    Mkt_Extract as (Select * from final
                    where "Sku" is not null),
                    Combo_Flag as (
                    Select a.*,
                    case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
                    case when b. combo is not null then  combo else null end as Combo_Name,
                    case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
                    b. extracted_number
                    from Mkt_Extract a
                    left join
                    Product_Combo b
                    using ("Sku")
                    ),
                    Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
                    sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
                    from Combo_Flag
                    group by 1,2,3
                    ),
                    Combo_Indicator as (Select a.*,
                    case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
                    from Combo_Flag a
                    left join
                    Count_Flag b
                    using (Combo_Name, event_name,"Preliminary Offer Name")
                    ),
                    Final_Combo_Table as (
                    select "Preliminary Offer Name",event_name, "Sku",
                    Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
                    from
                    (Select *,
                    case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
                    case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
                    ) as a
                    ),
                    Output as (
                    (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
                    case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
                    from
                    (Select a.*, b. min_number from Final_Combo_Table a
                    inner join
                    (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
                    group by 1,2,3
                    ) b
                    using ("Preliminary Offer Name",event_name, "Sku")
                    ) a
                    where min_number= extracted_number or min_number is null
                    ))
                    Select * from Output)
                    select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
                    ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
                    "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
                    "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount" from                                                                                                                                                                                                        
                    (select a.*,case when combo_url is not null then combo_url
                    when "web designation" = 'web enabled' then prod_url
                    else null end as url
                    from 
                    (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
                    concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
                    from final a
                    left join combo b 
                    on a."Sku"=b."Sku"
                    and a."Preliminary Offer Name"=b."Preliminary Offer Name"
                    and a.event_name=b.event_name) as a ) as a
                    order by "Event Name","Preliminary Offer Name"
                    $$$.format(
                        p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                        p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                        p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                        p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                        p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                        p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
                    )
            """
        },
        "31":{
            "query_type":"select",
            "query":  """ $$$
                        with base as
                        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
                        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
                        current_retail as "Current Retail",effective_discount,percent_Message,dollar_Message,
                        "IMU" as "IMU %",
                        "disc_from_Current_Retail" as "% Discount from Current Retail",
                        event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
                        round((percent_Message:: Decimal),2) as "% Message",
                        round(abs(dollar_Message :: Decimal),2) as "$ Message",
                        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
                        from
                        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
                        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                        AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
                        (1-("event_retail_with_100%_redemption"/current_retail)) as "% Off From Current Retail (With 100% Redemption)",
                        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
                        when "Offer Type Promosmart"='reg_price' then 0
                        else ("event_retail_with_100%_redemption" - current_retail)
                        end as dollar_Message,
                        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/comp_value)
                        when "Offer Type Promosmart"='reg_price' then 0
                        else (1-"event_retail_with_100%_redemption"/current_retail)
                        end as percent_Message,
                        round((Cost::Decimal),2) as Cost,
                        effective_discount,
                        round((current_retail::Decimal),2) as current_retail,
                        round((IMU::Decimal),2) as "IMU",
                        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                        round((event_retail::Decimal),2) as event_retail,
                        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                        coalesce (round((aur::Decimal),2),0) as aur,
                        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                        round((baseline_sales::Decimal),2) as baseline_sales,
                        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                        round(( forecast_sales::Decimal),2) as forecast_sales,
                        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                        round(( sales_diff::Decimal),2) as sales_diff,
                        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                        round((margin_diff::Decimal),2) as margin_diff,
                        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                        Comments_m, Marketing_Specific_Comments
                        from (
                        select Ad_Zone_Code, SAP_Subclass,
                        'CHAIN' as Ad_Zone,
                        offer_name,b.promo_id,
                        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
                        div_cd, dept_cd , dept_nm ,
                        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                        "Status",GRM_Flow,
                        cost, current_retail,
                        (1 - (cost/current_retail)) as IMU,
                        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                                when Offer_Type = 'fixed_price' then offer_value
                        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                        else current_retail end as event_retail,
                        case when Offer_Type='fixed_price' then offer_value
                        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                        else current_retail*(1-effective_discount/100)
                        end as "event_retail_with_100%_redemption",
                        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                        from (
                        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
                        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
                        avg(offer_value) as offer_value,
                        avg(effective_discount) as effective_discount,
                        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                        else avg(cost) end as cost,
                        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                        else avg(current_retail) end as current_retail,
                        max(Ad_Zone_Code) as Ad_Zone_Code,
                        max(IMU) as IMU ,
                        max(disc_from_CR) as disc_from_Current_Retail,
                        max(event_retail) as event_retail,
                        max(disc_from_ER) as disc_from_ER,
                        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                        sum(baseline_sales_units) as baseline_sales_units,
                        avg(baseline_sales_all_store) as baseline_sales_all_store,
                        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                        sum(baseline_sales) as baseline_sales ,
                        sum(baseline_GM_$) as baseline_GM_$,
                        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                        sum(forecast_sales_units) as forecast_sales_units,
                        avg(forecast_sales_all_store) as forecast_sales_all_store,
                        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                        sum(forecast_sales) as forecast_sales,
                        sum(forecast_event_MD) as forecast_event_MD,
                        sum(forecast_GM_$) as forecast_GM_$,
                        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                        sum(sales_diff) as sales_diff,
                        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                        sum(margin_diff) as margin_diff,
                        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                        max(Sell_Through) as Sell_Through,
                        max(Comments_m) as Comments_m,
                        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                        from (
                        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                        pm.name as offer_name,
                        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
                        null as AD_Feature_Item,
                        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                        t.recommendation_date as dt,
                        im.cost as cost,
                        im.price as current_retail,
                        '1 - cost/CR' as IMU,
                        'use case when on %off' as disc_from_CR,
                        'CR * (1-%offfromCR)' as event_retail,
                        '1 - AUR/ER' as disc_from_ER,
                        t.aur as aur, t.baseline_sales_units,
                        1435 as baseline_sales_all_store,
                        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                        t.baseline_revenue as baseline_sales,
                        t.baseline_margin as baseline_GM_$,
                        0 as UPAs_build,
                        t.sales_units as forecast_sales_units,
                        1435 as forecast_sales_all_store,
                        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                        'forecast units / bs units' as unit_lift,
                        (t.revenue - t.baseline_revenue) as sales_diff,
                        'forecast sales / bs sales' as sales_lift,
                        (t.margin - t.baseline_margin ) as margin_diff,
                        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                        null as Comments_m, null as Marketing_Specific_Comments
                        from simulation.ps_recommended_finalized as t 
                        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master {p1} group by 1,2,3,4) as em
                        on t.event_id = em.event_id
                        inner join (select * from public.promo_master where status=4 {p2}) as pm
                        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                        and t.recommendation_date between pm.start_date and pm.end_date
                        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
                        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
                        From public.product_master {p3} {p4} {p5} {p6}) as im 
                        on cast(im.product_h5_id as integer) = t.item_id
                        left join metaschema.tb_app_sub_master as tasm
                        on t.offer_type_id = tasm.id
                        ) as a
                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
                        ) as b
                        left join
                        promo_products as pp
                        on b.promo_id=pp.promo_id
                        and cast(b.Sku as integer)=pp.item_id
                        )as c
                        ) as d
                        where current_retail >0.01)),
                        base_2 as
                        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
                        from base a
                        left join public.mkt_store_inventory msi
                        on a."Sku"=msi.item_id
                        left join
                        (
                        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
                        from public.mkt_dam group by 1
                        ) as md
                        on a."Sku"=md."Sku"),
                        offer_x_y_value as
                        (WITH promo_product_store as (
                        SELECT
                        cast(sm.scenario_id as int4) as scenario_id ,
                        sm.promo_id,
                        pp.item_id,
                        pm2."name",
                        CASE
                        WHEN discount_level = -200 THEN -200
                        WHEN discount_level = 1 THEN pm.product_h1_id
                        WHEN discount_level = 2 THEN pm.product_h2_id
                        WHEN discount_level = 3 THEN pm.product_h3_id
                        WHEN discount_level = 4 THEN pm.product_h4_id
                        WHEN discount_level = 5 THEN pm.product_h5_id
                        END AS discount_level_value
                        from
                        (select * from public.promo_master
                        where status= 4 and last_approved_scenario_id is not null
                        )pm2
                        inner join
                        public.scenario_master sm
                        on pm2.promo_id=sm.promo_id
                        and pm2.last_approved_scenario_id=sm.scenario_id
                        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
                        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
                        )
                        SELECT
                        pps.*,
                        psd.offer_type,
                        psd.offer_value,
                        psd.offer_type_id,
                        offer_x_value,
                        offer_y_value
                        FROM
                        promo_product_store pps
                        INNER JOIN (
                        SELECT
                        discount_level_value,
                        scenario_id,
                        offer_value,
                        offer_x_value,
                        offer_y_value,
                        offer_type_id,
                        tasm."name" as offer_type
                        FROM public.ps_scenario_discounts pd
                        INNER JOIN
                        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
                        ) psd USING (discount_level_value, scenario_id) where offer_type in
                        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
                        final as 
                        (select a.*,
                        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
                        when "offer_type" = 'bxgy' then b.offer_value
                        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
                        else null end as "BOGO_Bundle_BMSM_Message"
                        from base_2 a
                        left join offer_x_y_value b
                        on a.promo_id=b.promo_id
                        and a."Sku"=b.item_id),  
                        combo as 
                        (With Product_Combo as
                        (Select a.id as "Sku", a.combo,
                        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
                        b. Total_SKUS_in_Combo
                        from (select * from public.mkt_combo_sku_attribute
                        where combo like '%combo%'
                        ) a
                        inner join
                        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
                        public.mkt_combo_sku_attribute
                        where combo like '%combo%'
                        group by 1
                        ) b
                        using (combo)
                        ),
                        Mkt_Extract as (Select * from final
                        where "Sku" is not null),
                        Combo_Flag as (
                        Select a.*,
                        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
                        case when b. combo is not null then  combo else null end as Combo_Name,
                        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
                        b. extracted_number
                        from Mkt_Extract a
                        left join
                        Product_Combo b
                        using ("Sku")
                        ),
                        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
                        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
                        from Combo_Flag
                        group by 1,2,3
                        ),
                        Combo_Indicator as (Select a.*,
                        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
                        from Combo_Flag a
                        left join
                        Count_Flag b
                        using (Combo_Name, event_name,"Preliminary Offer Name")
                        ),
                        Final_Combo_Table as (
                        select "Preliminary Offer Name",event_name, "Sku",
                        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
                        from
                        (Select *,
                        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
                        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
                        ) as a
                        ),
                        Output as (
                        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
                        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
                        from
                        (Select a.*, b. min_number from Final_Combo_Table a
                        inner join
                        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
                        group by 1,2,3
                        ) b
                        using ("Preliminary Offer Name",event_name, "Sku")
                        ) a
                        where min_number= extracted_number or min_number is null
                        ))
                        Select * from Output),
                        sku_offer as 
                        (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart",ROUND(avg("% Discount from Current Retail"),2) as "% Discount from Current Retail",ROUND(avg("% Discount from Event Retail"),2) as "% Discount from Event Retail",ROUND(avg(case when "Selling AUR">0 then "Selling AUR" else null end ),2) as "Selling AUR",                                                                                                                                                                                                        
                        sum("Baseline Sales Units") as "Baseline Sales Units",sum("Baseline Sales $") as "Baseline Sales $",sum("Baseline GM $") as "Baseline GM $",sum("Forecast Sales Units") as "Forecast Sales Units",sum("Forecast Sales $") as "Forecast Sales $",sum("Forecast Event MD $") as "Forecast Event MD $",
                        sum("Forecast GM $") as "Forecast GM $",sum("Sales $ Difference") as "Sales $ Difference",sum("GM $ Difference") as "GM $ Difference",round((max(percent_Message):: Decimal),2) as "% Message",
                        round(max(abs(dollar_Message :: Decimal)),2) as "$ Message",min(coalesce("BOGO_Bundle_BMSM_Message")) as "BOGO Bundle BMSM Message",min("Current Retail") as "Current Retail Min Price",max("Current Retail") as "Current Retail Max Price",min("Event Retail") as "Event Retail Min Price",max("Event Retail") as "Event Retail Max Price",
                        min(case when "Selling AUR">0 then "Selling AUR" else null end ) as "Selling Price Minimum",max("Selling AUR") as "Selling Price Maximum",min(comp_value) as "Comp Value Min Off",max(comp_value) as "Comp Value Max Off",count(distinct "Sku") as "Total Sku",
                        count (distinct(case when "AD Feature Item" like '%yes%' then "Sku" else null end)) as "Feature Sku Count",count(case when "AD Feature Item" = 'yes' and "Image Exists" = 'Y' then "Sku" else null end) as "Count Feature Sku With Image",ROUND(cast(avg("%_Store_with_inventory") as numeric),2) as "% Store With Inventory",ROUND(cast(avg(case when "AD Feature Item" = 'yes' then "%_Store_with_inventory" else null end ) as numeric),2) as "Feature Sku % Store With Inventory",
                        ROUND(cast(min(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as numeric),2) as "Comp Value Min %Off",ROUND(cast(max(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as numeric),2) as "Comp Value Max %Off" from                                                                                                                                                                                                  
                        (select a.*,case when combo_url is not null then combo_url
                        when "web designation" = 'web enabled' then prod_url
                        else null end as url
                        from 
                        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
                        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
                        from final a
                        left join combo b 
                        on a."Sku"=b."Sku"
                        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
                        and a.event_name=b.event_name) as a ) as a
                        group by 1,2,3,4,5,6,7),
                        feature_sku as 
                        (SELECT promo_id,offer_comment,ARRAY_AGG(Item)::text AS sku_ids FROM 
                        (select pm.promo_id,pm.offer_comment, cast("item_id" as varchar) as Item from public.promo_products as a
                        join public.promo_master pm
                        on a.promo_id=pm.promo_id
                        WHERE pm.status=4
                        and a.is_hero=1
                        ) as a
                        group by 1,2)
                        select a.*,case when "Count Feature Sku With Image">0 then 'Y' else 'N' end as "feature sku image(y/n)" ,b.offer_comment,b.sku_ids as feature_skus
                        from sku_offer a
                        left join feature_sku b 
                        on a."Promo ID"=b.promo_id
                        order by "Event Name","Preliminary Offer Name"
                        $$$.format(
                                p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                                p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                                p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                                p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                                p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                                p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
                        )
            """
    
        },

        "32" : {
            "query_type":"select",
            "query": """ $$$ 
            select k.event_name as "Event Name",k.promo_id as "Promo ID" ,k."Promo Name" as "Promo Name",a.assetpath as "Asset Path",a."Dynamic Media Link" as "Dynamic Media Link",a.sku_list as "Sku",
            k.hero_sku as "Feature SKU"
            from
            (SELECT sku_list,assetpath,"Dynamic Media Link"
            FROM public.mkt_dam) as a
            join
            (select p.*,t.name as event_name
            from
            (select y.*,z.promo_name as "Promo Name",event_id
            from
            (select promo_id,item_id,case when is_hero = 1 then 'yes' else 'no' end as "hero_sku"
            from public.promo_products
            inner join public.product_master on item_id = product_h5_id
            {p3} {p4} {p5} {p6}
            ) as y
            join
            (
            select promo_id,event_id,name as promo_name
            from public.promo_master where status=4 {p2}
            ) as z
            on y.promo_id = z.promo_id) as p
            join
            (
            select event_id,name
            from public.event_master
            {p1}
            ) as t
            on p.event_id = t.event_id) as k
            on a.sku_list = k.item_id
            group by 1,2,3,4,5,6,7
            order by "Event Name","Promo Name"
            $$$.format(
                    p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                    p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                    p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                    p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                    p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                    p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
            )
            """
        },

        "33": {
            "query_type": "select",
            "query": """
            $$$
            with base as
            ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
            div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
            AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
            current_retail as "Current Retail",effective_discount,
            "IMU" as "IMU %",
            "disc_from_Current_Retail" as "% Discount from Current Retail",
            event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
            round((percent_Message:: Decimal),2) as "% Message",
            round(abs(dollar_Message :: Decimal),2) as "$ Message",
            case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
            case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
            baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
            forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
            unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
            AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
            Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
            from
            (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
            div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
            AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
            (1-("event_retail_with_100%_redemption"/current_retail)) as "% Off From Current Retail (With 100% Redemption)",
            case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
            when "Offer Type Promosmart"='reg_price' then 0
            else ("event_retail_with_100%_redemption" - current_retail)
            end as dollar_Message,
            case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/comp_value)
            when "Offer Type Promosmart"='reg_price' then 0
            else (1-"event_retail_with_100%_redemption"/current_retail)
            end as percent_Message,
            round((Cost::Decimal),2) as Cost,
            effective_discount,
            round((current_retail::Decimal),2) as current_retail,
            round((IMU::Decimal),2) as "IMU",
            coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
            round((event_retail::Decimal),2) as event_retail,
            coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
            coalesce (round((aur::Decimal),2),0) as aur,
            round((baseline_sales_units::Decimal),2) as baseline_sales_units,
            round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
            coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
            round((baseline_sales::Decimal),2) as baseline_sales,
            round((baseline_GM_$::Decimal),2) as baseline_GM_$,
            coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
            round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
            round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
            coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
            round(( forecast_sales::Decimal),2) as forecast_sales,
            round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
            round((forecast_GM_$::Decimal),2) as forecast_GM_$,
            coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
            round(( sales_diff::Decimal),2) as sales_diff,
            coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
            round((margin_diff::Decimal),2) as margin_diff,
            AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
            Comments_m, Marketing_Specific_Comments
            from (
            select Ad_Zone_Code, SAP_Subclass,
            'CHAIN' as Ad_Zone,
            offer_name,b.promo_id,
            case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
            when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
            when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
            else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
            div_cd, dept_cd , dept_nm ,
            class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
            case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
            "Status",GRM_Flow,
            cost, current_retail,
            (1 - (cost/current_retail)) as IMU,
            case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
            case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                    when Offer_Type = 'fixed_price' then offer_value
            when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
            else current_retail end as event_retail,
            case when Offer_Type='fixed_price' then offer_value
            when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
            else current_retail*(1-effective_discount/100)
            end as "event_retail_with_100%_redemption",
            (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
            aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
            forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
            AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
            from (
            select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
            descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
            AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
            avg(offer_value) as offer_value,
            avg(effective_discount) as effective_discount,
            case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
            else avg(cost) end as cost,
            case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
            else avg(current_retail) end as current_retail,
            max(Ad_Zone_Code) as Ad_Zone_Code,
            max(IMU) as IMU ,
            max(disc_from_CR) as disc_from_Current_Retail,
            max(event_retail) as event_retail,
            max(disc_from_ER) as disc_from_ER,
            sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
            sum(baseline_sales_units) as baseline_sales_units,
            avg(baseline_sales_all_store) as baseline_sales_all_store,
            sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
            sum(baseline_sales) as baseline_sales ,
            sum(baseline_GM_$) as baseline_GM_$,
            sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
            sum(forecast_sales_units) as forecast_sales_units,
            avg(forecast_sales_all_store) as forecast_sales_all_store,
            sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
            sum(forecast_sales) as forecast_sales,
            sum(forecast_event_MD) as forecast_event_MD,
            sum(forecast_GM_$) as forecast_GM_$,
            sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
            sum(sales_diff) as sales_diff,
            sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
            sum(margin_diff) as margin_diff,
            max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
            max(Sell_Through) as Sell_Through,
            max(Comments_m) as Comments_m,
            max(Marketing_Specific_Comments) as Marketing_Specific_Comments
            from (
            select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
            pm.name as offer_name,
            concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
            tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
            im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
            im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
            im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
            null as AD_Feature_Item,
            null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
            t.recommendation_date as dt,
            im.cost as cost,
            im.price as current_retail,
            '1 - cost/CR' as IMU,
            'use case when on %off' as disc_from_CR,
            'CR * (1-%offfromCR)' as event_retail,
            '1 - AUR/ER' as disc_from_ER,
            t.aur as aur, t.baseline_sales_units,
            1435 as baseline_sales_all_store,
            'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
            t.baseline_revenue as baseline_sales,
            t.baseline_margin as baseline_GM_$,
            0 as UPAs_build,
            t.sales_units as forecast_sales_units,
            1435 as forecast_sales_all_store,
            'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
            t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
            'forecast units / bs units' as unit_lift,
            (t.revenue - t.baseline_revenue) as sales_diff,
            'forecast sales / bs sales' as sales_lift,
            (t.margin - t.baseline_margin ) as margin_diff,
            null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
            null as Comments_m, null as Marketing_Specific_Comments
            from simulation.ps_recommended_finalized as t 
            inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master {p1} group by 1,2,3,4) as em
            on t.event_id = em.event_id
            inner join (select * from public.promo_master where status=4 {p2}) as pm 
            on pm.promo_id = t.promo_id and pm.event_id = t.event_id
            and t.recommendation_date between pm.start_date and pm.end_date
            inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
            product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
            case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
            From public.product_master {p3} {p4} {p5} {p6} ) as im 
            on cast(im.product_h5_id as integer) = t.item_id
            left join metaschema.tb_app_sub_master as tasm
            on t.offer_type_id = tasm.id
            ) as a
            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
            ) as b
            left join
            promo_products as pp
            on b.promo_id=pp.promo_id
            and cast(b.Sku as integer)=pp.item_id
            )as c
            ) as d
            where current_retail >0.01)),
            base_2 as
            (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
            from base a
            left join public.mkt_store_inventory msi
            on a."Sku"=msi.item_id
            left join
            (
            select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
            from public.mkt_dam group by 1
            ) as md
            on a."Sku"=md."Sku"),
            offer_x_y_value as
            (WITH promo_product_store as (
            SELECT
            cast(sm.scenario_id as int4) as scenario_id ,
            sm.promo_id,
            pp.item_id,
            pm2."name",
            CASE
            WHEN discount_level = -200 THEN -200
            WHEN discount_level = 1 THEN pm.product_h1_id
            WHEN discount_level = 2 THEN pm.product_h2_id
            WHEN discount_level = 3 THEN pm.product_h3_id
            WHEN discount_level = 4 THEN pm.product_h4_id
            WHEN discount_level = 5 THEN pm.product_h5_id
            END AS discount_level_value
            from
            (select * from public.promo_master
            where status= 4 and last_approved_scenario_id is not null
            )pm2
            inner join
            public.scenario_master sm
            on pm2.promo_id=sm.promo_id
            and pm2.last_approved_scenario_id=sm.scenario_id
            LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
            LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
            )
            SELECT
            pps.*,
            psd.offer_type,
            psd.offer_value,
            psd.offer_type_id,
            offer_x_value,
            offer_y_value
            FROM
            promo_product_store pps
            INNER JOIN (
            SELECT
            discount_level_value,
            scenario_id,
            offer_value,
            offer_x_value,
            offer_y_value,
            offer_type_id,
            tasm."name" as offer_type
            FROM public.ps_scenario_discounts pd
            INNER JOIN
            metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
            ) psd USING (discount_level_value, scenario_id) where offer_type in
            ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
            final as 
            (select a.*,
            case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
            when "offer_type" = 'bxgy' then b.offer_value
            when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
            else null end as "BOGO_Bundle_BMSM_Message"
            from base_2 a
            left join offer_x_y_value b
            on a.promo_id=b.promo_id
            and a."Sku"=b.item_id),
            combo as 
            (With Product_Combo as
            (Select a.id as "Sku", a.combo,
            REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
            b. Total_SKUS_in_Combo
            from (select * from public.mkt_combo_sku_attribute
            where combo like '%combo%'
            ) a
            inner join
            (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
            public.mkt_combo_sku_attribute
            where combo like '%combo%'
            group by 1
            ) b
            using (combo)
            ),
            Mkt_Extract as (Select * from final
            where "Sku" is not null),
            Combo_Flag as (
            Select a.*,
            case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
            case when b. combo is not null then  combo else null end as Combo_Name,
            case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
            b. extracted_number
            from Mkt_Extract a
            left join
            Product_Combo b
            using ("Sku")
            ),
            Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
            sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
            from Combo_Flag
            group by 1,2,3
            ),
            Combo_Indicator as (Select a.*,
            case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
            from Combo_Flag a
            left join
            Count_Flag b
            using (Combo_Name, event_name,"Preliminary Offer Name")
            ),
            Final_Combo_Table as (
            select "Preliminary Offer Name",event_name, "Sku",
            Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
            from
            (Select *,
            case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
            case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
            ) as a
            ),
            Output as (
            (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
            case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
            from
            (Select a.*, b. min_number from Final_Combo_Table a
            inner join
            (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
            group by 1,2,3
            ) b
            using ("Preliminary Offer Name",event_name, "Sku")
            ) a
            where min_number= extracted_number or min_number is null
            ))
            Select * from Output),
            planogram as 
            (select * from public.mkt_planogram_report),
            sku_offer as 
            (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
            ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
            "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
            "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount",Combo_Name   from                                                                                                                                                                                                        
            (select a.*,case when combo_url is not null then combo_url
            when "web designation" = 'web enabled' then prod_url
            else null end as url
            from 
            (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
            concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag,b.Combo_Name 
            from final a
            left join combo b 
            on a."Sku"=b."Sku"
            and a."Preliminary Offer Name"=b."Preliminary Offer Name"
            and a.event_name=b.event_name) as a ) as a),
            pivot_combo as
            (select combo as combo_name,ARRAY_AGG(cast(id as varchar))::text as combo_skus from public.mkt_combo_sku_attribute
            where combo like '%combo%'
            group by 1
            )
            select "Preliminary Offer Name","Event Name","Promo ID","Offer Type SAP","Offer Type Promosmart","Start Date",a."End Date","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description",
            b.planogram_name as "Planogram Name",b."Section",b.inline__ool as "Inline Ool",b.presentation_detail as "Presentation Detail",b.presentation_type as "Presentation Type",b.endcap__side_panel__queue_code as "Endcap Side Panel Queue Code","Current Retail","Event Retail","Selling AUR",
            "Comp Value",(("Comp Value"-"Current Retail")/"Comp Value") as "Comp Value %","$ Message","% Message","AD Feature Item","Combination Event Retail","Combination Current Retail","BOGO Bundle BMSM Message",a.combo_name as "Combo Name",combo_skus as "Combo Skus"
            from sku_offer a  
            left join planogram b 
            on a."Sku"=b.itemid
            and b.set_date<=a."Start Date" and a."End Date"<=b.end_date
            left join 
            pivot_combo c   
            on a.Combo_Name=c.combo_name
            order by "Event Name","Preliminary Offer Name"
            $$$.format(
                    p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                    p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                    p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                    p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                    p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                    p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
            )
            """
        },
        "34":{
            "query_type": "select",
            "query" : """
                $$$
                with base as
                ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
                div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
                current_retail as "Current Retail",effective_discount,percent_Message,dollar_Message,
                "IMU" as "IMU %",
                "disc_from_Current_Retail" as "% Discount from Current Retail",
                event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
                round((percent_Message:: Decimal),2) as "% Message",
                round(abs(dollar_Message :: Decimal),2) as "$ Message",
                case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
                from
                (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
                div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
                (1-("event_retail_with_100%_redemption"/current_retail)) as "% Off From Current Retail (With 100% Redemption)",
                case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
                when "Offer Type Promosmart"='reg_price' then 0
                else ("event_retail_with_100%_redemption" - current_retail)
                end as dollar_Message,
                case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/comp_value)
                when "Offer Type Promosmart"='reg_price' then 0
                else (1-"event_retail_with_100%_redemption"/current_retail)
                end as percent_Message,
                round((Cost::Decimal),2) as Cost,
                effective_discount,
                round((current_retail::Decimal),2) as current_retail,
                round((IMU::Decimal),2) as "IMU",
                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                round((event_retail::Decimal),2) as event_retail,
                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                coalesce (round((aur::Decimal),2),0) as aur,
                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                round((baseline_sales::Decimal),2) as baseline_sales,
                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                round(( forecast_sales::Decimal),2) as forecast_sales,
                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                round(( sales_diff::Decimal),2) as sales_diff,
                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                round((margin_diff::Decimal),2) as margin_diff,
                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                Comments_m, Marketing_Specific_Comments
                from (
                select Ad_Zone_Code, SAP_Subclass,
                'CHAIN' as Ad_Zone,
                offer_name,b.promo_id,
                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
                div_cd, dept_cd , dept_nm ,
                class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                "Status",GRM_Flow,
                cost, current_retail,
                (1 - (cost/current_retail)) as IMU,
                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                        when Offer_Type = 'fixed_price' then offer_value
                when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else current_retail end as event_retail,
                case when Offer_Type='fixed_price' then offer_value
                when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else current_retail*(1-effective_discount/100)
                end as "event_retail_with_100%_redemption",
                (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                from (
                select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
                descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
                avg(offer_value) as offer_value,
                avg(effective_discount) as effective_discount,
                case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(cost) end as cost,
                case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(current_retail) end as current_retail,
                max(Ad_Zone_Code) as Ad_Zone_Code,
                max(IMU) as IMU ,
                max(disc_from_CR) as disc_from_Current_Retail,
                max(event_retail) as event_retail,
                max(disc_from_ER) as disc_from_ER,
                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                sum(baseline_sales_units) as baseline_sales_units,
                avg(baseline_sales_all_store) as baseline_sales_all_store,
                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                sum(baseline_sales) as baseline_sales ,
                sum(baseline_GM_$) as baseline_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                sum(forecast_sales_units) as forecast_sales_units,
                avg(forecast_sales_all_store) as forecast_sales_all_store,
                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                sum(forecast_sales) as forecast_sales,
                sum(forecast_event_MD) as forecast_event_MD,
                sum(forecast_GM_$) as forecast_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                sum(sales_diff) as sales_diff,
                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                sum(margin_diff) as margin_diff,
                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                max(Sell_Through) as Sell_Through,
                max(Comments_m) as Comments_m,
                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                from (
                select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                pm.name as offer_name,
                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
                null as AD_Feature_Item,
                null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                t.recommendation_date as dt,
                im.cost as cost,
                im.price as current_retail,
                '1 - cost/CR' as IMU,
                'use case when on %off' as disc_from_CR,
                'CR * (1-%offfromCR)' as event_retail,
                '1 - AUR/ER' as disc_from_ER,
                t.aur as aur, t.baseline_sales_units,
                1435 as baseline_sales_all_store,
                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                t.baseline_revenue as baseline_sales,
                t.baseline_margin as baseline_GM_$,
                0 as UPAs_build,
                t.sales_units as forecast_sales_units,
                1435 as forecast_sales_all_store,
                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                'forecast units / bs units' as unit_lift,
                (t.revenue - t.baseline_revenue) as sales_diff,
                'forecast sales / bs sales' as sales_lift,
                (t.margin - t.baseline_margin ) as margin_diff,
                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                null as Comments_m, null as Marketing_Specific_Comments
                from simulation.ps_recommended_finalized as t 
                inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master {p1} group by 1,2,3,4) as em
                on t.event_id = em.event_id
                inner join (select * from public.promo_master where status=4 {p2}) as pm 
                on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                and t.recommendation_date between pm.start_date and pm.end_date
                inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
                case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
                From public.product_master {p3} {p4} {p5} {p6}) as im 
                on cast(im.product_h5_id as integer) = t.item_id
                left join metaschema.tb_app_sub_master as tasm
                on t.offer_type_id = tasm.id
                ) as a
                group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
                ) as b
                left join
                promo_products as pp
                on b.promo_id=pp.promo_id
                and cast(b.Sku as integer)=pp.item_id
                )as c
                ) as d
                where current_retail >0.01)),
                base_2 as
                (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
                from base a
                left join public.mkt_store_inventory msi
                on a."Sku"=msi.item_id
                left join
                (
                select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
                from public.mkt_dam group by 1
                ) as md
                on a."Sku"=md."Sku"),
                offer_x_y_value as
                (WITH promo_product_store as (
                SELECT
                cast(sm.scenario_id as int4) as scenario_id ,
                sm.promo_id,
                pp.item_id,
                pm2."name",
                CASE
                WHEN discount_level = -200 THEN -200
                WHEN discount_level = 1 THEN pm.product_h1_id
                WHEN discount_level = 2 THEN pm.product_h2_id
                WHEN discount_level = 3 THEN pm.product_h3_id
                WHEN discount_level = 4 THEN pm.product_h4_id
                WHEN discount_level = 5 THEN pm.product_h5_id
                END AS discount_level_value
                from
                (select * from public.promo_master
                where status= 4 and last_approved_scenario_id is not null
                )pm2
                inner join
                public.scenario_master sm
                on pm2.promo_id=sm.promo_id
                and pm2.last_approved_scenario_id=sm.scenario_id
                LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
                LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
                )
                SELECT
                pps.*,
                psd.offer_type,
                psd.offer_value,
                psd.offer_type_id,
                offer_x_value,
                offer_y_value
                FROM
                promo_product_store pps
                INNER JOIN (
                SELECT
                discount_level_value,
                scenario_id,
                offer_value,
                offer_x_value,
                offer_y_value,
                offer_type_id,
                tasm."name" as offer_type
                FROM public.ps_scenario_discounts pd
                INNER JOIN
                metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
                ) psd USING (discount_level_value, scenario_id) where offer_type in
                ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
                final as 
                (select a.*,
                case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
                when "offer_type" = 'bxgy' then b.offer_value
                when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
                else null end as "BOGO_Bundle_BMSM_Message"
                from base_2 a
                left join offer_x_y_value b
                on a.promo_id=b.promo_id
                and a."Sku"=b.item_id),
                combo as 
                (With Product_Combo as
                (Select a.id as "Sku", a.combo,
                REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
                b. Total_SKUS_in_Combo
                from (select * from public.mkt_combo_sku_attribute
                where combo like '%combo%'
                ) a
                inner join
                (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
                public.mkt_combo_sku_attribute
                where combo like '%combo%'
                group by 1
                ) b
                using (combo)
                ),
                Mkt_Extract as (Select * from final
                where "Sku" is not null),
                Combo_Flag as (
                Select a.*,
                case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
                case when b. combo is not null then  combo else null end as Combo_Name,
                case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
                b. extracted_number
                from Mkt_Extract a
                left join
                Product_Combo b
                using ("Sku")
                ),
                Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
                sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
                from Combo_Flag
                group by 1,2,3
                ),
                Combo_Indicator as (Select a.*,
                case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
                from Combo_Flag a
                left join
                Count_Flag b
                using (Combo_Name, event_name,"Preliminary Offer Name")
                ),
                Final_Combo_Table as (
                select "Preliminary Offer Name",event_name, "Sku",
                Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
                from
                (Select *,
                case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
                case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
                ) as a
                ),
                Output as (
                (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
                case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
                from
                (Select a.*, b. min_number from Final_Combo_Table a
                inner join
                (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
                group by 1,2,3
                ) b
                using ("Preliminary Offer Name",event_name, "Sku")
                ) a
                where min_number= extracted_number or min_number is null
                ))
                Select * from Output),
                planogram as 
                (select * from public.mkt_planogram_report),
                sku_offer as 
                (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
                ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
                "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
                "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount",dollar_message, percent_message from                                                                                                                                                                                                        
                (select a.*,case when combo_url is not null then combo_url
                when "web designation" = 'web enabled' then prod_url
                else null end as url,cn
                from 
                (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
                concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag,b.Combo_Name as cn
                from final a
                left join combo b 
                on a."Sku"=b."Sku"
                and a."Preliminary Offer Name"=b."Preliminary Offer Name"
                and a.event_name=b.event_name) as a ) as a),
                pivot_combo as
                (select combo as combo_name,STRING_AGG(cast(id as varchar),',') from public.mkt_combo_sku_attribute
                where combo like '%combo%'
                group by 1
                ),
                per_dollar_msg as 
                (select event_name,start_date,end_date,"Preliminary Offer Name",round((max(percent_Message):: Decimal),2) as "% Message",
                round(max(abs(dollar_Message :: Decimal)),2) as "$ Message" from final
                group by 1,2,3,4),
                signange_offer_level as
                (select "Event Name","Preliminary Offer Name",a."Start Date",a."End Date","Offer Type SAP","Offer Type Promosmart",
                b.planogram_name as "Planogram Name" ,b."Section",b.inline__ool as "Inline Ool",b.presentation_detail as "Presentation Detail",b.presentation_type as "Presentation Type",b.endcap__side_panel__queue_code as "Endcap Side Panel Queue Code",min("Event Retail") as "Event Retail Min Price",max("Event Retail") as "Event Retail Max Price",min("Selling AUR") as "Selling Price Minimum",max("Selling AUR") as "Selling Price Maximum",min("Current Retail") as "Current Retail Min Price",max("Current Retail") as "Current Retail Max Price",
                min(abs(dollar_Message)) as "Min $ Discount",max(abs(dollar_Message)) as "Max $ Discount",round(cast(min(percent_Message) as numeric),2) as "Min % Discount",round(cast(max(percent_Message) as numeric),2) as "Max % Discount",min("Comp Value") as "Comp Value Min Off",max("Comp Value") as "Comp Value Max Off",min(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as "Comp Value Min %Off",max(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as "Comp Value Max %Off"
                from sku_offer a  
                left join planogram b 
                on a."Sku"=b.itemid
                and b.set_date<=a."Start Date" and a."End Date"<=b.end_date
                group by 1,2,3,4,5,6,7,8,9,10,11,12)
                select a.*,b."% Message","$ Message" from
                signange_offer_level a 
                left join per_dollar_msg b 
                on b.start_date=a."Start Date"
                and b.end_date=a."End Date"
                and trim(a."Preliminary Offer Name")=trim(b."Preliminary Offer Name")
                order by "Event Name","Preliminary Offer Name"
                $$$.format(
                    p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                    p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                    p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                    p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                    p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                    p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
                )
            """
        },
        "35": {
            "query_type":"select",
            "query": """
                $$$
                with base as
                ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
                div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
                current_retail as "Current Retail",effective_discount,
                "IMU" as "IMU %",
                "disc_from_Current_Retail" as "% Discount from Current Retail",
                event_retail as "Event Retail","event_retail_with_100%_redemption", "% off from current retail (with 100% redemption)",
                round((percent_Message:: Decimal),2) as "% Message",
                round(abs(dollar_Message :: Decimal),2) as "$ Message",
                case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
                from
                (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
                div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
                (1-("event_retail_with_100%_redemption"/current_retail)) as "% off from current retail (with 100% redemption)",
                case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
                when "Offer Type Promosmart"='reg_price' then 0
                else ("event_retail_with_100%_redemption" - current_retail)
                end as dollar_Message,
                case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/comp_value)
                when "Offer Type Promosmart"='reg_price' then 0
                else (1-"event_retail_with_100%_redemption"/current_retail)
                end as percent_Message,
                round((Cost::Decimal),2) as Cost,
                effective_discount,
                round((current_retail::Decimal),2) as current_retail,
                round((IMU::Decimal),2) as "IMU",
                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                round((event_retail::Decimal),2) as event_retail,
                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                coalesce (round((aur::Decimal),2),0) as aur,
                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                round((baseline_sales::Decimal),2) as baseline_sales,
                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                round(( forecast_sales::Decimal),2) as forecast_sales,
                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                round(( sales_diff::Decimal),2) as sales_diff,
                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                round((margin_diff::Decimal),2) as margin_diff,
                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                Comments_m, Marketing_Specific_Comments
                from (
                select Ad_Zone_Code, SAP_Subclass,
                'CHAIN' as Ad_Zone,
                offer_name,b.promo_id,
                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
                div_cd, dept_cd , dept_nm ,
                class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                "Status",GRM_Flow,
                cost, current_retail,
                (1 - (cost/current_retail)) as IMU,
                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                        when Offer_Type = 'fixed_price' then offer_value
                when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else current_retail end as event_retail,
                case when Offer_Type='fixed_price' then offer_value
                when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else current_retail*(1-effective_discount/100)
                end as "event_retail_with_100%_redemption",
                (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                from (
                select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
                descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
                AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
                avg(offer_value) as offer_value,
                avg(effective_discount) as effective_discount,
                case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(cost) end as cost,
                case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(current_retail) end as current_retail,
                max(Ad_Zone_Code) as Ad_Zone_Code,
                max(IMU) as IMU ,
                max(disc_from_CR) as disc_from_Current_Retail,
                max(event_retail) as event_retail,
                max(disc_from_ER) as disc_from_ER,
                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                sum(baseline_sales_units) as baseline_sales_units,
                avg(baseline_sales_all_store) as baseline_sales_all_store,
                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                sum(baseline_sales) as baseline_sales ,
                sum(baseline_GM_$) as baseline_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                sum(forecast_sales_units) as forecast_sales_units,
                avg(forecast_sales_all_store) as forecast_sales_all_store,
                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                sum(forecast_sales) as forecast_sales,
                sum(forecast_event_MD) as forecast_event_MD,
                sum(forecast_GM_$) as forecast_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                sum(sales_diff) as sales_diff,
                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                sum(margin_diff) as margin_diff,
                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                max(Sell_Through) as Sell_Through,
                max(Comments_m) as Comments_m,
                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                from (
                select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                pm.name as offer_name,
                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
                null as AD_Feature_Item,
                null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                t.recommendation_date as dt,
                im.cost as cost,
                im.price as current_retail,
                '1 - cost/CR' as IMU,
                'use case when on %off' as disc_from_CR,
                'CR * (1-%offfromCR)' as event_retail,
                '1 - AUR/ER' as disc_from_ER,
                t.aur as aur, t.baseline_sales_units,
                1435 as baseline_sales_all_store,
                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                t.baseline_revenue as baseline_sales,
                t.baseline_margin as baseline_GM_$,
                0 as UPAs_build,
                t.sales_units as forecast_sales_units,
                1435 as forecast_sales_all_store,
                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                'forecast units / bs units' as unit_lift,
                (t.revenue - t.baseline_revenue) as sales_diff,
                'forecast sales / bs sales' as sales_lift,
                (t.margin - t.baseline_margin ) as margin_diff,
                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                null as Comments_m, null as Marketing_Specific_Comments
                from simulation.ps_recommended_finalized as t 
                inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master {p1} group by 1,2,3,4) as em
                on t.event_id = em.event_id
                inner join (select * from public.promo_master where status=4 {p2}) as pm 
                on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                and t.recommendation_date between pm.start_date and pm.end_date
                inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD=1 then 'web enabled' else null end as "web designation",
                case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
                From public.product_master {p3} {p4} {p5} {p6}) as im 
                on cast(im.product_h5_id as integer) = t.item_id
                left join metaschema.tb_app_sub_master as tasm
                on t.offer_type_id = tasm.id
                ) as a
                group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
                ) as b
                left join
                promo_products as pp
                on b.promo_id=pp.promo_id
                and cast(b.Sku as integer)=pp.item_id
                )as c
                ) as d
                where current_retail >0.01)),
                base_2 as
                (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
                from base a
                left join public.mkt_store_inventory msi
                on a."Sku"=msi.item_id
                left join
                (
                select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
                from public.mkt_dam group by 1
                ) as md
                on a."Sku"=md."Sku"),
                offer_x_y_value as
                (WITH promo_product_store as (
                SELECT
                cast(sm.scenario_id as int4) as scenario_id ,
                sm.promo_id,
                pp.item_id,
                pm2."name",
                CASE
                WHEN discount_level = -200 THEN -200
                WHEN discount_level = 1 THEN pm.product_h1_id
                WHEN discount_level = 2 THEN pm.product_h2_id
                WHEN discount_level = 3 THEN pm.product_h3_id
                WHEN discount_level = 4 THEN pm.product_h4_id
                WHEN discount_level = 5 THEN pm.product_h5_id
                END AS discount_level_value
                from
                (select * from public.promo_master
                where status= 4 and last_approved_scenario_id is not null
                )pm2
                inner join
                public.scenario_master sm
                on pm2.promo_id=sm.promo_id
                and pm2.last_approved_scenario_id=sm.scenario_id
                LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
                LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
                )
                SELECT
                pps.*,
                psd.offer_type,
                psd.offer_value,
                psd.offer_type_id,
                offer_x_value,
                offer_y_value
                FROM
                promo_product_store pps
                INNER JOIN (
                SELECT
                discount_level_value,
                scenario_id,
                offer_value,
                offer_x_value,
                offer_y_value,
                offer_type_id,
                tasm."name" as offer_type
                FROM public.ps_scenario_discounts pd
                INNER JOIN
                metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
                ) psd USING (discount_level_value, scenario_id) where offer_type in
                ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
                final as 
                (select a.*,
                case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
                when "offer_type" = 'bxgy' then b.offer_value
                when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
                else null end as "BOGO_Bundle_BMSM_Message"
                from base_2 a
                left join offer_x_y_value b
                on a.promo_id=b.promo_id
                and a."Sku"=b.item_id),
                combo as 
                (With Product_Combo as
                (Select a.id as "Sku", a.combo,
                REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
                b. Total_SKUS_in_Combo
                from (select * from public.mkt_combo_sku_attribute
                where combo like '%combo%'
                ) a
                inner join
                (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
                public.mkt_combo_sku_attribute
                where combo like '%combo%'
                group by 1
                ) b
                using (combo)
                ),
                Mkt_Extract as (Select * from final
                where "Sku" is not null),
                Combo_Flag as (
                Select a.*,
                case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
                case when b. combo is not null then  combo else null end as Combo_Name,
                case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
                b. extracted_number
                from Mkt_Extract a
                left join
                Product_Combo b
                using ("Sku")
                ),
                Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
                sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
                from Combo_Flag
                group by 1,2,3
                ),
                Combo_Indicator as (Select a.*,
                case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
                from Combo_Flag a
                left join
                Count_Flag b
                using (Combo_Name, event_name,"Preliminary Offer Name")
                ),
                Final_Combo_Table as (
                select "Preliminary Offer Name",event_name, "Sku",
                Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
                from
                (Select *,
                case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
                case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
                ) as a
                ),
                Output as (
                (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
                case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
                from
                (Select a.*, b. min_number from Final_Combo_Table a
                inner join
                (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
                group by 1,2,3
                ) b
                using ("Preliminary Offer Name",event_name, "Sku")
                ) a
                where min_number= extracted_number or min_number is null
                ))
                Select * from Output),
                sku_offer as (
                select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% off from current retail (with 100% redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
                ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
                "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
                "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount" from                                                                                                                                                                                                        
                (select a.*,case when combo_url is not null then combo_url
                when "web designation" = 'web enabled' then prod_url
                else null end as url
                from 
                (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
                concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
                from final a
                left join combo b 
                on a."Sku"=b."Sku"
                and a."Preliminary Offer Name"=b."Preliminary Offer Name"
                and a.event_name=b.event_name) as a ) as a),
                Marketing_Set as (
                Select a.*, b. price as Current_Retail_Price_PM, 
                b. price as Event_Retail_Price_PM from
                (SELECT distinct childskus,id FROM public.mkt_set
                where childskus is not null
                ) a
                inner join public.product_master b 
                on a. childskus=b.product_h5_id),
                Set_Offer_Combinations as 
                (Select distinct "Event Name", "Preliminary Offer Name", "Start Date", "End Date", id as set_name from 
                (Select a.*, b. * from sku_offer a
                left join
                Marketing_Set b  
                on b.childskus= a."Sku") a
                where id is not null
                ),
                SKU_Set_Values as 
                (Select a.*, b.childskus as "Sku", b. Current_Retail_Price_PM, 
                b. Event_Retail_Price_PM
                from Set_Offer_Combinations a
                inner join 
                Marketing_Set b
                on a.set_name= b.id),
                Prices_Choice as (
                Select a.*,
                case when b. "Current Retail" is not null then b."Current Retail" else Current_Retail_Price_PM end as "Current Retail",
                case when b. "Event Retail With 100% Redemption" is not null then b."Event Retail With 100% Redemption" else Event_Retail_Price_PM end as "Event Retail Price" from SKU_Set_Values a 
                left join 
                sku_offer b
                using ("Event Name","Preliminary Offer Name","Sku" )),
                Set_Rollup as (
                Select "Event Name","Preliminary Offer Name","Start Date","End Date",set_name ,
                sum("Current Retail") as "Set Ticket Price", sum("Event Retail Price") as "Set Event Price",ARRAY_AGG(cast("Sku" as varchar))::text as set_list
                from Prices_Choice
                group by 1,2,3,4,5)
                (Select distinct a."Event Name", a."Preliminary Offer Name", a."Start Date", a."End Date",
                a.set_name as "Set Name",a."Sku",pm.product_h5_name as "Description",pm.product_h1_name as "Division",pm.product_h2_name as "Department",product_h3_name as "Class Desc",product_h4_name as "Subclass Desc", a."Current Retail",b."Set Ticket Price",a."Event Retail Price", b."Set Event Price",b. set_list as "Set List"
                from Prices_Choice a 
                inner join 
                Set_Rollup b
                using (set_name,"Event Name","Preliminary Offer Name")
                inner join public.product_master pm 
                on a."Sku"=pm.product_h5_id
                )  
                order by "Event Name","Preliminary Offer Name"
                $$$.format(
                    p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                    p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else "",
                    p3=" where product_h1_id in ({?product_h1}) " if len(parameter:product_h1) > 0 else "",
                    p4=" where product_h2_id in ({?product_h2}) " if (len(parameter:product_h2) > 0 and len(parameter:product_h1) == 0) else " and product_h2_id in ({?product_h2}) " if len(parameter:product_h2) > 0 else "",
                    p5=" where product_h3_id in ({?product_h3}) " if (len(parameter:product_h3) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0) else " and product_h3_id in ({?product_h3}) " if len(parameter:product_h3) > 0 else "",
                    p6=" where product_h4_id in ({?product_h4}) " if (len(parameter:product_h4) > 0 and len(parameter:product_h1) == 0 and len(parameter:product_h2) == 0 and len(parameter:product_h3) == 0) else " and product_h4_id in ({?product_h4}) " if len(parameter:product_h4) > 0 else ""
                )
            """
        },
        "36": {
            "query_type": "select",
            "query": """
            $$$
            with base as
            (select Ad_Zone_Code,event_name,cast(start_date as date) as start_date,cast(end_date as date) as end_date, SAP_Subclass, Ad_Zone, offer_name, Offer_Type,tool_offer_type,
            div_cd,div_nm, dept_cd ,dept_nm, class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
            AD_Feature_Item,event_objective,ad_type,channel,
            round((Cost::Decimal),2) as Cost,
            round((current_retail::Decimal),2) as current_retail,
            round((IMU::Decimal),2) as "IMU",
            coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
            round((event_retail::Decimal),2) as event_retail,
            coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
            round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
            coalesce (round((aur::Decimal),2),0) as aur,
            round((baseline_sales_units::Decimal),2) as baseline_sales_units,
            coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
            round((baseline_sales::Decimal),2) as baseline_sales,
            round((baseline_GM_$::Decimal),2) as baseline_GM_$,
            coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
            round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
            round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
            coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
            round(( forecast_sales::Decimal),2) as forecast_sales,
            round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
            round((forecast_GM_$::Decimal),2) as forecast_GM_$,
            round(( forecast_sales_units_diff::Decimal),2) as forecast_sales_units_diff,
            round(( sales_diff::Decimal),2) as sales_diff,
            coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
            round((margin_diff::Decimal),2) as margin_diff,
            AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
            Comments_m, Marketing_Specific_Comments
            from (
            select Ad_Zone_Code, SAP_Subclass, start_date,end_date,
            'CHAIN' as Ad_Zone,event_objective,
            event_name,offer_name,
            case when (tool_offer_type = 'percent_off') then 'percent_off'
            when (tool_offer_type = 'fixed_price') then 'fixed_price'
            when (tool_offer_type = 'extra_amount_off') then 'extra_amount_off'
            when (tool_offer_type = 'reg_price_cv') then 'reg_price_cv'
            when (tool_offer_type = 'reg_price') then 'reg_price'
            when (tool_offer_type = 'bxgy') then 'bxgy'
            when (tool_offer_type = 'bundle_offer') then 'bundle_offer'
            when (tool_offer_type = 'bxgy_$') then 'bxgy_$'
            when (tool_offer_type = 'bmsm_$') then 'bmsm_$'
            else 'BOGO (APR Offer)' end as tool_offer_type,
            case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
            when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
            when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'reg_price'
            else 'BOGO (APR Offer)' end as Offer_Type,
            case when (ad_type = '1') then 'Advertised'
                when (ad_type = '2') then 'Unadvertised' end as ad_type,
            case when (channel_type = '22') then 'Ecomm Only'
                when (channel_type = '23') then 'Omni Channel' end as channel,
            div_cd,div_nm,dept_cd , dept_nm ,
            class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
            case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item,
            cost, current_retail,
            (1 - (cost/current_retail)) as IMU,
            case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
            case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                    when Offer_Type = 'fixed_price' then offer_value
                    when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                    else current_retail end as event_retail,
            (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
            aur,baseline_sales_units, baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units, forecast_sales_all_store ,
            forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$,forecast_sales_units_diff, sales_diff, sales_lift, margin_diff,
            AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
            from (
            select SAP_Subclass, 'CHAIN' as "Ad_Zone",event_name,offer_name, Offer_Type,tool_offer_type , start_date,end_date,
            div_cd,div_nm, dept_cd, dept_nm, class_cd, class_nm, subcls_cd, subcls_nm, brand,
            Sku, descr,promo_id,channel_type,
            AD_Feature_Item,event_objective,ad_type,
            avg(effective_discount) as effective_discount ,
            case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
            else avg(cost) end as cost,
            case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
            else avg(current_retail) end as current_retail, avg(offer_value) as offer_value,
            max(Ad_Zone_Code) as Ad_Zone_Code,
            max(IMU) as IMU ,
            max(disc_from_CR) as disc_from_Current_Retail,
            max(event_retail) as event_retail,
            max(disc_from_ER) as disc_from_ER,
            sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
            sum(baseline_sales_units) as baseline_sales_units,
            avg(baseline_sales_all_store) as baseline_sales_all_store,
            sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
            sum(baseline_sales) as baseline_sales ,
            sum(baseline_GM_$) as baseline_GM_$,
            sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
            sum(forecast_sales_units) as forecast_sales_units,
            avg(forecast_sales_all_store) as forecast_sales_all_store,
            sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
            sum(forecast_sales) as forecast_sales,
            sum(forecast_event_MD) as forecast_event_MD,
            sum(forecast_GM_$) as forecast_GM_$,
            sum(forecast_sales_units_diff) as forecast_sales_units_diff,
            sum(sales_diff) as sales_diff,
            sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
            sum(margin_diff) as margin_diff,
            max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
            max(Sell_Through) as Sell_Through,
            max(Comments_m) as Comments_m,
            max(Marketing_Specific_Comments) as Marketing_Specific_Comments
            from (
            select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,pm.start_date as start_date, pm.end_date as end_date,
            pm.name as offer_name, em.channel_type as channel_type,em.ad_type as ad_type,em.name as event_name,
            concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
            t.effective_discount ,t.promo_id as promo_id,em.event_objective as event_objective,
            tasm.name as Offer_Type, tasm.name as tool_offer_type, case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
            im.division_id as div_cd,im.division_nm as div_nm, im.department_id as dept_cd, im.department_nm as dept_nm,im.brand as brand,
            im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
            im.product_h5_id as Sku, im.item_nm as descr,
            null as AD_Feature_Item,
            t.recommendation_date as dt,
            im.cost as cost,
            im.price as current_retail,
            '1 - cost/CR' as IMU,
            'use case when on %off' as disc_from_CR,
            'CR * (1-%offfromCR)' as event_retail,
            '1 - AUR/ER' as disc_from_ER,
            t.aur as aur, t.baseline_sales_units as baseline_sales_units,
            1435 as baseline_sales_all_store,
            'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
            t.baseline_revenue as baseline_sales,
            t.baseline_margin as baseline_GM_$,
            0 as UPAs_build,
            t.sales_units as forecast_sales_units,
            1435 as forecast_sales_all_store,
            'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
            t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
            (t.sales_units-t.baseline_sales_units) as forecast_sales_units_diff,
            (t.revenue - t.baseline_revenue) as sales_diff,
            'forecast sales / bs sales' as sales_lift,
            (t.margin - t.baseline_margin ) as margin_diff,
            null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
            null as Comments_m, null as Marketing_Specific_Comments
            from simulation.ps_recommended_finalized as t
            inner join ( select event_id ,name, start_date , end_date,event_objective,channel_type,ad_type from public.event_master {p1} group by 1,2,3,4,5,6) as em
            on t.event_id = em.event_id
            inner join (select * from public.promo_master where status=4 {p2}) as pm
            on pm.promo_id = t.promo_id and pm.event_id = t.event_id
            and t.recommendation_date between pm.start_date and pm.end_date
            inner join (select product_h1_id as division_id , product_h1_name as division_nm, product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
            product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,brand as brand,cost,price
            From public.product_master
            ) as im
            on cast(im.product_h5_id as integer) = t.item_id
            left join metaschema.tb_app_sub_master as tasm
            on t.offer_type_id = tasm.id
            ) as a
            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
            ) as b
            inner join
            promo_products as pp
            on b.promo_id=pp.promo_id
            and cast(b.Sku as integer)=pp.item_id
            )as c
            where current_retail >0.01
            ),
            base_2 as
            (select u.*,case when u.Performance <-0.05 then 'toxic' when u.Performance between -0.05 and 0.05 then 'average' else 'good' end as Performance_cal
            from
            (select y.*,y.incremental_per/y.baseline_per as performance
            from
            (select em."name" as event_name,pm.start_date,pm.end_date,pm."name" as offer_name,item_id as item_id,
            avg(sum(t.incremental_margin)) over (partition by pm.promo_id,pm.event_id) as incremental_per,
            avg(sum(t.baseline_margin)) over (partition by pm.promo_id,pm.event_id) as baseline_per,
            sum(t.sales_units) as Actual_Sales_Units,sum(t.revenue) as Actual_Sales_$,sum(t.baseline_sales_units) as Actual_Baseline_Sales_Units,
            sum(t.incremental_sales_units) as Incremental_Sales_Units,sum(t.baseline_revenue) as Actual_Baseline_Revenue,
            sum(t.incremental_revenue) as Actual_Incremental_Revenue,sum(t.margin) as Actual_Margin,sum(t.baseline_margin) as Actual_Baseline_Margin,
            sum(t.incremental_margin) as Actual_Incremental_Margin,
            sum(promo_spend) as Actual_MD,
            sum(t.sales_units-t.baseline_sales_units) as actual_sales_unit_diff,
            sum(t.sales_units)/1435 as Actual_UPAs
            from simulation.ps_recommended_actuals as t
            inner join public.promo_master as pm
            on pm.promo_id = t.promo_id and pm.event_id = t.event_id
            and t.recommendation_date >= pm.start_date and t.recommendation_date <= pm.end_date
            inner join event_master em
            on em.event_id =pm.event_id
            and pm.status=4
            group by 1,2,3,4,5,pm.promo_id,pm.event_id
            )as y)as u)
            select a.Ad_Zone_Code as "Ad Zone Code",a.event_name as "Event Name", a.start_date as "Start Date",a.end_date as "End Date",a.SAP_Subclass as "SAP Subclass",
            a.Ad_Zone as "Ad Zone (Chain or specific DC)", a.offer_name as "Preliminary Offer Name",
            a.Offer_Type as "Offer Type", a.tool_offer_type as "PriceSmart Offer Type",a.div_cd as "Div. #", a.div_nm as "Div Name",a.dept_cd as "Dept. #", a.dept_nm as "Dept Name",
            a.class_cd as "Class #", a.class_nm as "Class Desc",a.subcls_cd as "Subclass #", a.subcls_nm as "Subclass Desc", a.brand as "Brand",
            Sku as "Sku", descr as "Description",
            a.AD_Feature_Item as "AD Feature Item",a.event_objective as "Intent of Promo" ,a.cost as "Cost",
            a.current_retail as "Current Retail",a.ad_type as "Ad_Type",a.channel as "Channel",b.Performance_cal as "Performance",
            a."IMU"*100 as "IMU %",
            a."disc_from_Current_Retail"*100 as "% Discount from Current Retail",
            a.event_retail as "Event Retail",
            a."Discount_from_Event_Retail"*100 as "% Discount from Event Retail",
            a.aur as "Selling AUR",
            a.AD_Prep_Units as "AD Prep Units", a.as_Units as "A/S Units", a.Ad_Prep_$ as "Ad Prep $", a.Sell_Through as "Sell Through",
            a.Comments_m as "Comments (Basis for Projection)", a.Marketing_Specific_Comments as "Marketing Specific Comments",
            a.baseline_sales_all_store as "Baseline # of Stores",
            a.baseline_UPAs as "Baseline UPAS",
            a.forecast_sales_all_store as "Forecast # of Stores",
            a.forecast_UPAs as "Forecast UPAS",
            a.UPAs_build as "UPAS Build",
            a.forecast_event_MD as "Forecast Event MD $",
            a.forecast_sales_units as "Forecast Sales Units",
            a.baseline_sales_units as "Forecast Baseline Sales Units",
            a.forecast_sales_units_diff as "Forecast Incremental Units",
            a.forecast_sales as "Forecast Sales $",
            a.baseline_sales as " Forecast Baseline Sales $",
            a.sales_diff as "Forecast Incremental Sales $",
            a.baseline_GM_$ as "Forecast Baseline GM $",
            a.forecast_GM_$ as "Forecast GM $",
            a.margin_diff as "Forecast Incremental Margin $",
            b.Actual_UPAs as "Actual UPAS",
            b.Actual_MD as "Actual MD $",
            b.Actual_Sales_Units as "Actual Sales Units",
            b.Actual_Baseline_Sales_Units as "Actual Baseline Sales Units",
            b.actual_sales_unit_diff as "Actual Incremental Units",
            b.Actual_Sales_$ as "Actual Sales $",b.actual_baseline_revenue as "Actual Baseline Sales $",
            b.Actual_Incremental_Revenue as "Actual Incremental Sales $",
            b.Actual_Baseline_Margin as "Actual Baseline GM $",
            b.Actual_Baseline_Margin + b.Actual_Incremental_Margin  as "Actual GM $",
            b.Actual_Incremental_Margin as "Actual Incremental Margin $"
            from base as a
            left join base_2 as b
            on a.Start_Date=b.start_date
            and a.End_Date=b.end_date
            and a.Sku=b.item_id
            and upper(trim(a.offer_name))=upper(trim(b.offer_name))
            order by "Event Name","Preliminary Offer Name"
            $$$.format(
                    p1=" where event_id in ({?event_id}) " if len(parameter:event_id) > 0 else "",
                    p2=" and promo_id in ({?promo_id}) " if len(parameter:promo_id) > 0 else ""
            )
            """
        }
}