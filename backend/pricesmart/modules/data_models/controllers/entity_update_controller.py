import json
from datetime import datetime
from pricesmart_common.staticclass import StaticClass
from pricesmart_common.utils import get_str_repr
from pricesmart.common.response.response_model import CommonResponseDataModel
from enums.Enums import CRUDOperation, OperationStatus
from fastapi import APIRouter, Depends
from pricesmart.database.operations.exception_details import CodeException
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pricesmart.database.operations.postgres.query_executor import QueryExecutorPostgres
from pricesmart.modules.data_models.datamodel_models import DataModel, DataModelInput, ModelActions,ModelParameters, QueryData
import pricesmart.modules.data_models.constants as model_constants
from user_management.login.CustomRouteClass import ValidationErrorLoggingRoute
from pricesmart.common.response.process_response import send_response
from pricesmart.modules.data_models.datamodel_store.data_models_store import data_models
from pricesmart.modules.data_models.datamodel_store.query_store import query_store
from configuration.environment import environment

router = APIRouter()
query_executor_postgres = QueryExecutorPostgres()
@router.post(model_constants.MODEL_UPDATE_URL)
async def update_entity_model(
    request_input: DataModelInput
):
    objCommonResponseDataModel = await update_entity_data(request_input)
    if objCommonResponseDataModel is None:
        objCommonResponseDataModel = CommonResponseDataModel(tag= model_constants.MODULE_NAME , message=f"Data can not be updated at this moment! Try again in 30 sec.",status=OperationStatus.FAIL )
        return await send_response(objCommonResponseDataModel)
    return await send_response(objCommonResponseDataModel)

async def update_entity_data(request_input: DataModelInput):
        try:
            model_info = DataModel.parse_obj(data_models["models"][str(request_input.id)])
            # for pre_action in model_info.pre_actions:
            #     if pre_action.is_active:
            #         pass
            query, data_to_bind = process_input_get_parameterized_query(model_info, request_input.parameters)
            query_objects = parse_query_further_for_update(query, data_to_bind)
            output  = await query_executor_postgres.execute_non_query_multiple(query_objects,"bulk-update")
            return output
        except Exception as ex:
            return None

def parse_query_further_for_update(query:str, data:dict):
    query_objects = []
    for item in data:
        updated_query = query.format(updatable_columns=item["updatable_columns"] ,condition = item["condition"])
        query_objects.append(QueryObject(query=updated_query,data=item["data"]))
        #print (updated_query , item)
    return query_objects

def process_input_get_parameterized_query(model_info:DataModel, input_parameters:dict):
        query = get_query_from_store(model_info,input_parameters)
        data_to_bind = []
        for parameter in model_info.parameters:
            if(parameter.name in input_parameters):
                parameter_value = input_parameters[parameter.name]
                value_string = get_parameter_value_string(parameter.data_type,parameter.formate , parameter_value, parameter.default)
                if parameter.data_type != "record":
                    query = query.replace("{?" + parameter.name + "}" , value_string)
                else:
                    data_to_bind = value_string
        return query ,data_to_bind



def get_parameter_value_string(parameter_datatype : str,parameter_format:str, parameter_value: str, parameter_default = ""):

    parameter_value = evaluate_defaults(parameter_datatype, parameter_value, parameter_default)
    parameter_value_string = ""
    if parameter_datatype == "int" or parameter_datatype == "real" or parameter_datatype == "float":
        parameter_value_string = parse_int(parameter_format, parameter_value)
        return parameter_value_string
    elif parameter_datatype == "str":
        parameter_value_string = parse_string(parameter_format, parameter_value)
        return parameter_value_string
    elif parameter_datatype == "date":
        parameter_value_string = f"to_date('{parameter_value}' , '{parameter_format}')"
        return parameter_value_string
    elif parameter_datatype == "record":
        records_data = parse_records(parameter_value)
        return records_data

def parse_records(parameter_value):
    records_data = []
    if isinstance(parameter_value, list):
        
        for data_item in parameter_value:
            for items in data_item:
                condition = f"{items} = {get_str_repr(data_item[items])} "
                data_item.pop(items)
                break   
            updatable_columns = ', '.join([item +'=' + '%(' + item + ')s'  for item in data_item])
            columns = ', '.join([item for item in data_item])
            record_data = {"updatable_columns" : updatable_columns ,"columns" :columns , "data": data_item, "condition" :condition}
            #values  =  #'(' + ', '.join([ str(get_str_repr(value))  for value in data_item.values()]) + ')'
            records_data.append(record_data)
    return records_data

def parse_string(parameter_format, parameter_value):
    if parameter_format == "list":
        parameter_value_string = "'%s'" % "','".join(parameter_value)
    else:
        parameter_value_string = f"'{parameter_value}'"
    return parameter_value_string

def parse_int(parameter_format, parameter_value):
    if parameter_format == "list":
        parameter_value_string = ','.join(str(x) for x in parameter_value)
    else:
        parameter_value_string = parameter_value
    return parameter_value_string

def evaluate_defaults(parameter_datatype, parameter_value, parameter_default):
    try:
        if parameter_value == "default" and parameter_datatype == "date":
            default_start_date, default_end_date = StaticClass.get_default_dates()
            parameter_value =  eval(parameter_default)
    except Exception as ex:
        print (ex)
    return parameter_value
    
def get_query_from_store(model_info:DataModel,input_parameters:dict):

    final_id = model_info.query
    for parameter in model_info.parameters:
        if parameter.name in input_parameters:
            final_id = str(final_id).replace(f"parameter:{parameter.name}" , f"input_parameters[\"{parameter.name}\"]" )
        else:
            final_id = str(final_id).replace(f"parameter:{parameter.name}" , "{}" )
    final_query_id = eval(final_id)
    queries_store = QueryData.parse_obj(query_store[str(final_query_id)])
    query = queries_store.query
    return query