import json
from datetime import datetime
from pricesmart_common.staticclass import StaticClass
from pricesmart.common.response.response_model import CommonResponseDataModel
from enums.Enums import CRUDOperation, OperationStatus
from fastapi import APIRouter, Depends
from pricesmart.database.operations.exception_details import CodeException
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pricesmart.database.operations.postgres.query_executor import QueryExecutorPostgres
from pricesmart.modules.data_models.datamodel_models import DataModel, DataModelInput, ModelActions,ModelParameters, QueryData
import pricesmart.modules.data_models.constants as model_constants
from user_management.login.CustomRouteClass import ValidationErrorLoggingRoute
from pricesmart.common.response.process_response import send_response
from pricesmart.modules.data_models.datamodel_store.data_models_store import data_models
from pricesmart.modules.data_models.datamodel_store.query_store import query_store
from configuration.environment import environment
from ast import literal_eval


router = APIRouter()
query_executor_postgres = QueryExecutorPostgres()
@router.post(model_constants.MODEL_DATA_URL)
async def get_model_data(
    request_input: DataModelInput
):
    """
    The get_model_data function is used to fetch the model data from the database.
    It accepts a DataModelInput object as an input and returns a CommonResponseDataModel object as output.
    The function first checks if the model id exists in the database, if not it returns an error message with status code 404 else it fetches all data for that particular model id.
    
    :param request_input: DataModelInput: Pass the model id
    :param token_info: Pass the token information to the auth_handler
    :return: The data of the model with the given id
    :author: Arun Kumar[1533]
    """
    
    objCommonResponseDataModel = await get_data_using_query(request_input)
    if isinstance(objCommonResponseDataModel, dict):
        objCommonResponseDataModel = objCommonResponseDataModel['model_response_data']
    if objCommonResponseDataModel is None:
        objCommonResponseDataModel = CommonResponseDataModel(tag= model_constants.MODULE_NAME , message=f"Model id : {request_input.id} is not present.",status=OperationStatus.FAIL )
        return await send_response(objCommonResponseDataModel)
    return await send_response(objCommonResponseDataModel)


async def get_data_using_query(request_input, output_format= None):
    """
    The get_data_using_query function is used to fetch data from the database using a query.
    The function accepts a request_input object as an input and returns a CommonResponseDataModel object with the response message, status code and exception details.
    
    :param request_input: Pass the input to the query_executor_postgres
    :return: A commonresponsedatamodel object
    :author: Arun Kumar[1533]
    """
    CommonResponse = None
    objCommonResponseDataModel = await get_data_model_query(request_input)
    if isinstance(objCommonResponseDataModel, dict):
        CommonResponse = objCommonResponseDataModel
        objCommonResponseDataModel = CommonResponse['model_response_data']
    
    if objCommonResponseDataModel is not None and not objCommonResponseDataModel.data.empty and objCommonResponseDataModel.status == OperationStatus.SUCCESS:
        objCommonResponseDataModel.message = "Model data fetched successfully "
        objCommonResponseDataModel.exception = CodeException()
    return CommonResponse if CommonResponse else objCommonResponseDataModel


async def get_data_model_query(request_input: DataModelInput):
    """
    The get_data_model_query function takes in a DataModelInput object and returns the query associated with that data model.
    The function first checks if the data model exists, then it processes the input parameters to create a parameterized query.
    
    :param request_input: DataModelInput: Get the id of the model that is being queried
    :return: A query that is parameterized based on the input parameters
    :author: Arun Kumar[1533]
    """
    
    try:
        model_info = DataModel.parse_obj(data_models["models"][str(request_input.id)])
        pre_action_output = []
        for pre_action in model_info.pre_actions:
            if pre_action.is_active:
                preactionCommonResponseDataModel = await execute_pre_actions(pre_action, request_input.parameters)
                if pre_action.output_parameter:
                    request_input.parameters[pre_action.output_parameter] = preactionCommonResponseDataModel.data[pre_action.output_parameter]
                else:
                    request_input.parameters['pre_action_output'] = preactionCommonResponseDataModel.data
                    pre_action_output.append(preactionCommonResponseDataModel)
        query = process_input_get_parameterized_query(model_info, request_input.parameters)

        query_object = QueryObject(query=query)
        objCommonResponseDataModel = await query_executor_postgres.get_data_async(query_object)
        if objCommonResponseDataModel.data.empty:
            objCommonResponseDataModel.status = OperationStatus.SUCCESS
            objCommonResponseDataModel.message = "Data not available"
            objCommonResponseDataModel.exception = CodeException()

        if pre_action_output:
            # pre_action_output.data = request_input.parameters['pre_action_output']  # this is done because the value of pre_action_output was being overwritten after main model query execution
            CommonResponseDict = {'pre_action_data': pre_action_output, 'model_response_data': objCommonResponseDataModel}
            return CommonResponseDict
        return objCommonResponseDataModel
    except Exception as ex:
        print(ex)
        print(f"Model did not exist::{request_input.id}") 
        return None
    

def process_input_get_parameterized_query(model_info:DataModel, input_parameters:dict):
    """
    The process_input_get_parameterized_query function takes in a DataModel and a dictionary of parameters.
    It then replaces all the parameter placeholders with their values, and returns the resulting query.
    
    :param model_info:DataModel: Get the query and parameter information
    :param parameters:dict: Get the values of the parameters in the query
    :return: A string containing the query with parameter values inserted
    :author: Arun Kumar[1533]
    """
    query = get_query_from_store(model_info, input_parameters)
    return query

def replace_parameters(model_info, input_parameters, query):
    for parameter in model_info.parameters:
        if(parameter.name in input_parameters):
            parameter_value = input_parameters[parameter.name]
            value_string = get_parameter_value_string(parameter.data_type,parameter.formate , parameter_value, parameter.default)
            query = query.replace("{?" + parameter.name + "}" , value_string)
    #print(query)
    return query

def get_parameter_value_string(parameter_datatype : str,parameter_format:str, parameter_value: str, parameter_default = ""):
    """
    The get_parameter_value_string function takes a parameter and its value as input.
    It then checks the data type of the parameter and converts it to a string that can be used in SQL queries.
    If the data type is int, real or float, it returns just the value of that parameter. If it is str, 
    it checks if there are multiple values for this string (i.e., if formate = list). If so, 
    it joins them with commas into one single comma-separated string which will be enclosed in single quotes 
    and returned by get_parameter_value_string function.
    
    :param parameter : ModelParameters: Get the data type of the parameter
    :param parameter_value: str: Store the value of each parameter
    :return: The value of the parameter in string format
    :author: Arun Kumar[1533]
    """
    try:
        if parameter_value == "default" and parameter_datatype == "date":
            default_start_date, default_end_date = StaticClass.get_default_dates()
            parameter_eval_value = eval(parameter_default)
            parameter_value = datetime.strftime(parameter_eval_value, '%m/%d/%Y') 
    except Exception as ex:
        print (ex)

    parameter_value_string = ""
    if parameter_datatype == "int" or parameter_datatype == "real" or parameter_datatype == "float":
        if parameter_format == "list":
            parameter_value_string = ','.join(str(x) for x in parameter_value)
        else:
            parameter_value_string = parameter_value
        return parameter_value_string
    elif parameter_datatype == "str":
        if parameter_format == "list":
            parameter_value_string = "'%s'" % "','".join(parameter_value)
        else:
            parameter_value_string = f"'{parameter_value}'"
        return parameter_value_string
    elif parameter_datatype == "date":
        parameter_value_string = f"to_date('{parameter_value}' , '{parameter_format}')"
        return parameter_value_string

def evaluate_query_string(model_info:DataModel , query : str,input_parameters:dict):
    final_query = evaluate_string_code(model_info,input_parameters,f"{query}")
    return final_query


def get_query_from_store(model_info:DataModel,input_parameters:dict):
    """
    The get_query_from_store function takes in a query_id and returns the corresponding query from the store.
    The get_query_from_store function is used by other functions to retrieve queries from the store.
    
    :param query_id:int: Identify the query in the store
    :return: The query from the store
    :author: Arun Kumar[1533]
    """
    raw_query = model_info.query
    final_query_id = evaluate_string_code(model_info, input_parameters, raw_query)
    print("query in use :: " , final_query_id)
    queries_store = QueryData.parse_obj(query_store[str(final_query_id)])
    store_query = replace_parameters(model_info, input_parameters, queries_store.query)
    evaluated_query = evaluate_query_string(model_info , store_query,input_parameters)
    print ("final query ::" , evaluated_query)
    return evaluated_query

def evaluate_string_code(model_info, input_parameters, raw_query):
    for parameter in model_info.parameters:
        if parameter.name in input_parameters:
            raw_query = str(raw_query).replace(f"parameter:{parameter.name}" , f"input_parameters[\"{parameter.name}\"]" )
        else:
            raw_query = str(raw_query).replace(f"parameter:{parameter.name}" , "{}" )
    raw_query = str(raw_query).replace("$$$", "\"\"\"")
    try:
        raw_query = raw_query.replace("\t", " ")
        raw_query = raw_query.replace("\n", " ")
        evaluated_query = eval(raw_query)
    except Exception as ex:
        evaluated_query = raw_query
        print(ex)
    return evaluated_query


async def execute_pre_actions(pre_action:ModelActions, parent_parameters):
    if(pre_action.type=="model"):
        objDataModelInput = DataModelInput(id=pre_action.identifier)
        if(pre_action.pass_parameters):
            objDataModelInput.parameters = parent_parameters
        return await get_data_using_query(objDataModelInput)