
{
        "id" : -1
        "operation": 1, ### insert
        "tag":"events",
        "selected_filters" : <dictionary> ## mandatory
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## mandatory
        "data_filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}
{
        "id" : 121,
        "operation": 2, ### Update
        "tag":"events",
        "selected_filters" : <dictionary> ## mandatory
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## Mandatory
        "data_filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}

{
        "id" : 121,
        "operation": 3, ### Delete
        "tag":"events",
        "selected_filters" : <dictionary> ## mandatory
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## null
        "data_filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}

{
        "id" : 11, ## -1 if you want to get the data based on filter provided
        "operation": 4, ## get data
        "tag":"events",
        "selected_filters" : <dictionary> ## mandatory
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## Null
        "data_filter" :<dictionary> ---- optional
}