from datetime import datetime, timed<PERSON>ta
from typing import Dict, List

import pricesmart.modules.events.events_constants as events_constants
from configuration.environment import environment
from enums.Enums import CRUDOperation, OperationStatus
from logger.logger import logger
from messages_collection.api_messages import api_messages, validation_message
from multimethod import multimethod
from pandas import DataFrame
from pricesmart.common.requests.request_model import CommonRequestDataModel
from pricesmart.common.response.response_model import CommonResponseDataModel
from pricesmart.database.operations.postgres.crud_operations import (
    PerformCurdOperations,
)
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pricesmart.database.operations.postgres.query_executor import QueryExecutorPostgres
from pricesmart.modules.events.entities.event_backup import EventBackupMeta
from pricesmart.modules.events.entities.event_promo_master import EventPromoMasterMeta
from pricesmart.modules.events.entities.event_store import EventStoreMeta
from pricesmart.modules.events.entities.events_master import EventMasterMeta
from pricesmart.modules.events.entities.notification_master import (
    NotificationMasterMeta,
)
from pricesmart.modules.events.entities.product_hierarchy import ProductHierarchyMeta
from pricesmart.modules.events.entities.store_hierarchy import StoreHierarchyMeta
from pricesmart.modules.events.events_model import (
    EventEditValidate,
    EventMasterGet,
    EventRequest,
    QueryExecutionObject,
)
from pricesmart.modules.notification.notification_models import NotificationInsertModel
from pricesmart_common import constants as global_constants
from pricesmart_common.decorators.decorators import FunctionInfo, function_time_logging
from pricesmart_common.system_parameters import ParameterProcessing


class ProcessEventsRequests:
    def __init__(self):
        """
        The __init__ function is the constructor for a class. It is called whenever an instance of a class is created.
        The __init__ function allows us to set attributes on our object, and can also establish default behaviors or settings for our object.

        :param self: Refer to the object itself
        :return: An instance of the event database class
        :author: Arun Kumar[1533]
        """

        self.event_database = PerformCurdOperations()
        self.query_executor_postgres = QueryExecutorPostgres()

    def get_processed_message(self, message, event_incoming_data, module_data=None):
        """
        The get_processed_message function is used to replace the placeholders in the message with actual values.
        The placeholders are identified by '{?attribute_name}' and replaced with actual value of attribute name.


        :param self: Access the attributes and methods of the class inside a method
        :param operation: Get the name of the operation that is being processed
        :param messages: Store the message that is to be sent
        :param event_incoming_data: Pass the event data to the function
        :return: A string that is the message to be sent to the user
        :author: Arun Kumar[1533]
        """

        processed_message = message
        data_for_parameters = {}
        try:
            if isinstance(event_incoming_data, list):
                data_for_parameters["attributes"] = event_incoming_data[0].attributes
            elif isinstance(event_incoming_data, CommonRequestDataModel):
                data_for_parameters["attributes"] = event_incoming_data.attributes
            else:
                data_for_parameters["attributes"] = event_incoming_data.dict()
            if len(data_for_parameters) > 0:
                for item in data_for_parameters:
                    for attribute in (
                        item.attributes
                        if hasattr(item, "attributes")
                        else data_for_parameters[item]
                    ):
                        attribute_value = (
                            str(item.attributes[attribute])
                            if hasattr(item, "attributes")
                            else str(data_for_parameters[item][attribute])
                        )
                        processed_message = processed_message.replace(
                            "{?" + attribute + "}", f"'{attribute_value}'"
                        )
            if module_data is not None and len(module_data) > 0:
                for item in module_data:
                    for attribute in module_data[item]:
                        attribute_value = str(module_data[item][attribute])
                        processed_message = processed_message.replace(
                            "{?" + attribute + "}", f"'{attribute_value}'"
                        )
        except Exception as ex:
            print("Error in parsing the message", ex)

        if "{?" in processed_message:
            processed_message = processed_message[str(processed_message).find(",")]
        return processed_message

    def process_message_for_parameters(
        self, request_data_dict, objCommonResponseDataModel
    ):
        """
        The process_message_for_parameters function is used to replace the parameters in the message with their values.
        For example, if a message has {?name} and {?age}, then this function will replace these parameters with their values.
        The process_message_for_parameters function takes two arguments: request data dictionary and CommonResponseDataModel object.

        :param request_data_dict: Get the values of all parameters in the message
        :param objCommonResponseDataModel: Store the response message
        :return: The message string with the parameters replaced by their values
        :author: Arun Kumar[1533]
        """
        for item in request_data_dict:
            objCommonResponseDataModel.message = (
                objCommonResponseDataModel.message.replace(
                    "{?" + item.replace("'", "") + "}",
                    f"'{str(request_data_dict[item])}'",
                )
            )

    async def verify_input(
        self, event_data, operation: CRUDOperation, module_info=EventMasterMeta()
    ):
        """
        The verify_input function is used to verify the input data for a given event.
        It checks if the event_id exists in the database and also checks if all required parameters are present.
        If any of these conditions fail, it returns an error message with appropriate status code.

        :param event_data: Store the event data
        :param operation:CRUDOperation: Determine if the event is being created, updated or deleted
        :return: A TempResponse object
        :author: Arun Kumar[1533]
        """
        module_data = await self.get_module_data_based_on_id(
            event_data.event_id, module_info
        )
        objTempResponse = self.check_event(event_data, module_data, operation)
        return objTempResponse

    def check_event(self, event_data, module_data, operation: CRUDOperation):
        """
        The check_event function checks if the requested event is available in the database.
        If it is not available, then it returns a message to the user that says that this event does not exist.
        If it exists, then check_event returns True.

        :param event_data: Store the event data that is fetched from the database
        :param module_data: Get the event_id from the database
        :param operation:CRUDOperation: Determine whether the operation is create, update or delete
        :return:  A TempResponse object
        :author: Arun Kumar[1533]
        """
        tempCommonResponseDataModel = CommonResponseDataModel(
            id=event_data.event_id, tag=events_constants.MODULE_NAME
        )
        tempCommonResponseDataModel.status = OperationStatus.SUCCESS
        try:
            if module_data is None or module_data == {}:
                tempCommonResponseDataModel.message = self.get_processed_message(
                    validation_message[events_constants.MODULE_NAME]["NONE"]["NULL"],
                    event_data,
                    module_data,
                )
                tempCommonResponseDataModel.status = OperationStatus.OPERATION_ABORTED
                return tempCommonResponseDataModel
            if (
                operation == CRUDOperation.LOCKED and module_data[0]["status"] == 1
            ) or (
                operation == CRUDOperation.UNLOCKED and module_data[0]["status"] == 0
            ):
                tempCommonResponseDataModel.message = self.get_processed_message(
                    validation_message[events_constants.MODULE_NAME][
                        operation.name.upper()
                    ][operation.name.upper()],
                    event_data,
                    module_data,
                )
                tempCommonResponseDataModel.status = OperationStatus.OPERATION_ABORTED
                return tempCommonResponseDataModel
            if module_data[0]["is_deleted"] == 1:
                tempCommonResponseDataModel.message = self.get_processed_message(
                    validation_message[events_constants.MODULE_NAME]["DELETED"][
                        operation.name.upper()
                    ],
                    event_data,
                    module_data,
                )
                tempCommonResponseDataModel.status = OperationStatus.OPERATION_ABORTED
                return tempCommonResponseDataModel
        except Exception as ex:
            print(ex)
            logger.info(f" {environment.LOG_TITLE}: 5000 {ex}")
        return tempCommonResponseDataModel

    async def process_request_and_get_result(
        self,
        event_data: dict,
        dml_operation: CRUDOperation,
        data_id,
        actual_operation: CRUDOperation,
    ):
        """
        The process_request_and_get_result function is used to process the request and return the response.
        It accepts event_data, dml_operation, data_id and actual operation as parameters.
        event_data: It contains all the incoming data for processing.
        dml_operation: It contains CRUDOperation class object which helps in determining what type of operation is it (CRUD).
        data id : Id of a particular record in database table based on which we will perform our operations like update or delete etc..
        actual operation : This parameter tells us that what actually this function does i.e., whether it's create/update/delete

        :param event_data:dict: Pass the data to be processed
        :param dml_operation :CRUDOperation: Determine the type of operation to be performed
        :param data_id:int: Identify the data in the database
        :param actual_operation :CRUDOperation: Determine whether the operation is a create, update or delete
        :return: The response object of the api_calling_interface function
        :author: Arun Kumar[1533]
        """
        event_incoming_data = self.prepare_data(
            event_data, dml_operation, data_id, actual_operation
        )
        objCommonResponseDataModel = await self.api_calling_interface(
            event_incoming_data
        )
        final_objCommonResponseDataModel = None
        if (
            isinstance(objCommonResponseDataModel, list)
            and len(objCommonResponseDataModel) > 0
        ):
            final_objCommonResponseDataModel = list(
                filter(
                    None,
                    map(
                        lambda n: n
                        if n.status == OperationStatus.FAIL
                        else objCommonResponseDataModel[0],
                        objCommonResponseDataModel,
                    ),
                )
            )[0]
        else:
            final_objCommonResponseDataModel = objCommonResponseDataModel
        if final_objCommonResponseDataModel.status == OperationStatus.FAIL:
            logger.info(
                f" {environment.LOG_TITLE}: {final_objCommonResponseDataModel.exception}"
            )

        self.set_final_message(
            final_objCommonResponseDataModel, actual_operation, event_incoming_data
        )
        return final_objCommonResponseDataModel

    def set_final_message(
        self, objCommonResponseDataModel, operation: CRUDOperation, event_incoming_data
    ):
        """
        The set_final_message function is used to set the final message for a successful or unsuccessful request.
        It takes in an object of type CommonResponseDataModel and sets the success/failure messages based on whether
        the operation was a success or failure. It also sets the status code accordingly.

        :param objCommonResponseDataModel: Set the response data model
        :param operation :CRUDOperation: Set the message based on the operation performed
        :return: The final message to be sent to the client
        :author: Arun Kumar[1533]
        """
        if objCommonResponseDataModel.status == OperationStatus.SUCCESS:
            success_messages = api_messages[events_constants.MODULE_NAME][
                OperationStatus.SUCCESS.name.upper()
            ]
            self.set_success_message(
                objCommonResponseDataModel,
                operation,
                success_messages,
                event_incoming_data,
            )
        elif (
            objCommonResponseDataModel.status == OperationStatus.SUCCESS_WITH_NO_RETURN
        ):
            success_messages = api_messages[events_constants.MODULE_NAME][
                OperationStatus.SUCCESS_WITH_NO_RETURN.name.upper()
            ]
            self.set_success_message(
                objCommonResponseDataModel,
                operation,
                success_messages,
                event_incoming_data,
            )
        else:
            failure_messages = api_messages[events_constants.MODULE_NAME][
                OperationStatus.FAIL.name.upper()
            ]
            self.set_failure_message(
                objCommonResponseDataModel,
                operation,
                failure_messages,
                event_incoming_data,
            )

    def set_failure_message(
        self,
        objCommonResponseDataModel,
        operation: CRUDOperation,
        messages: dict,
        event_incoming_data,
    ):
        """
        The set_failure_message function is used to set the message in CommonResponseDataModel object.
            Parameters:
                objCommonResponseDataModel (CommonResponseDataModel): The CommonResponseDataModel object which needs to be modified.
                operation (CRUDOperation): The CRUDOperation enum value that represents the type of operation being performed on a resource.  This will determine what message should be returned for a failure case.  For example, if an insert fails, then this function will set the message property of objCommonResponseDataModel with whatever string is passed in as messages[operation].name().upper() where messages is a dictionary and operation

        :param objCommonResponseDataModel: Set the message property of the response model
        :param operation:CRUDOperation: Set the message in the commonresponsedatamodel object
        :param messages:dict: Set the message for each crud operation
        :return: The message dictionary
        :author: Arun Kumar[1533]
        """
        objCommonResponseDataModel.message = self.get_processed_message(
            str(messages[operation.name.upper()]), event_incoming_data
        )

    def set_success_message(
        self,
        objCommonResponseDataModel: CommonResponseDataModel,
        operation: CRUDOperation,
        messages: dict,
        event_incoming_data,
    ):
        """
        The set_success_message function is used to set the success message in the response.
        The function takes three parameters:
            1) objCommonResponseDataModel - This is a CommonResponseDataModel object which will be used to set the success message.
            2) operation - This parameter specifies what type of CRUD operation was performed by the API call (create, read, update or delete).  The value can either be an instance of CRUDOperation class or a string containing one of these values: &quot;CREATE&quot;, &quot;READ&quot;, &quot;UPDATE&quot; and &quot;DELETE&quot;.  If this parameter is not specified then it defaults to None and

        :param objCommonResponseDataModel: Store the response data
        :param operation:CRUDOperation: Set the message for the success or failure of a crud operation
        :param messages:dict: Store the message for each crud operation
        :return: The message for the given operation
        :author: Arun Kumar[1533]
        """

        objCommonResponseDataModel.message = self.get_processed_message(
            str(messages[operation.name.upper()]), event_incoming_data
        )

    @FunctionInfo.function_time_logging
    def prepare_data(
        self,
        event_data,
        curd_operation: CRUDOperation,
        data_id: int = -1,
        actual_operation=CRUDOperation.NONE,
    ):
        """
        The prepare_data function takes in the event_data and curd_operation as input.
        It returns a list of CommonRequestDataModel objects which are used to create/update/delete data in the database.

        :param self: Access variables that belongs to the class
        :param event_data: Pass the data to be inserted into the database
        :param curd_operation:CRUDOperation: Determine the type of operation that is being performed on the data
        :param data_id:int: Identify the data to be updated
        :return: A list of commonrequestdatamodel objects
        :author: Arun Kumar[1533]
        """

        event_incoming_data = []
        if curd_operation == CRUDOperation.INSERT:
            events_master_attributes_request_model = (
                self.get_relevant_object_for_processing(
                    event_data,
                    data_id,
                    "attributes",
                    1,
                    "event_id",
                    EventMasterMeta(),
                    curd_operation,
                )
            )
            events_product_hierarchy_attributes_request_model = (
                self.get_relevant_object_for_processing(
                    event_data,
                    data_id,
                    "product_hierarchy",
                    2,
                    "event_id",
                    ProductHierarchyMeta(),
                    curd_operation,
                )
            )
            store_hierarchy_request_model = self.get_relevant_object_for_processing(
                event_data,
                data_id,
                "store_hierarchy",
                3,
                "event_id",
                StoreHierarchyMeta(),
                curd_operation,
            )
            event_store_request_model = self.get_relevant_object_for_processing(
                event_data,
                data_id,
                "event_stores",
                4,
                "event_id",
                EventStoreMeta(),
                curd_operation,
            )
            event_incoming_data = [
                events_master_attributes_request_model,
                events_product_hierarchy_attributes_request_model,
                store_hierarchy_request_model,
                event_store_request_model,
            ]
            return event_incoming_data
        if curd_operation == CRUDOperation.UPDATE:
            # event_master_backup_request_model = self.get_relevant_object_for_processing(event_data, data_id, "", 1, "event_id", EventBackupMeta(), CRUDOperation.INSERT)
            # event_incoming_data.append(event_master_backup_request_model)

            if "update_flag" in event_data and event_data["update_flag"]:
                event_data.pop("update_flag", None)
                event_master_backup_request_model = (
                    self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "",
                        1,
                        "event_id",
                        EventBackupMeta(),
                        CRUDOperation.INSERT,
                    )
                )
                event_incoming_data.append(event_master_backup_request_model)
                promo_data_attributes = {
                    "attributes": {
                        "is_under_processing": 1,
                        "updated_by": event_data["attributes"]["updated_by"],
                        "updated_at": datetime.now(),
                    }
                }
                promo_master_attributes_request_model = (
                    self.get_relevant_object_for_processing(
                        promo_data_attributes,
                        data_id,
                        "",
                        9,
                        "event_id",
                        EventPromoMasterMeta(),
                        curd_operation,
                    )
                )
                event_incoming_data.append(promo_master_attributes_request_model)

            if "attributes" in event_data and event_data["attributes"] is not None:
                events_master_attributes_request_model = (
                    self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "",
                        2,
                        "event_id",
                        EventMasterMeta(),
                        curd_operation,
                    )
                )
                event_incoming_data.append(events_master_attributes_request_model)
                if event_data["attributes"].get("ad_type") is not None:
                    promo_data_attributes = {
                        "attributes": {"ad_type": event_data["attributes"]["ad_type"]}
                    }
                    event_incoming_data.append(
                        self.get_relevant_object_for_processing(
                            promo_data_attributes,
                            data_id,
                            "",
                            9,
                            "event_id",
                            EventPromoMasterMeta(),
                            curd_operation,
                        )
                    )
                if actual_operation == CRUDOperation.DELETE:
                    promo_data_attributes = {}
                    promo_data = {
                        "status": 6,
                        "updated_by": event_data["attributes"]["updated_by"],
                        "updated_at": datetime.now(),
                    }
                    promo_data_attributes["attributes"] = promo_data
                    promo_master_attributes_request_model = (
                        self.get_relevant_object_for_processing(
                            promo_data_attributes,
                            data_id,
                            "",
                            2,
                            "event_id",
                            EventPromoMasterMeta(),
                            curd_operation,
                        )
                    )
                    event_incoming_data.append(promo_master_attributes_request_model)

            if (
                "product_hierarchy" in event_data
                and event_data["product_hierarchy"] is not None
            ):
                events_product_hierarchy_delete_model = (
                    self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "product_hierarchy",
                        3,
                        "event_id",
                        ProductHierarchyMeta(),
                        CRUDOperation.DELETE,
                    )
                )
                if actual_operation != CRUDOperation.DELETE:
                    events_product_hierarchy_attributes_request_model = (
                        self.get_relevant_object_for_processing(
                            event_data,
                            data_id,
                            "product_hierarchy",
                            5,
                            "event_id",
                            ProductHierarchyMeta(),
                            CRUDOperation.INSERT,
                        )
                    )
                    event_incoming_data.append(
                        events_product_hierarchy_attributes_request_model
                    )
                event_incoming_data.append(events_product_hierarchy_delete_model)

            if (
                "store_hierarchy" in event_data
                and event_data["store_hierarchy"] is not None
            ):
                store_hierarchy__delete_request_model = (
                    self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "store_hierarchy",
                        4,
                        "event_id",
                        StoreHierarchyMeta(),
                        CRUDOperation.DELETE,
                    )
                )
                if actual_operation != CRUDOperation.DELETE:
                    store_hierarchy_request_model = (
                        self.get_relevant_object_for_processing(
                            event_data,
                            data_id,
                            "store_hierarchy",
                            6,
                            "event_id",
                            StoreHierarchyMeta(),
                            CRUDOperation.INSERT,
                        )
                    )
                    event_incoming_data.append(store_hierarchy_request_model)
                event_incoming_data.append(store_hierarchy__delete_request_model)

            if "event_stores" in event_data and event_data["event_stores"] is not None:
                event_store_delete_request_model = (
                    self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "event_stores",
                        7,
                        "event_id",
                        EventStoreMeta(),
                        CRUDOperation.DELETE,
                    )
                )
                if actual_operation != CRUDOperation.DELETE:
                    event_store_request_model = self.get_relevant_object_for_processing(
                        event_data,
                        data_id,
                        "event_stores",
                        8,
                        "event_id",
                        EventStoreMeta(),
                        CRUDOperation.INSERT,
                    )
                    event_incoming_data.append(event_store_request_model)
                event_incoming_data.append(event_store_delete_request_model)
            # event_incoming_data = [events_master_attributes_request_model,events_product_hierarchy_delete_model,store_hierarchy__delete_request_model,events_product_hierarchy_attributes_request_model,store_hierarchy_request_model]
            return event_incoming_data
        else:
            event_incoming_data = CommonRequestDataModel(
                id=data_id, operation=curd_operation, tag=events_constants.MODULE_NAME
            )
            event_incoming_data.attributes = event_data
            event_incoming_data.associated_module = EventMasterMeta()
        return [event_incoming_data]

    def get_relevant_object_for_processing(
        self,
        event_data,
        data_id,
        dict_key_name: str,
        sequence: int,
        parameter_column_name: str,
        object: any,
        curd_operation: CRUDOperation,
    ):
        events_request_model = CommonRequestDataModel(
            id=data_id, operation=curd_operation, tag=events_constants.MODULE_NAME
        )
        events_request_model.attributes = (
            event_data[dict_key_name] if dict_key_name != "" else event_data
        )
        events_request_model.execution_sequence = sequence
        events_request_model.parameter_column_name = parameter_column_name
        events_request_model.dependent_parameter_name = None
        events_request_model.associated_module = object
        return events_request_model

    async def api_calling_interface(
        self, event_incoming_data: List[CommonRequestDataModel]
    ):
        """
        The api_calling_interface function is the main function that will be called by the api gateway.
        It takes in an event object as input and returns a response object to the caller.
        The event object contains all of the data passed along with a request from API Gateway, including
        the path parameters, query string parameters, headers (e.g., Content-Type), and other request information.

        :param event_incoming_data: Store the event data which is received from the client
        :return: The response to the client
        :author: Arun Kumar[1533]
        """
        # if event_incoming_data.attributes != None and event_incoming_data.attributes != {}:
        #     await self.check_and_process_system_parameters(event_incoming_data)
        objProcessRequest = ProcessEventsRequests()
        objCommonResponseDataModel = await objProcessRequest.process_incoming_data(
            event_incoming_data
        )
        return objCommonResponseDataModel

    async def check_and_process_system_parameters(
        self, event_incoming_data: CommonRequestDataModel
    ):
        """
        The check_and_process_system_parameters function is used to check if the incoming request contains any system parameters.
        If it does, then those parameters are processed and replaced with their corresponding values.

        :param event_incoming_data: Pass the event data to the function
        :return: The processed data attributes
        :author: Arun Kumar[1533]
        """
        try:
            objParameterProcessing = ParameterProcessing()
            for item in event_incoming_data.attributes:
                if str(event_incoming_data.attributes[item]).__contains__("{?"):
                    event_incoming_data.attributes[
                        item
                    ] = await objParameterProcessing.process_system_parameters(
                        event_incoming_data.attributes[item]
                    )
                print(item, event_incoming_data.attributes[item])
        except Exception as ex:
            logger.info(f" {environment.LOG_TITLE}: {ex}")

    async def get_module_data_based_on_id(self, _id: int, module_info):
        """
        The get_module_data_based_on_id function is used to get the data from the database based on id.
        It accepts an integer as a parameter and returns a dictionary of values

        :param _id :int: Get the data of a particular module based on its id
        :return: The module data based on the id passed as a parameter
        :author: Arun Kumar[1533]
        """
        event_incoming_data = CommonRequestDataModel(
            id=_id,
            operation=CRUDOperation.GET,
            tag=events_constants.MODULE_NAME,
            associated_module=module_info,
        )
        objProcessRequest = ProcessEventsRequests()
        responseObject = await objProcessRequest.process_incoming_data(
            [event_incoming_data]
        )
        if isinstance(responseObject.data, DataFrame):
            df = DataFrame(responseObject.data)
            if df[df.columns[0]].count() > 0:
                return responseObject.data.to_dict("index")
        else:
            return None

    async def process_incoming_data(
        self, request_input_data: List[CommonRequestDataModel]
    ):
        """
        The process_incoming_data function is the main function that handles all incoming requests.
        It takes in a request object and returns a response object. The request object contains the
        operation type, which is either insert, update, delete or get. Depending on what operation type
        the input data has it will call the appropriate database function to handle it.

        :param self: Access the class attributes and methods
        :param input_data:CommonRequestDataModel: Pass the data that is being processed by the function
        :return: An eventresponsedatamodel object
        :author: Arun Kumar[1533]
        """
        request_input_data.sort(key=lambda x: x.execution_sequence)
        query_collection = []
        if request_input_data:
            for input_data in request_input_data:
                print(input_data)
                obj_query_execution_object = QueryExecutionObject()
                if input_data.operation == CRUDOperation.INSERT:
                    obj_query_details = await self.event_database.insert(input_data)
                    if obj_query_details:
                        obj_query_execution_object = (
                            await self.get_query_execution_object(
                                input_data, obj_query_details
                            )
                        )
                    else:
                        obj_query_execution_object = None
                if input_data.operation == CRUDOperation.UPDATE:
                    obj_query_details = await self.event_database.update(input_data)
                    obj_query_execution_object = await self.get_query_execution_object(
                        input_data, obj_query_details
                    )
                if input_data.operation == CRUDOperation.DELETE:
                    obj_query_details = await self.event_database.delete(input_data)
                    obj_query_execution_object = await self.get_query_execution_object(
                        input_data, obj_query_details
                    )
                if input_data.operation == CRUDOperation.GET:
                    if isinstance(input_data.attributes, EventRequest):
                        obj_query_details = await self.event_database.get(
                            input_data.attributes
                        )
                    elif isinstance(input_data.attributes, EventMasterGet):
                        obj_query_details = await self.event_database.get(
                            input_data.attributes.dict()
                        )
                    elif isinstance(input_data.attributes, EventEditValidate):
                        obj_query_details = await self.event_database.get(
                            input_data.attributes
                        )
                    elif isinstance(input_data, CommonRequestDataModel):
                        obj_query_details = await self.event_database.get(input_data)
                    else:
                        obj_query_details = await self.event_database.get(
                            input_data.attributes.dict()
                        )
                    obj_query_execution_object = await self.get_query_execution_object(
                        input_data, obj_query_details
                    )
                if obj_query_execution_object:
                    query_collection.append(obj_query_execution_object)

        result = await self.execute_queries(query_collection)
        return result

    async def get_query_execution_object(self, input_data, obj_query_details):
        """
        The get_query_execution_object function is used to create a QueryExecutionObject object.
        The function accepts the input_data and obj_query_details as parameters.
        The function returns an object of type QueryExecutionObject.

        :param self: Access the class variables
        :param input_data: Pass the operation that needs to be performed
        :param obj_query_details: Store the query details
        :return: A queryexecutionobject object
        :author: Arun Kumar[1533]
        """
        obj_query_execution_object = QueryExecutionObject(
            query_object=obj_query_details, action=input_data.operation
        )
        print(obj_query_details.query)
        return obj_query_execution_object

    async def execute_queries(self, query_collection: list):
        """
        The execute_queries function accepts a list of QueryObjects and executes them.
        It returns the result of the query execution to the caller.

        :param self: Access variables that belongs to the class
        :param query_collection:list: Pass a list of query objects to the execute_queries function
        :return: A list of objects
        :author: Arun Kumar[1533]
        """

        if len(query_collection) > 1:
            queries_to_execute = []
            for input in query_collection:
                queries_to_execute.append(input.query_object)
            return await self.query_executor_postgres.execute_non_query_multiple(
                queries_to_execute, "Event creation"
            )
        else:
            if query_collection[0].action == CRUDOperation.GET:
                return await self.query_executor_postgres.get_data_async(
                    query_collection[0].query_object
                )
            else:
                return await self.query_executor_postgres.execute_non_query(
                    query_collection[0].query_object
                )
        # return insert_result
