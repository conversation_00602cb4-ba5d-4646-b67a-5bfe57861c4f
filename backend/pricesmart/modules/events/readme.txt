
{
        "id" : -1
        "operation": 1, ### insert
        "tag":"events",
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## mandatory
        "filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}
{
        "id" : 121,
        "operation": 2, ### Update
        "tag":"events",
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## Mandatory
        "filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}

{
        "id" : 121,
        "operation": 3, ### Delete
        "tag":"events",
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## null
        "filter" :<dictionary> #### optional  if refresh_data is true and this is null the complete data will be returned
}

{
        "id" : 11, ## -1 if you want to get the data based on filter provided
        "operation": 4, ## get data
        "tag":"events",
        "refresh_data": "False",
        "data_attributes" : <dictionary> ## Null
        "filter" :<dictionary> ---- optional
}