
from pricesmart.modules.events.events_model import TransformationInfo


class ProductHierarchyMeta():
        def __init__(self):
                self.transformation_info = TransformationInfo()
                self.transformation_info.key_name = "hierarchy_level_name"
                self.transformation_info.Value_name = "hierarchy_name"
                self.transformation_info.dependent_key_column = "event_id"
                self.transformation_info.dependent_key_value = "?event_id"
                self.table_name = "event_hierarchy"
                self.id_column_name = "id"
                self.table_schema = "public"
                self.type_of_object = "product_hierarchy"
                self.type_of_object_column_name = "hierarchy_type"

