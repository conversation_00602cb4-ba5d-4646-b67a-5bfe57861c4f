from pricesmart.modules.events.events_model import TransformationInfo


class EventStoreMeta():
        def __init__(self):
                self.transformation_info = TransformationInfo()
                self.transformation_info.key_name = "store_id"
                self.transformation_info.Value_name = "store_id"
                self.transformation_info.dependent_key_column = "event_id"
                self.transformation_info.dependent_key_value = "?event_id"
                self.table_name = "event_store"
                self.id_column_name = "id"
                self.table_schema = "public"
                self.type_of_object = ""
                self.type_of_object_column_name = ""
                # self.sub_attribute_key_name = "store_h6"

