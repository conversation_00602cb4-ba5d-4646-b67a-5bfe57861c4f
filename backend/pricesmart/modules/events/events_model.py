from datetime import date
from typing import Any, List, Optional
from pydantic import BaseModel, validator
from pricesmart_common.staticclass import StaticClass
from pricesmart.common.FilterModel import FilterModel
from enums.Enums import CRUDOperation, Operator
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pydantic import BaseModel, validator


class EventMaster(BaseModel):
    event_code: Optional[str] = None
    campaign_id: Optional[int] = None
    name: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    customer_segment: Optional[str] = None
    description: Optional[str] = None
    channel: Optional[str] = None
    marketing_channel: Optional[str] = None
    submit_offer_by: Optional[str] = None
    marketing_notes: Optional[str] = None
    status: Optional[int] = None
    event_type: Optional[str] = None
    event_objective: Optional[str] = None
    event_objective_description: Optional[str] = None
    event_offer_type_id: Optional[int] = None
    channel_type: Optional[int] = None
    ad_type: Optional[int] = None
    min_percent_value: Optional[float] = None


class EventProductHierarchy(BaseModel):
    brand: Optional[list] = []
    product_h1: Optional[list] = []
    product_h2: Optional[list] = []
    product_h3: Optional[list] = []
    product_h4: Optional[list] = []
    product_h5: Optional[list] = []


class EventStoreHierarchy(BaseModel):
    store_h0: Optional[list] = []
    store_h1: Optional[list] = []
    store_h2: Optional[list] = []
    store_h3: Optional[list] = []
    store_h4: Optional[list] = []
    store_h5: Optional[list] = []
    store_h6: Optional[list] = []


class EventStores(BaseModel):
    store_id: Optional[List]


class EventMasterWithProductAndStoreHierarchy(BaseModel):
    attributes: EventMaster
    product_hierarchy: EventProductHierarchy
    store_hierarchy: EventStoreHierarchy
    event_stores: EventStores


default_start_date, default_end_date = StaticClass.get_quarter_dates()




class Calendar (BaseModel):
    campaign: Optional[list] = None
    event: Optional[list] = None
    promotion: Optional[list] = None
    start_date: Optional[date]= default_start_date
    end_date: Optional[date] = default_end_date

    @validator(*["start_date", "end_date"], pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            return StaticClass.get_parsed_date(v)
        return v


class EventFilters(BaseModel):
    product_hierarchy:Optional[EventProductHierarchy] = None
    store_hierarchy: Optional[EventStoreHierarchy] = None
    calendar : Optional[Calendar] = Calendar()
    channel_type:Optional[list] = None
    ad_type:Optional[list] = None

class EventMasterGet(BaseModel):
    filters: Optional[EventFilters] = None
    aggregations: Optional[str] = None
    show_last_year: Optional[bool] = False


class EventMasterDelete(BaseModel):
    event_id: int
    operator: Optional[int] = Operator.EQUAL.value
    # filter_string:Optional[List[FilterModel]] = list()


class EventMasterDeleteUpdate(BaseModel):
    event_ids: list


class EventMasterUpdate(BaseModel):
    event_id: int
    attributes: Optional[EventMaster] = None
    product_hierarchy: Optional[EventProductHierarchy] = None
    store_hierarchy: Optional[EventStoreHierarchy] = None
    event_stores: Optional[EventStores] = None


class EventMasterLock(BaseModel):
    event_ids: list
    is_locked: int


class EventRequest(BaseModel):
    event_ids: list
    with_promo: Optional[bool] = False
    with_product_hierarchy: Optional[bool] = False
    with_store_hierarchy: Optional[bool] = False
    return_type_flat: Optional[bool] = True


class TransformationInfo(BaseModel):
    key_name: Optional[str] = None
    Value_name: Optional[str] = None
    dependent_key_column: Optional[str] = None
    dependent_key_value: Optional[str] = None


class QueryExecutionObject(BaseModel):
    query_object: QueryObject
    action: CRUDOperation


class EventEditValidate(BaseModel):
    event_id: int
    ad_type: int
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    store_id: Optional[List[int]] = None
    product_h2_id: Optional[List[int]] = None
