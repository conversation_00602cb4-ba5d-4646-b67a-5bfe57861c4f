from datetime import datetime, timedelta
from json import dumps, loads
from uuid import uuid4

import pricesmart.modules.events.events_constants as events_constants
import pricesmart_common.constants as common_constants
import promotions.constants as promo_constants
import pytz
from app.main import scheduler
from configuration.environment import environment
from enums.Enums import CRUDOperation, OperationStatus
from fastapi import APIRouter, BackgroundTasks, Depends, Request
from logger.logger import logger
from pricesmart.common.requests.request_model import CommonRequestDataModel
from pricesmart.common.response.process_response import send_response
from pricesmart.common.response.response_model import CommonResponseDataModel
from pricesmart.modules.events.entities.events_master import EventMasterMeta
from pricesmart_common.utils import async_execute_query
from pricesmart.modules.events.event_queries import EventQueries
from pricesmart.modules.events.events_model import (
    Calendar,
    EventEditValidate,
    EventFilters,
    EventMaster,
    EventMasterDelete,
    EventMasterDeleteUpdate,
    EventMasterGet,
    EventMasterLock,
    EventMasterUpdate,
    EventMasterWithProductAndStoreHierarchy,
    EventRequest,
)
from pricesmart.modules.events.process_events_request import ProcessEventsRequests
from pricesmart.modules.notification.notification_operations import (
    NotificationOperations,
)
from pricesmart_common import constants as common_constants
from pricesmart_common import models as common_models
from pydantic import BaseModel
from server_sent_events.utils import SSEController
from user_management.login.CustomRouteClass import ValidationErrorLoggingRoute

router = APIRouter()
processRequest = ProcessEventsRequests()

sse_controller = SSEController()


@router.post(events_constants.EVENT_URL)
async def create_event_controller(
    event_data: EventMasterWithProductAndStoreHierarchy, request: Request
):
    """
    The create_event_controller function is used to create an event in the database.
    The function accepts a JSON object containing all of the necessary information needed to create an event.
    The function then calls the api_calling_interface function which returns a response from our API interface.

    :param event_data: EventMaster: Pass the event data to be inserted into the database
    :param token_info: User details fetched
    :param : Pass the data to the api_calling_interface function
    :return: The event_id of the newly created event
    :author: Arun Kumar[1533]
    """
    request_data_dict = exclude_optional_dict(event_data)
    request_data_dict["attributes"]["created_by"] = request.state.user_id
    objCommonResponseDataModel = await processRequest.process_request_and_get_result(
        request_data_dict, CRUDOperation.INSERT, -1, CRUDOperation.INSERT
    )
    processRequest.process_message_for_parameters(
        request_data_dict, objCommonResponseDataModel
    )
    await create_notification(
        objCommonResponseDataModel,
        f"An event created with the name {event_data.attributes.name}, Have a look !",
    )
    return await send_response(objCommonResponseDataModel)


@router.put(events_constants.EVENT_URL)
async def update_event_controller(
    event_data: EventMasterUpdate, background_task: BackgroundTasks, request: Request
):
    """
    The update_event_controller function is used to update an existing event in the database.
    The function accepts a single parameter, which is of type EventMasterUpdate.
    This object contains all the information required to update an event in the database.

    :param background_task:
    :param token_info:
    :param event_data: EventMasterUpdate: Pass the data to be updated
    :param : Update the event in the database
    :return: The response from the api_calling_interface function
    :author: Arun Kumar[1533]
    """
    request_data_dict = exclude_optional_dict(event_data)
    logger.info(
        f" {environment.LOG_TITLE}: Update event {request_data_dict['event_id']} - start"
    )
    if event_data.attributes is None:
        request_data_dict["attributes"] = {}
    request_data_dict["attributes"]["updated_by"] = request.state.user_id
    request_data_dict["attributes"]["updated_at"] = datetime.now()
    request_data_dict[
        "update_flag"
    ] = True  # added so that 'is_under_processing flag' gets updated only for event edit operation

    objTempResponse = await processRequest.verify_input(
        event_data, CRUDOperation.UPDATE
    )
    if (
        objTempResponse.status == OperationStatus.FAIL
        or objTempResponse.status == OperationStatus.OPERATION_ABORTED
    ):
        logger.info(
            f"{environment.LOG_TITLE}: Update event {request_data_dict['event_id']} - verify failed"
        )
        return await send_response(objTempResponse)

    objCommonResponseDataModel = await processRequest.process_request_and_get_result(
        request_data_dict,
        CRUDOperation.UPDATE,
        event_data.event_id,
        CRUDOperation.UPDATE,
    )
    processRequest.process_message_for_parameters(
        request_data_dict, objCommonResponseDataModel
    )
    logger.info(
        f"{environment.LOG_TITLE}: Update event {request_data_dict['event_id']} - success."
    )
    await send_event_update_notification(
        objCommonResponseDataModel, request_data_dict["event_id"]
    )

    logger.info(
        f"{environment.LOG_TITLE}: Adding bg task for event {request_data_dict['event_id']}"
    )
    background_task.add_task(
        auto_resimulate_after_event_edit, request_data_dict["event_id"]
    )

    logger.info(
        f"{environment.LOG_TITLE}: Sending response for update event {request_data_dict['event_id']}"
    )
    return await send_response(objCommonResponseDataModel)


async def send_event_update_notification(objCommonResponseDataModel, event_id):
    logger.info(
        f"{environment.LOG_TITLE}: Creating notification for update event {event_id}"
    )
    event_name = (
        await async_execute_query(
            f"""
        select name from event_master where event_id = {event_id}
        """
        )
    )[0]["name"]
    await create_notification(
        objCommonResponseDataModel,
        f"The event {event_name} has been updated , Have a look !",
    )
    logger.info(
        f"{environment.LOG_TITLE}: Notification created for update event {event_id}"
    )


async def auto_resimulate_after_event_edit(event_id):
    event_name = None
    event_queries = EventQueries()
    try:
        logger.info(
            f"{environment.LOG_TITLE}: Inside auto re-simulate bg task for event id: {event_id}"
        )
        query = event_queries.get_event_edit_change_indicator_query(event_id)
        result = await async_execute_query(query=query)
        indicators = result[0]
        event_name = indicators["event_name"]
        logger.info(f"{environment.LOG_TITLE}: {indicators}")
        removed_product_h2 = indicators["deleted_product_h2"]
        latest_ad_type = indicators["new_ad_type"]
        do_resimulation = False
        if (
            indicators["date_change_ind"] == 1
            or (indicators["product_hierarchy_change_ind"] == 1 and removed_product_h2)
            or indicators["store_hierarchy_change_ind"] == 1
        ):
            do_resimulation = True

        if do_resimulation:
            logger.info(
                f"{environment.LOG_TITLE}: Re-simulation to be done for promos in event id: {event_id}"
            )
            remove_promos_query = (
                event_queries.remove_promos_for_event_product_hierarchy_edit(
                    event_id, removed_product_h2
                )
                if removed_product_h2
                else ""
            )
            final_query = event_queries.resimulate_process_queries_for_event_edit(
                event_id, remove_promos_query, latest_ad_type
            )
            await async_execute_query(
                query=final_query, transaction_mode=True, timelimit=None
            )
        else:
            print("Event changes do not require re-simulation of promos")
            logger.info(
                f"{environment.LOG_TITLE}: Event changes do not require re-simulation of promos in event id: {event_id}"
            )
    except Exception as e:
        logger.info(
            f"{environment.LOG_TITLE}: Auto re-simulate of promos due to event edit encountered an issue.  Issue : {e}"
        )
        print(
            f"\n\nAuto re-simulate of promos due to event edit encountered an issue.  Issue : {e}"
        )
        await async_execute_query(
            event_queries.post_background_task_queries(event_id, -1)
        )
        if not event_name:
            event_name = (
                await async_execute_query(
                    f"select name from event_master where event_id = {event_id}"
                )
            )[0]["name"]

        logger.info(
            f"{environment.LOG_TITLE}: Sending failure notification for auto resimulate in event : {event_id}"
        )
        await send_notification(
            event_id,
            events_constants.AUTO_RESIMULATION_FAILURE_MESSAGE.format(
                event_name=event_name
            ),
        )
    else:
        logger.info(f"{environment.LOG_TITLE}: Inside else block")
        await async_execute_query(
            query=event_queries.post_background_task_queries(event_id)
        )
        logger.info(
            f"{environment.LOG_TITLE}: Post background task query done for event : {event_id}"
        )
        print("post background task query done")
        await send_notification(
            event_id,
            events_constants.AUTO_RESIMULATION_SUCCESS_MESSAGE.format(
                event_name=event_name
            ),
        )


async def send_notification(event_id, message):
    print("Sending notification")
    try:
        await NotificationOperations().save_notification_for_all_users(
            events_constants.EVENT_AUTORESIMULATION_MODULE,
            message,
            "navigate",
            navigate_to=event_id,
        )
        notification_data = {
            "action": "navigate",
            "created_at": datetime.now(
                tz=pytz.timezone(common_constants.CLIENT_TIMEZONE)
            ).strftime("%Y-%m-%d %H:%M:%S:%f"),
            "is_active": 1,
            "life_span": 3600,
            "message": message,
            "module": events_constants.EVENT_AUTORESIMULATION_MODULE,
            "navigate_to": event_id,
            "priority": 3,
            "read_at": None,
            "read_by": None,
            "severity": None,
            "uuid": "uuid",
            "status": 200,
        }
        sse_controller.broadcast(notification_data)
    except Exception:
        logger.exception(
            f"{environment.LOG_TITLE}: error when sending event auto resimulation notification"
        )


async def create_notification(objCommonResponseDataModel, message: str):
    try:
        if objCommonResponseDataModel.status == OperationStatus.SUCCESS:
            obj_notification_operations = NotificationOperations()
            return await obj_notification_operations.create_notification(
                events_constants.MODULE_NAME,
                message,
                "navigate",
                navigate_to=objCommonResponseDataModel.data["event_id"],
            )
    except Exception as ex:
        logger.info(
            f"{environment.LOG_TITLE}: Problem while creating the notification {ex}"
        )


def exclude_optional_dict(model: BaseModel):
    return {**model.dict(exclude_unset=True), **model.dict(exclude_none=True)}


@router.post(events_constants.SOFT_DELETE_EVENT_URL)
async def update_for_delete_event_controller(
    event_data: EventMasterDeleteUpdate, request: Request
):
    """
    The delete_event_controller function is used to delete an event from the database.
    The function accepts a JSON object containing the event data and returns a JSON response.

    :param event_data: EventMasterDelete: Pass the data of event to be deleted
    :param : Store the data that is to be deleted from the database
    :return: A list of events that were deleted
    :author: Arun Kumar[1533]
    """
    objCommonResponseDataModel = CommonResponseDataModel()
    messages = {}
    delete_column_json = {
        "is_deleted": 1,
        "updated_by": request.state.user_id,
        "updated_at": datetime.now(),
    }
    for event_id in event_data.event_ids:
        obj_event_master_update = EventMasterUpdate(
            event_id=event_id, attributes=delete_column_json
        )
        objTempResponse = await processRequest.verify_input(
            obj_event_master_update, CRUDOperation.DELETE
        )
        if objTempResponse.status == OperationStatus.OPERATION_ABORTED:
            return await send_response(objTempResponse)
        request_data_dict = exclude_optional_dict(obj_event_master_update)
        request_data_dict["attributes"] = delete_column_json
        objCommonResponseDataModel = (
            await processRequest.process_request_and_get_result(
                request_data_dict, CRUDOperation.UPDATE, event_id, CRUDOperation.DELETE
            )
        )
        processRequest.process_message_for_parameters(
            request_data_dict, objCommonResponseDataModel
        )
        messages[event_id] = objCommonResponseDataModel.message
    # objCommonResponseDataModel.message = json.dumps(messages)
    await create_notification(
        objCommonResponseDataModel, f"Event(s) deleted , Have a look !"
    )
    return await send_response(objCommonResponseDataModel)


@router.get(events_constants.EVENT_URL)
async def get_event_controller(event_data: EventMasterGet):
    """
    The get_event_controller function is used to get the event details from the database.
    The function accepts a JSON object as input and returns a JSON object as output.

    :param event_data: EventMaster: Pass the event data to the api_calling_interface function
    :param : Get the event data from the database
    :return: The event data from the database
    :author: Arun Kumar[1533]
    """
    fields_to_get = None
    if event_data.attributes is not None and event_data.attributes != {}:
        fields_to_get = event_data.attributes

    event_incoming_data = processRequest.prepare_data(
        fields_to_get, CRUDOperation.GET, event_data.event_id
    )
    objCommonResponseDataModel = await processRequest.api_calling_interface(
        event_incoming_data
    )
    return await send_response(objCommonResponseDataModel)


@router.post(events_constants.GET_EVENT_URL)
async def get_events_controller(event_data: EventMasterGet):
    """
    The get_event_controller function is used to get the event details from the database.
    The function accepts a JSON object as input and returns a JSON object as output.

    :param event_data: EventMaster: Pass the event data to the api_calling_interface function
    :param : Get the event data from the database
    :return: The event data from the database
    :author: Arun Kumar[1533]
    """
    logger.info(f"{environment.LOG_TITLE}: {event_data}")
    event_incoming_data = processRequest.prepare_data(event_data, CRUDOperation.GET, -1)
    objCommonResponseDataModel = await processRequest.api_calling_interface(
        event_incoming_data
    )
    return await send_response(objCommonResponseDataModel)


@router.put(events_constants.LOCK_EVENT_URL)
async def lock_event_controller(event_data: EventMasterLock, request: Request):
    """
    The lock_event_controller function is used to lock the event.

    :param event_data: EventMasterLock, Pass the event_id to verify_input function
    :return: A response object which contains the status of the request
    :author: Arun Kumar[1533]
    """
    operation_requested = CRUDOperation(7 if event_data.is_locked < 1 else 5)
    update_json = {"status": 0 if event_data.is_locked < 1 else 1}
    for event_id in event_data.event_ids:
        obj_event_master_update = EventMasterUpdate(
            event_id=event_id, attributes=update_json
        )
        request_data_dict = exclude_optional_dict(obj_event_master_update)
        request_data_dict["attributes"]["updated_by"] = request.state.user_id
        request_data_dict["attributes"]["updated_at"] = datetime.now()
        objTempResponse = await processRequest.verify_input(
            obj_event_master_update, operation_requested
        )
        if (
            objTempResponse.status == OperationStatus.FAIL
            or objTempResponse.status == OperationStatus.OPERATION_ABORTED
        ):
            return await send_response(objTempResponse)
        objCommonResponseDataModel = (
            await processRequest.process_request_and_get_result(
                request_data_dict, CRUDOperation.UPDATE, event_id, operation_requested
            )
        )
        processRequest.process_message_for_parameters(
            request_data_dict, objCommonResponseDataModel
        )
    await create_notification(
        objCommonResponseDataModel, f"Event(s) locked , Have a look !"
    )
    return await send_response(objCommonResponseDataModel)


# @router.post(constants.EVENT_COMMON_URL)
async def create_event_controller(event_incoming_data: CommonRequestDataModel):
    """
    The create_event_controller function is the controller function that will be called when a user wants to create an event.
    The incoming data from the request will be passed into this function and it will return a response object with either
    a success or failure status code. The incoming data is of type CommonRequestDataModel, which has been defined in
    the events_constants file.

    :param event_incoming_data: CommonRequestDataModel: Pass the data from the event to this function
    :param : Pass the data that is used to create an event
    :return: The result of the api_calling_interface function
    :author: Arun Kumar[1533]
    """
    return await processRequest.api_calling_interface(event_incoming_data)


# @router.delete(constants.DELETE_EVENT_URL)
async def delete_event_controller(
    event_data: EventMasterDelete,
):
    """
    The delete_event_controller function is used to delete an event from the database.
    The function accepts a JSON object containing the event data and returns a JSON response.

    :param event_data: EventMasterDelete: Pass the data of event to be deleted
    :param : Store the data that is to be deleted from the database
    :return: A list of events that were deleted
    :author: Arun Kumar[1533]
    """
    request_data_dict = event_data.dict()
    objTempResponse = await processRequest.verify_input(
        event_data, CRUDOperation.DELETE
    )
    if (
        objTempResponse.status == OperationStatus.FAIL
        or objTempResponse.status == OperationStatus.OPERATION_ABORTED
    ):
        return await send_response(objTempResponse)
    objCommonResponseDataModel = await processRequest.process_request_and_get_result(
        request_data_dict,
        CRUDOperation.DELETE,
        event_data.event_id,
        CRUDOperation.DELETE,
    )
    processRequest.process_message_for_parameters(
        request_data_dict, objCommonResponseDataModel
    )
    return await send_response(objCommonResponseDataModel)


@router.post(events_constants.GET_EVENT_DETAILS)
async def get_events_promo_controller(event_data: EventRequest):
    """
    The get_events_promo_controller function is used to get the events data from the database.
    The function accepts a JSON object as input and returns a JSON object as output.

    :param event_data: EventRequest: Pass the input data to the function
    :param : Get the events based on the event id
    :return: The response from the api_calling_interface function
    :author: Arun Kumar[1533]
    """

    event_incoming_data = processRequest.prepare_data(event_data, CRUDOperation.GET, -1)
    objCommonResponseDataModel = await processRequest.api_calling_interface(
        event_incoming_data
    )
    return await send_response(objCommonResponseDataModel)


# @router.post(f"/lock_event_instantly")
@scheduler.scheduled_job("cron", hour=5, minute=30)
async def lock_event_logically():
    """
    The lock_event_logically function is used to lock the events which are in submit_offer_by date.

    :return: The list of event ids which are locked based on the submit_offer_by date
    :author: Arun Kumar[1533]
    """
    even_get_object = EventMasterGet()
    even_get_object.filters = EventFilters()
    even_get_object.filters.calendar = Calendar()
    even_get_object.filters.calendar.start_date = datetime.now() + timedelta(days=3)
    even_get_object.filters.calendar.end_date = datetime.now() + timedelta(days=10)
    event_incoming_data = processRequest.prepare_data(
        even_get_object, CRUDOperation.GET, -1
    )
    objCommonResponseDataModel = await processRequest.api_calling_interface(
        event_incoming_data
    )
    print(objCommonResponseDataModel)

    event_ids = []
    for index, item in objCommonResponseDataModel.data.iterrows():
        if item["ad_type"] != 2:
            if events_constants.LOCK_BASED_ON == "submit_offer_by":
                if (
                    item["submit_offer_by"] is not None
                    and item["submit_offer_by"] < datetime.now()
                ):
                    event_ids.append(item["event_id"])
            else:
                event_ids.append(item["event_id"])
    print(event_ids)
    update_json = {"status": 1}
    event_data = EventMasterUpdate(event_id=0)
    event_data.attributes = update_json
    request_data_dict = exclude_optional_dict(event_data)
    request_data_dict["attributes"]["updated_by"] = common_constants.GENERIC_USER_ID
    request_data_dict["attributes"]["updated_at"] = datetime.now()
    await processRequest.process_request_and_get_result(
        request_data_dict, CRUDOperation.UPDATE, event_ids, CRUDOperation.LOCKED
    )
    await create_notification(
        objCommonResponseDataModel, f"Event(s) auto locked , Have a look !"
    )


@router.post(events_constants.VALIDATE_EVENT_EDIT_URL)
async def validate_edit_event(request: EventEditValidate):
    event_incoming_data = processRequest.prepare_data(request, CRUDOperation.GET, -1)
    objCommonResponseDataModel = await processRequest.api_calling_interface(
        event_incoming_data
    )
    return await send_response(objCommonResponseDataModel)
