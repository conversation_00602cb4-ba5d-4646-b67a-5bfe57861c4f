
EVENT_COMMON_URL = "/events-operation"
EVENT_URL = "/events"
CREATE_EVENT_URL = "/create-event"
UPDATE_EVENT_URL = "/update-event"
DELETE_EVENT_URL = "/delete-event"
SOFT_DELETE_EVENT_URL = "/events/delete"
LOCK_EVENT_URL = "/events/lock"
LOGICAL_DELETE_EVENT_URL = "/del-event"
GET_EVENT_URL = "/get-events"
MODULE_NAME = "EVENTS"
EVENT_AUTORESIMULATION_MODULE = "EVENT_AUTO_RESIMULATION"
GET_EVENT_DETAILS = "/events-details"
LOCK_BASED_ON = ""
VALIDATE_EVENT_EDIT_URL = "/events/edit/validate"
DOWNLOAD_VALIDATE_EVENT_EDIT_DATA_URL = "/events/edit/validate/download"



AUTO_RESIMULATION_SUCCESS_MESSAGE = "Resimulation is complete for all finalized offers under the event {event_name}"
AUTO_RESIMULATION_FAILURE_MESSAGE = "Something went wrong when re-simulating promos of event {event_name}"

