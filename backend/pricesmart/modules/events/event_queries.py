from datetime import date , datetime
from typing import List
from multimethod import multimethod
import pricesmart_common.constants as global_constants
from configuration.environment import environment
from pricesmart_common.utils import get_key_value_str, get_str_repr


class EventQueries():
    # def resimulate_data_correction_query(self, event_id: int):
    #     query = f"call simulation.refresh_short_promo_multiplier_event_modified({event_id});"
    #     return query

    def get_validate_event_edit_query(self, event_id: int, start_date: date, end_date: date, store_ids: List, product_h2_ids: List, ad_type:int):
        validate_event_edit_query = f"""
            with latest_event_store_association as (
                select 
                    {event_id} as event_id,      -- update event_id
                    array_agg(distinct sm.store_h6_id) as store_id_list 
                from 
                    public.store_master sm
                where 
                    {get_key_value_str('store_h6_id', store_ids)}    -- new h2 values
            ),
            edited_event_promo_details as (
                select e.event_id,
                    event_name,
                    event_start,
                    event_end,
                    promo_id,
                    item_id_list,
                    store_id_list
                    from  
                    (
                        (
                            select 
                                e.event_id, 
                                e.name as event_name, 
                                e.start_date as event_start, 
                                e.end_date as event_end,
                                p.promo_id,
                                array_agg(distinct pp.item_id) as item_id_list
                            from 
                                public.event_master e
                            left join
                                public.promo_master p on p.event_id = e.event_id
                            left join 
                                public.promo_products pp on p.promo_id = pp.promo_id
                            left join 
                                latest_event_store_association es on es.event_id = e.event_id
                            where 
                                e.event_id = {event_id}  -- update event_id
                                and p.status = 4   -- only take finalized promos from source to compare with other event promos for overlap
                                and p.ad_type = {ad_type}
                            group by 
                                e.event_id, 
                                e.name, 
                                e.start_date, 
                                e.end_date,
                                p.promo_id
                        ) as e
                        left join
                        latest_event_store_association es using(event_id)
                    )
            ),
            -- fetching all active promo list between the to-be-updated event dates
            active_promos_set as (
                select 
                    p.promo_id,
                    p.name as promo_name,
                    p.start_date as promo_start,
                    p.end_date as promo_end, 
                    p.status,
                    p.event_id
                from 
                    public.promo_master p
                where
                    p.status = 4
                    and p.event_id != {event_id}
                    and p.start_date <= '{end_date}'   -- updated event end date
                    and p.end_date >= '{start_date}'   -- updated event start date
                    and p.ad_type = {ad_type}
            ),
            -- details of promos that conflict with the edited event values
            conflict_data_set as (
                select
                    distinct em.event_id,
                    em.name as event_name,
                    aps.promo_id,
                    aps.promo_name
                from 
                    public.event_store es
                left join 
                    public.event_master em on em.event_id = es.event_id
                left join
                    active_promos_set aps on aps.event_id = es.event_id
                left join 
                    public.promo_products pp on aps.promo_id = pp.promo_id
                where 
                    em.event_id in (select distinct event_id from active_promos_set)
                    and es.store_id in (select unnest(store_id_list) from edited_event_promo_details)
                    and pp.item_id in (select unnest(item_id_list) from edited_event_promo_details)
            ),
            -- finding removed and newly added product h2 values  
            h2_diff_cte as (
                select 
                    ARRAY(
                        SELECT 
                            distinct hierarchy_name::bigint as h2_id 
                        from 
                            public.event_hierarchy 
                        where 
                            hierarchy_type='product_hierarchy' 
                            and hierarchy_level_name='product_h2' 
                            and event_id = {event_id}         -- update event_id
                        EXCEPT 
                        SELECT 
                            unnest(array[{product_h2_ids}]::bigint[]) as h2_id    -- new h2 values 
                    ) AS deleted_product_h2,
                    ARRAY( 
                        SELECT 
                            unnest(array[{product_h2_ids}]::bigint[]) as h2_id   -- new h2 values
                        EXCEPT
                        SELECT 
                            distinct hierarchy_name::bigint as h2_id 
                        from 
                            public.event_hierarchy 
                        where 
                            hierarchy_type='product_hierarchy' 
                            and hierarchy_level_name='product_h2' 
                            and event_id = {event_id}      -- update event_id
                    ) as added_product_h2
            ),
            -- finding removed and newly added store ids
            store_ids_diff_cte as (
                select 
                    ARRAY(
                        SELECT 
                            store_id
                        from 
                            public.event_store
                        where event_id = {event_id}
                        except
                        SELECT 
                            unnest(array[{store_ids}]::bigint[]) as store_id
                    ) AS deleted_store_ids,
                    ARRAY( 
                        SELECT 
                            unnest(array[{store_ids}]::bigint[]) as store_id
                        except
                        SELECT 
                            store_id
                        from 
                            public.event_store
                        where event_id = {event_id}
                    ) as added_store_ids
            ),
            -- finding promos that has to be archived due to change in event hierarchy 
            to_be_deleted_promo_set as (
                select 
                    pm.promo_id, 
                    pm.name as promo_name,
                    'To be archived' as action
                from 
                    public.promo_master pm
                left join 
                    public.promo_products pp on pm.promo_id = pp.promo_id 
                where 
                    pm.event_id = {event_id}   -- update event_id
                    and pm.status!=6
                    and pp.item_id in (
                        select 
                            prm.product_h5_id 
                        from 
                            public.product_master prm 
                        where 
                            product_h2_id in (select unnest(deleted_product_h2) from h2_diff_cte)
                    )
            ),
            -- finding promos that are getting auto re-simulated/edited due to event change
            to_be_updated_promos_set as (
                select
                    pm.promo_id,
                    pm.name as promo_name,
                    'To be re-simulated/edited' as action
                from 
                    public.promo_master pm 
                    left join
                    public.event_master em
                    using (event_id)
                    cross join
                    h2_diff_cte,store_ids_diff_cte
                where
                    pm.event_id = {event_id}   -- update event_id
                    and pm.promo_id not in (select promo_id from to_be_deleted_promo_set)
                    and (
                        em.start_date != {get_str_repr(start_date)} 
                        or em.end_date != {get_str_repr(end_date)} 
                        or array_length(h2_diff_cte.deleted_product_h2,1) > 0
                        or array_length(h2_diff_cte.added_product_h2,1) > 0
                        or array_length(store_ids_diff_cte.deleted_store_ids,1) > 0
                        or array_length(store_ids_diff_cte.added_store_ids,1) > 0

                    )
            ),
            -- promos affected by unadv event date change
            unadv_event_date_change_blocking_promos as (
                select
                    pm.promo_id, 
                    pm.name as promo_name,
                    pm.start_date, 
                    pm.end_date,
                    u.name as created_by,
                    em.name as event_name
                from 
                    public.promo_master pm, um.users u, public.event_master em
                where 
                    {ad_type} in (select id from metaschema.tb_app_sub_master tasm where name='Unadvertised')
                    and pm.created_by = u.id
                    and pm.event_id = em.event_id
                    and pm.event_id = {event_id}
                    and ( 
                        pm.start_date > {get_str_repr(end_date)} -- updated event end date
                        or 
                        pm.end_date < {get_str_repr(start_date)} -- updated event start date
                    )
            ),
            -- building response structure for conflicted promo details
            validation_data as ( 
                select 
                    json_agg(
                        json_build_object(
                            'event_id',event_id,
                            'event_name', event_name,
                            'promo_id', promo_id,
                            'promo_name', promo_name
                        )
                    ) as validation,
                    {event_id} as event_id  -- update event_id
                from
                    conflict_data_set
                where 
                    promo_id not in (select promo_id from unadv_event_date_change_blocking_promos)
            ),
            -- building response structure for re-simulated/edited promo details
            info_data as (
                select
                    json_agg( 
                        json_build_object(
                            'promo_id', promo_id,
                            'promo_name', promo_name,
                            'action', action
                        )
                    ) as info,
                    {event_id} as event_id   -- update event_id
                from (
                    select * from to_be_deleted_promo_set
                    union
                    select * from to_be_updated_promos_set
                where 
                    promo_id not in (select promo_id from unadv_event_date_change_blocking_promos)
                order by 
                    action
                ) dd
            ),
            -- building response structure for unadvertised event edit blocking promo details
            unadv_blocking_promo_data as (
                select
                    {event_id} as event_id,
                    json_agg( 
                        json_build_object(
                            'event_name', ubp.event_name,
                            'promo_id', ubp.promo_id,
                            'promo_name', ubp.promo_name,
                            'promo_start', ubp.start_date,
                            'promo_end', ubp.end_date,
                            'created_by', ubp.created_by
                        )
                    ) as unadv_blocking_promos
                from 
                    unadv_event_date_change_blocking_promos ubp
            )
            select 
                validation, info, unadv_blocking_promos
            from 
                validation_data vd, info_data id, unadv_blocking_promo_data un
            where vd.event_id = id.event_id and vd.event_id = un.event_id
        """
        return validate_event_edit_query

    def get_events_based_on_hierarchy_query(self,schema_name:str, hierarchy_value:list, hierarchy_name:str, hierarchy_type:str, start_date:date , end_date: date):
        event_details_query= f"""
        with event_auto_resimulation_status as (
            select event_id,max(is_under_processing)  as is_under_processing
            from promo_master pm
            where event_id in (
                select event_id from event_master
                where start_date >= to_date('{datetime.strftime(start_date, "%m/%d/%Y")}','mm/dd/yyyy') and end_date <= to_date('{datetime.strftime(end_date, "%m/%d/%Y")}','mm/dd/yyyy')
            )
            group by event_id
        )
        Select * from (Select distinct
            A.event_id,
            A.event_code,
            A.campaign_id,
            A.name,
            to_char(A.start_date,'{global_constants.DB_DATE_FORMAT}') as start_date,
            to_char(A.end_date,'{global_constants.DB_DATE_FORMAT}') as end_date,
            A.customer_segment,
            A.description,
            A.channel,
            A.channel_type,
            A.ad_type,
            A.marketing_channel,
            A.status as is_locked,
            to_char(A.created_at,'{global_constants.DB_DATETIME_FORMAT}') as created_at,
            to_char(A.updated_at,'{global_constants.DB_DATETIME_FORMAT}') as updated_at,
            A.created_by,
            A.updated_by,
            A.is_deleted,
            to_char(A.submit_offer_by,'{global_constants.DB_DATE_FORMAT}') as submit_offer_by,
            A.marketing_notes,
            A.event_type,
            A.event_objective,
            A.event_objective_description,
            a.event_products_count,
            A.event_stores_count,
            COALESCE(event_auto_resimulation_status.is_under_processing,0) as is_under_processing,
            A.finalized_sales_units,
            A.finalized_baseline_sales_units,
            A.finalized_incremental_sales_units,
            A.finalized_revenue,
            A.finalized_baseline_revenue,
            A.finalized_incremental_revenue,
            A.finalized_margin,
            A.finalized_baseline_margin,
            A.finalized_incremental_margin,
            A.finalized_markdown_budget,
            A.recommended_sales_units,
            A.recommended_baseline_sales_units,
            A.recommended_incremental_sales_units,
            A.recommended_revenue,
            A.recommended_baseline_revenue,
            A.recommended_incremental_revenue,
            A.recommended_margin,
            A.recommended_baseline_margin,
            A.recommended_incremental_margin,
            A.recommended_markdown_budget,
            A.scenarios_sales_units,
            A.scenarios_baseline_sales_units,
            A.scenarios_incremental_sales_units,
            A.scenarios_revenue,
            A.scenarios_baseline_revenue,
            A.scenarios_incremental_revenue,
            A.scenarios_margin,
            A.scenarios_baseline_margin,
            A.scenarios_incremental_margin,
            A.scenarios_markdown_budget,
            A.event_promo_count as event_all_promo_count,
            c.event_filtered_promo_count  as event_promo_count,
            A.finalized_promo_spend,
            A.recommended_promo_spend,
            A.performance,
            A.performance_status as status,
            A.event_lifecycle_status,
            A.scenarios_revenue_target,
            A.scenarios_gross_margin_target,
            A.scenarios_units_target ,
            A.recommended_revenue_target ,
            A.recommended_gross_margin_target ,
            A.recommended_units_target ,
            A.finalized_revenue_target ,
            A.finalized_gross_margin_target ,
            A.finalized_units_target ,
            A.actuals_sales_units,
            A.actuals_baseline_sales_units,
            A.actuals_incremental_sales_units,
            A.actuals_revenue,
            A.actuals_baseline_revenue,
            A.actuals_incremental_revenue,
            A.actuals_margin,
            A.actuals_baseline_margin,
            A.actuals_incremental_margin,
            A.actuals_revenue_target,
            A.actuals_gross_margin_target,
            A.actuals_units_target,
            A.event_txn  ,
            A.event_units_per_txn ,
            A.event_avg_basket_size,
            A.start_date  as order_start_date
        from  {schema_name}.vw_event_details_processed A inner join  {schema_name}.event_hierarchy B  on A.event_id = B.event_id 
        left join  ({self.get_event_promo_query(hierarchy_value, hierarchy_name, hierarchy_type)}) C 
        on A.event_id = C.event_id
        left join event_auto_resimulation_status
        on A.event_id = event_auto_resimulation_status.event_id
        where a.start_date >= to_date('{datetime.strftime(start_date, "%m/%d/%Y")}','mm/dd/yyyy') and a.end_date <= to_date('{datetime.strftime(end_date, "%m/%d/%Y")}','mm/dd/yyyy')
        {self.get_hierarchy_condition(hierarchy_value, hierarchy_name, hierarchy_type)}
        ) foo 
        """
        return event_details_query

    def get_hierarchy_condition(self , hierarchy_value : list , hierarchy_name : str , hierarchy_type :str):
        """
        The get_hierarchy_condition function is used to create a condition string for the SQL query.
        It takes in three parameters: self, hierarchy_value (a list of strings), and hierarchy_name (a string).
        The function returns a condition string that will be added to the end of an SQL query. The returned value 
        is dependent on the values passed into the function.
        
        :param self: Access the class attributes
        :param hierarchy_value : list: Pass the list of values that we want to filter on
        :param hierarchy_name : str: Specify the hierarchy level that is being used
        :param hierarchy_type :str: Specify the type of hierarchy that is being used
        :return: The condition for the hierarchy_name and hierarchy_type
        :author: Arun Kumar[1533]
        """
        if len(hierarchy_value) > 0:
            return f""" and b.hierarchy_name in ({','.join(str(x) for x in hierarchy_value)}) and b.hierarchy_level_name = '{hierarchy_name}' and hierarchy_type='{hierarchy_type}'"""
        else:
            return ""

    def get_promo_hierarchy_condition(self , hierarchy_value : list , hierarchy_name : str , hierarchy_type :str):
        """
        The get_hierarchy_condition function is used to create a condition string for the SQL query.
        It takes in three parameters: self, hierarchy_value (a list of strings), and hierarchy_name (a string).
        The function returns a condition string that will be added to the end of an SQL query. The returned value 
        is dependent on the values passed into the function.
        
        :param self: Access the class attributes
        :param hierarchy_value : list: Pass the list of values that we want to filter on
        :param hierarchy_name : str: Specify the hierarchy level that is being used
        :param hierarchy_type :str: Specify the type of hierarchy that is being used
        :return: The condition for the hierarchy_name and hierarchy_type
        :author: Arun Kumar[1533]
        """
        if len(hierarchy_value) > 0:
            #','.join(str(x) for x in parameter_value)
            return f""" and b.hierarchy_value in ({','.join(str(x) for x in hierarchy_value)}) and b.hierarchy_level = '{hierarchy_name}' and hierarchy_type='{hierarchy_type}'"""
            #return f""" and b.hierarchy_value in {"('%s')" % "','".join(hierarchy_value)} and b.hierarchy_level = '{hierarchy_name}' and hierarchy_type='{hierarchy_type}'"""
        else:
            return ""

    def get_event_promo_query(self , hierarchy_value : list , hierarchy_name : str , hierarchy_type :str):

        return f"""select event_id , count( distinct A.promo_id) as event_filtered_promo_count from public.promo_master A, public.promo_hierarchy B
                    where A.promo_id = B.promo_id  {self.get_promo_hierarchy_condition(hierarchy_value, hierarchy_name, hierarchy_type)}
                    group by 1
                """



    def get_union_of_queries(self,list_of_queries: list):
        """
        The get_union_of_queries function accepts a list of queries and returns the union of all those queries.
        For example, if you pass in two query strings:
            get_union_of_queries(['SELECT * FROM table', 'SELECT * FROM anothertable'])
        You will receive back:
            SELECT * FROM table UNION SELECT * FROM anothertable
        
        :param self: Access the class attributes
        :param list_of_queries: list: Store the queries that are being unioned together
        :return: The final query that will be executed on the database
        :author: Arun Kumar[1533]
        """
        final_query = ' union '.join(list_of_queries)
        return final_query

    def get_events_promo_query(self , event_ids : list):
        """
        The get_events_promo_query function accepts a list of event_ids and returns the promo details for each event.
        The function first creates a query that selects all the promo details from {client_schema}.promo_master for each event in 
        the list of events passed to it as an argument. The function then executes this query on the database and returns 
        the results.
        
        :param self: Access the class attributes
        :param event_ids : list: Pass the list of event_ids for which we need to fetch the promo details
        :return: A query which is used to fetch the promo details for a list of events
        :author: Arun Kumar[1533]
        """
        query = f"""select distinct B.* from {environment.client_schema}.event_master A , {environment.client_schema}.promo_master B
            where a.is_deleted =0 and A.event_id = B.event_id and A.event_id in  ({",".join(str(x) for x in event_ids)} )
        """
        
        return query

    def get_events_with_promo_query(self , event_ids : list):
        """
        The get_events_with_promo_query function accepts a list of event_ids and returns the following:
            - A list of dictionaries, where each dictionary represents an event.
            - The keys in each dictionary are the same as those returned by get_events_query.
            - Each key is mapped to a value for that particular event.
        
        :param self: Access the class attributes
        :param event_ids : list: Pass the list of event ids to be fetched from the database
        :return: A query that joins the event_master and promo_master tables
        :author: Arun Kumar[1533]
        """
        query = f"""select distinct A.*,B.* from {environment.client_schema}.event_master A  
        left join {environment.client_schema}.promo_master B on  (A.event_id = B.event_id)
        where a.is_deleted =0 and A.event_id in  ({",".join(str(x) for x in event_ids)} )
        """
        return query

    @multimethod
    def get_events_query(self , event_ids : list, with_promo:bool , with_product_hierarchy: bool , with_store_hierarchy: bool):
        """
        The get_events_query function accepts a list of event_ids and returns the following fields for each event:
            -event_id
            -event_code
            -campaign_id
            -name  (of the campaign)  
        :param self: Access variables that belongs to the class
        :param event_ids : list: Pass the list of event ids to be fetched from the database
        :param is_only_event:bool: Pass a boolean value to the query
        :return: The query string that is used to fetch the event details from the event master table
        :author: Arun Kumar[1533]
        """

        query = f"""
        select 
            A.event_id,
            A.event_code,
            A.campaign_id,
            A.name,
            to_char(A.start_date,'{global_constants.DB_DATE_FORMAT}') as start_date,
            to_char(A.end_date,'{global_constants.DB_DATE_FORMAT}') as end_date,
            A.customer_segment,
            A.description,
            A.channel,
            A.channel_type,
            A.ad_type,
            A.marketing_channel,
            A.status as is_locked,
            to_char(A.created_at,'{global_constants.DB_DATETIME_FORMAT}') as created_at,
            to_char(A.updated_at,'{global_constants.DB_DATETIME_FORMAT}') as updated_at,
            A.created_by,
            A.updated_by,
            A.is_deleted,
            to_char(A.submit_offer_by,'{global_constants.DB_DATE_FORMAT}') as submit_offer_by,
            A.marketing_notes,
            A.event_type,
            A.event_objective,
            A.event_objective_description,
            A.min_percent_value,
            B.event_products_count,
            B.event_stores_count,
            B.finalized_sales_units,
            B.finalized_baseline_sales_units,
            B.finalized_incremental_sales_units,
            B.finalized_revenue,
            B.finalized_baseline_revenue,
            B.finalized_incremental_revenue,
            B.finalized_margin,
            B.finalized_baseline_margin,
            B.finalized_incremental_margin,
            B.recommended_sales_units,
            B.recommended_baseline_sales_units,
            B.recommended_incremental_sales_units,
            B.recommended_revenue,
            B.recommended_baseline_revenue,
            B.recommended_incremental_revenue,
            B.recommended_margin,
            B.recommended_baseline_margin,
            B.recommended_incremental_margin,
            B.scenarios_sales_units,
            B.scenarios_baseline_sales_units,
            B.scenarios_incremental_sales_units,
            B.scenarios_revenue,
            B.scenarios_baseline_revenue,
            B.scenarios_incremental_revenue,
            B.scenarios_margin,
            B.scenarios_baseline_margin,
            B.scenarios_incremental_margin,
            B.event_promo_count,
            B.finalized_promo_spend,
            B.recommended_promo_spend,
            B.performance,
            B.performance_status as status,
            B.event_lifecycle_status,
            B.scenarios_revenue_target,
            B.scenarios_gross_margin_target,
            B.scenarios_units_target ,
            B.recommended_revenue_target ,
            B.recommended_gross_margin_target ,
            B.recommended_units_target ,
            B.finalized_revenue_target ,
            B.finalized_gross_margin_target ,
            B.finalized_units_target,
            B.actuals_sales_units,
            B.actuals_baseline_sales_units,
            B.actuals_incremental_sales_units,
            B.actuals_revenue,
            B.actuals_baseline_revenue,
            B.actuals_incremental_revenue,
            B.actuals_margin,
            B.actuals_baseline_margin,
            B.actuals_incremental_margin,
            B.actuals_revenue_target,
            B.actuals_gross_margin_target,
            B.actuals_units_target,
            B.event_txn  ,
            B.event_units_per_txn ,
            B.event_avg_basket_size 
            {', A.product_hierarchy' if with_product_hierarchy else ' '}
            {', A.store_hierarchy' if with_store_hierarchy else ' '}
            {', A.promos' if with_promo else ' '}

            from {environment.client_schema}.fn_event_details_complex_structure(ARRAY[{",".join(str(x) for x in event_ids)}])  A, 
            {environment.client_schema}.fn_event_details_processed(ARRAY[{",".join(str(x) for x in event_ids)}]) B
            where A.event_id = B.event_id and A.event_id in  ({",".join(str(x) for x in event_ids)} )

        """
        return query

    @multimethod
    def get_events_query(self , event_ids : list, is_only_event:bool):
        """
        The get_events_query function accepts a list of event_ids and returns the following fields for each event:
            -event_id
            -event_code
            -campaign_id
            -name  (of the campaign)  
        :param self: Access variables that belongs to the class
        :param event_ids : list: Pass the list of event ids to be fetched from the database
        :param is_only_event:bool: Pass a boolean value to the query
        :return: The query string that is used to fetch the event details from the event master table
        :author: Arun Kumar[1533]
        """
        query = f"""select
            A.event_id,
            A.event_code,
            A.campaign_id,
            A.name,
            to_char(A.start_date,'{global_constants.DB_DATE_FORMAT}') as start_date,
            to_char(A.end_date,'{global_constants.DB_DATE_FORMAT}') as end_date,
            A.customer_segment,
            A.description,
            A.channel,
            A.channel_type,
            A.ad_type,
            A.marketing_channel,
            A.status,
            to_char(A.created_at,'{global_constants.DB_DATETIME_FORMAT}') as created_at,
            to_char(A.updated_at,'{global_constants.DB_DATETIME_FORMAT}') as updated_at,
            A.created_by,
            A.updated_by,
            A.is_deleted,
            to_char(A.submit_offer_by,'{global_constants.DB_DATE_FORMAT}') as submit_offer_by,
            A.marketing_notes,
            A.event_type,
            A.event_objective,
            A.event_objective_description,
            A.min_percent_value
            from {environment.client_schema}.event_master A
            where a.is_deleted =0 and A.event_id in  ({",".join(str(x) for x in event_ids)} )
        """
        return query
        
    @multimethod
    def get_events_query(self , event_ids : list):
        """
        The get_events_query function accepts a list of event_ids and returns the following fields for each event:
            -event_id
            -event_code
            -campaign_id
            -name 
            -start date (formatted as YYYY-MM-DD) 
            -end date (formatted as YYYY-MM-DD) 
            customer segment, description, channel, marketing channel, status , created at , updated at , created by , updated by is deleted submit offer by marketing notes event type objective objective description
        
        :param self: Allow the function to refer to the class variables
        :param event_ids : list: Pass the event_ids to be queried
        :return: A query that can be used to fetch the event details for a given list of events
        :author: Arun Kumar[1533]
        """
        query = f"""select  event_id,
                    name,
                    to_char(start_date,'{global_constants.DB_DATE_FORMAT}') as start_date,
                    to_char(end_date,'{global_constants.DB_DATE_FORMAT}') as end_date,
                    status as is_locked,
                    is_deleted,
                    customer_segment,
                    channel,
                    channel_type,
                    ad_type,
                    marketing_channel,
                    marketing_notes,
                    to_char(submit_offer_by,'{global_constants.DB_DATE_FORMAT}') as submit_offer_by,
                    event_type,
                    event_objective,
                    event_objective_description,
                    event_products_count,
                    event_stores_count,
                    finalized_sales_units,
                    finalized_baseline_sales_units,
                    finalized_incremental_sales_units,
                    finalized_revenue,
                    finalized_baseline_revenue,
                    finalized_incremental_revenue,
                    finalized_margin,
                    finalized_baseline_margin,
                    finalized_incremental_margin,
                    recommended_sales_units,
                    recommended_baseline_sales_units,
                    recommended_incremental_sales_units,
                    recommended_revenue,
                    recommended_baseline_revenue,
                    recommended_incremental_revenue,
                    recommended_margin,
                    recommended_baseline_margin,
                    recommended_incremental_margin,
                    scenarios_sales_units,
                    scenarios_baseline_sales_units,
                    scenarios_incremental_sales_units,
                    scenarios_revenue,
                    scenarios_baseline_revenue,
                    scenarios_incremental_revenue,
                    scenarios_margin,
                    scenarios_baseline_margin,
                    scenarios_incremental_margin,
                    event_promo_count,
                    finalized_promo_spend,
                    recommended_promo_spend,
                    performance,
                    performance_status as status,
                    event_lifecycle_status,
                    scenarios_revenue_target,
                    scenarios_gross_margin_target,
                    scenarios_units_target ,
                    recommended_revenue_target ,
                    recommended_gross_margin_target ,
                    recommended_units_target ,
                    finalized_revenue_target ,
                    finalized_gross_margin_target ,
                    finalized_units_target,
                    actuals_sales_units,
                    actuals_baseline_sales_units,
                    actuals_incremental_sales_units,
                    actuals_revenue,
                    actuals_baseline_revenue,
                    actuals_incremental_revenue,
                    actuals_margin,
                    actuals_baseline_margin,
                    actuals_incremental_margin,
                    actuals_revenue_target,
                    actuals_gross_margin_target,
                    actuals_units_target,
                    event_txn  ,
                    event_units_per_txn ,
                    event_avg_basket_size,
                    min_percent_value
                    from {environment.client_schema}.fn_event_details_processed (ARRAY[{",".join(str(x) for x in event_ids)}])
                    where event_id in  ({",".join(str(x) for x in event_ids)} )
                    """
        return query

    def get_events_product_hierarchy_query(self , event_ids : list):
        """
        The get_events_product_hierarchy_query function is used to get the product hierarchy for a given event.
        The function accepts a list of event ids and returns all the products associated with that particular event.
        
        :param self: Allow the function to reference attributes or methods of the class in which it is defined
        :param event_ids : list: Pass the list of event_ids to the query
        :return: A query that returns all the events and their corresponding product hierarchy
        :author: Arun Kumar[1533]
        """
        
        query = f"""
        SELECT * FROM {environment.client_schema}.fn_event_product_hierarchies(ARRAY[{",".join(str(x) for x in event_ids)}]) where event_id in ({",".join(str(x) for x in event_ids)} )
        """
        return query

    def get_events_store_hierarchy_query(self , event_ids : list):
        """
        The get_events_store_hierarchy_query function accepts a list of event_ids and returns the store hierarchy for each event.
        The function is used to populate the Event Store Hierarchy table in the database.
        
        :param self: Allow the function to reference attributes or methods of the class in which it is defined
        :param event_ids : list: Pass the event_ids to the query
        :return: A query that will return all the events in the database with their store hierarchy
        :author: Arun Kumar[1533]
        """
        
        query = f"""
        SELECT * FROM {environment.client_schema}.fn_event_store_hierarchies(ARRAY[{",".join(str(x) for x in event_ids)}]) where event_id in ({",".join(str(x) for x in event_ids)} )
        """
        return query

    def get_event_edit_change_indicator_query(self, event_id):
        query = f"""with data_cte as (	
                        select 
                            em.event_id,
                            em.name as event_name,
                            em.start_date as new_start_date,
                            em.end_date as new_end_date,
                            em.ad_type as new_ad_type,
                            array_agg(distinct eh.hierarchy_name order by eh.hierarchy_name) as new_product_h2_list,
                            array_agg(distinct es.store_id order by es.store_id) as new_store_h6_list,
                            ted.start_date as old_start_date,
                            ted.end_date as old_end_date,
                            ted.product_h2_list as old_product_h2_list,
                            ted.store_h6_list as old_store_h6_list,
                            ted.ad_type as old_ad_type
                        from 
                            public.event_master em
                        left join 
                            public.temp_event_details ted on em.event_id = ted.event_id 
                        left join 
                            public.event_store es on em.event_id = es.event_id 
                        left join 
                            public.event_hierarchy eh on em.event_id = eh.event_id 
                        where 
                            em.event_id = {event_id}
                            and eh.hierarchy_level_name = 'product_h2'
                            and eh.hierarchy_type = 'product_hierarchy'
                        group by
                            em.event_id,
                            em.name,
                            em.start_date,
                            em.end_date,
                            em.ad_type,
                            ted.start_date,
                            ted.end_date,
                            ted.product_h2_list,
                            ted.store_h6_list,
                            ted.ad_type
                    ),
                    indicator_cte as (
                        select
                            event_name,
                            ARRAY(SELECT unnest(old_product_h2_list) EXCEPT SELECT unnest(new_product_h2_list)) AS deleted_product_h2,
                            new_ad_type, 
                            case 
                                when new_start_date = old_start_date and new_end_date = old_end_date then 0
                                else 1
                            end as date_change_ind,
                            case 
                                when new_product_h2_list::bigint[] = old_product_h2_list::bigint[] then 0
                                else 1
                            end as product_hierarchy_change_ind,
                            case 
                                when new_store_h6_list::bigint[] = old_store_h6_list::bigint[] then 0
                                else 1
                            end as store_hierarchy_change_ind
                        from
                            data_cte
                    )
                    select * from indicator_cte
            """
        return query

    def remove_promos_for_event_product_hierarchy_edit(self, event_id, removed_prod_h2_ids):
        query = f"""update public.promo_master 
                    set status=6
                    where promo_id in (
                        select 
                            distinct pm.promo_id
                        from 
                            public.promo_master pm
                        left join 
                            public.promo_products pp on pm.promo_id = pp.promo_id 
                        where 
                            pm.event_id = {event_id} 
                            and pm.status!=6
                            and pp.item_id in (select product_h5_id from public.product_master prm where {get_key_value_str('product_h2_id', removed_prod_h2_ids)})
                    );
        """
        return query

    def resimulate_process_queries_for_event_edit(self, event_id, remove_promos_query, latest_ad_type):
        if int(latest_ad_type) == global_constants.UNADVERTISED:
            promo_date_update_query = f"""
                -- updating promo dates for promos in the edited event for unadv event type
                update 
                    public.promo_master pm
                set 
                    start_date = case 
                                    when pm.start_date < em.start_date then em.start_date
                                    else pm.start_date
                                end, 
                    end_date = case 
                                    when pm.end_date > em.end_date then em.end_date
                                    else pm.end_date
                             end
                from 
                    (select event_id, start_date, end_date from public.event_master where event_id={event_id}) as em
                where 
                    pm.event_id = {event_id};
            """
        else:
            promo_date_update_query = f"""
                -- updating promo dates for promos in the edited event
                update public.promo_master pm
                    set start_date = em.start_date, end_date=em.end_date
                from 
                    (select event_id, start_date, end_date from public.event_master where event_id={event_id}) as em
                where 
                    pm.event_id = {event_id};
            """
        query = f"""{remove_promos_query}
            {promo_date_update_query}
            call simulation.refresh_short_promo_multiplier_event_modified({event_id});
            -- deleting data from ps_recommended_scenarios table 
            delete from 
                simulation.ps_recommended_scenarios prs 
            where 
                prs.promo_id in (select promo_id from public.promo_master where event_id={event_id});
                
            -- populating data to ia_scenario_master
            delete from 
                public.ia_scenario_master 
            where
                promo_id in (select promo_id from public.promo_master where event_id={event_id});
            insert into public.ia_scenario_master (scenario_id, event_id, promo_id, discount_level, created_by)
            select 
                pm.promo_id,
                event_id,
                pm.promo_id,
                pr.discount_level,
                -1 as created_by
            from public.promo_master pm 
                left join public.ps_rules pr on pm.promo_id = pr.promo_id     
            where pm.promo_id in (select distinct promo_id from simulation.ps_recommended_ia_projected prip where event_id = {event_id});
            
            -- populating data to ia_ps_scenario_discounts
            delete from 
                public.ia_ps_scenario_discounts
            where
                scenario_id in (select promo_id from public.promo_master where event_id = {event_id});  --  promo_id is saved as scenario_id in the ia_scenario_discounts table
            insert into public.ia_ps_scenario_discounts (scenario_id, discount_level_value, offer_value, offer_type_id)
            select 
                promo_id as scenario_id,
                discount_level_value,
                offer_value,
                offer_type_id
            from 
                simulation.ps_recommended_ia_projected prip 
            where 
                event_id = {event_id}
            group by 
                1,2,3,4;
                
            -- delete data from ps_recommended_ia_projected table for edited event
            delete from simulation.ps_recommended_ia_projected prip where event_id={event_id};  
            
            --- re-simulating for promos with new values
            DO $$
            DECLARE 
                 row int;
                _promo_id INTEGER;
                start_date VARCHAR;
                end_date VARCHAR;
                ad_type integer;
                vf_fixed_amount float8;
                vf_per_unit float8;
                _event_id integer;
                channel_type integer;
                week_start_date VARCHAR;
                week_end_date VARCHAR;
            BEGIN
                FOR row in SELECT distinct promo_id from promo_master where event_id = {event_id} and status!=6
                LOOP
                    BEGIN
                        select * from simulation.get_promo_details(row) INTO _promo_id,start_date,end_date, ad_type, _event_id,channel_type,vf_fixed_amount, vf_per_unit,week_start_date,week_end_date ;
                        CALL simulation.resimulate_event(_promo_id, start_date, end_date, channel_type, ad_type, vf_fixed_amount, vf_per_unit, week_start_date, week_end_date);
                        CALL simulation.optimisation_event(_promo_id, start_date, end_date, channel_type, ad_type, vf_fixed_amount, vf_per_unit, week_start_date, week_end_date);
                    EXCEPTION
                        WHEN others THEN
                            update promo_master set is_auto_resimulated = -1 where promo_id = row;
                    END;
                END LOOP;
            END
            $$;
            
            -- Deleting data from ps_recommended_finalized table for the edited event
            delete from simulation.ps_recommended_finalized where event_id = {event_id};
            
            -- Inserting data to ps_recommended_finalized table for finalized promos in edited event
            call simulation.finalized_event({event_id});
            
            -- Updating is_auto_resimulated flag for all edited/re-simulated promo which are in finalized or to-finalized status in the event
            update public.promo_master p
            set is_auto_resimulated = A.flag_value
            from (
                select 
                    pm.promo_id, 
                    case
                        when prip.promo_id is not null then 2
                        else 1
                    end as flag_value,
                    is_auto_resimulated
                from 
                    public.promo_master pm
                left join 
                    simulation.ps_recommended_ia_projected prip  on pm.promo_id = prip.promo_id
                where 
                    pm.event_id = {event_id} 
                    and pm.status in (2,4) 
            ) A
            where 
                p.promo_id = A.promo_id
                and p.is_auto_resimulated <> -1;
        """
        return query

    def post_background_task_queries(self, event_id,under_processing_status=0):
        query = f"""
            delete from public.temp_event_details where event_id = {event_id};
            update public.promo_master set is_under_processing = {under_processing_status} where event_id = {event_id};
        """
        return query
