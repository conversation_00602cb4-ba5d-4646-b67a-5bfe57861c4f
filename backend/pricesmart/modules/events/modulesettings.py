import app.main as server
import pricesmart.modules.events.events_constants as events_constants
import pricesmart_common.constants as global_constants
from configuration.environment import environment
from fastapi import APIRouter
from logger.logger import logger
from pricesmart.modules.events.controllers import event_crud_controller

server.app.include_router(
    event_crud_controller.router,
    prefix=f"{global_constants.API_PREFIX}",
    tags=[events_constants.MODULE_NAME],
)
logger.info(f" {environment.LOG_TITLE}: {events_constants.MODULE_NAME}")
