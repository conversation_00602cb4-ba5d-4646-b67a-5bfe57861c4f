from fastapi import APIRouter, UploadFile, File
from typing import Optional, List

from pricesmart.modules.ticketing_system.ticketing_system_request_models import CreateTicket
from pricesmart.modules.ticketing_system import ticketing_system_services as TicketingSystemServices
from pricesmart.common.response.process_response import send_response
from pricesmart.modules.ticketing_system import constants as Constants

router = APIRouter()


@router.get(Constants.GET_TICKETS_URL)
async def get_tickets_controller():
    response = await TicketingSystemServices.get_tickets_service()
    return send_response(response, True, Constants.success_message)


@router.post(Constants.CREATE_TICKET_URL)
async def create_ticket_controller(create_ticket_input: CreateTicket):

    response = await TicketingSystemServices.create_ticket_service(create_ticket_input)
    return send_response(response, True, Constants.success_message)


@router.post(Constants.CREATE_TICKET_WITH_ATTACHMENTS_URL)
async def create_ticket_with_attachments_controller(
    file: Optional[List[UploadFile]] = File(None),
    ticket_type_id: Optional[int] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
    priority_id: Optional[int] = None,
    email: Optional[str] = None,
):
    create_ticket_input = CreateTicket()
    create_ticket_input.title = title
    create_ticket_input.description = description
    create_ticket_input.priority_id = priority_id
    create_ticket_input.ticket_type_id = ticket_type_id
    create_ticket_input.email = email

    response = await TicketingSystemServices.create_ticket_with_attachments_service(
        create_ticket_input, file
    )
    return send_response(response, True, Constants.success_message)


@router.post(Constants.UPDATE_TICKET_URL)
async def update_ticket_controller(create_ticket_input: CreateTicket):

    await TicketingSystemServices.update_ticket_service(create_ticket_input)
    return send_response(
        "Ticket is updated successfully", True, Constants.success_message
    )


@router.get(Constants.LIST_ATTACHMENT_URL)
async def list_attachment_controller(ticket_id: int):
    output = await TicketingSystemServices.list_attachment_service(ticket_id)
    return send_response(output, True, Constants.success_message)


@router.put(Constants.DELETE_ATTACHMENT_URL)
async def delete_attachment_controller(ticket_id: int):
    output = await TicketingSystemServices.delete_attachment_service(ticket_id)
    return send_response(output, True, Constants.success_message)


@router.post(Constants.UPDATE_TICKET_WITH_ATTACHMENTS_URL)
async def update_ticket_with_attachments_controller(
    file: Optional[List[UploadFile]] = File(None),
    ticket_type_id: Optional[int] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
    priority_id: Optional[int] = None,
    email: Optional[str] = None,
    ticket_id: Optional[int] = None,
):
    update_ticket_input = CreateTicket()
    update_ticket_input.title = title
    update_ticket_input.description = description
    update_ticket_input.priority_id = priority_id
    update_ticket_input.ticket_type_id = ticket_type_id
    update_ticket_input.email = email
    update_ticket_input.ticket_id = ticket_id

    response = await TicketingSystemServices.update_ticket_with_attachments_service(
        update_ticket_input, file
    )
    return send_response(response, True, Constants.success_message)
