import requests
from io import StringIO, Bytes<PERSON>


from configuration.environment import environment


headers = {"Accept": "application/json", "Content-Type": "application/json"}

mojo_url = environment.mojo_base_url + environment.mojo_api_url


async def get_tickets_service():
    page = 1
    ticket_list = []
    while page > 0:
        response = requests.get(
            mojo_url
            + "/tickets/"
            + "search?query=queue.id:"
            + str(environment.mojo_ticket_queue_id)
            + "&access_key="
            + environment.mojo_access_key
            + f"&per_page=100&page={page}",
            headers=headers,
        )
        ticket = response.json()
        if len(ticket) == 100:
            page += 1
        else:
            page = 0
        ticket_list.extend(ticket)
    for i in ticket_list:
        i["description"] = i["description"].strip("~")
    return ticket_list


async def create_ticket_service(create_ticket_input):
    data = {
        "suppress_user_notification": "false",
        "title": create_ticket_input.title,
        "description": create_ticket_input.description,
        "priority_id": create_ticket_input.priority_id,
        "ticket_type_id": create_ticket_input.ticket_type_id,
        "ticket_queue_id": environment.mojo_ticket_queue_id,
        "user": {
            "email": environment.ticket_mail_list,
        },
        "cc": create_ticket_input.email,
    }

    response = requests.post(
        mojo_url + "tickets?access_key=" + environment.mojo_access_key,
        json=data,
        headers=headers,
    )
    ticket = response.json()
    if "description" in ticket:
        ticket["description"] = ticket["description"].strip("~")
    return ticket


async def create_ticket_with_attachments_service(create_ticket_input, attachments):
    data = {
        "suppress_user_notification": "false",
        "title": create_ticket_input.title,
        "description": create_ticket_input.description,
        "priority_id": create_ticket_input.priority_id,
        "ticket_type_id": create_ticket_input.ticket_type_id,
        "ticket_queue_id": environment.mojo_ticket_queue_id,
        "cc": create_ticket_input.email,
    }
    files = {}
    for i in range(len((attachments))):
        files[f"attachment[{i}][content]"] = attachments[i].file.read()

    response = requests.post(
        mojo_url + "tickets?access_key=" + environment.mojo_access_key,
        files=files,
        data=data,
    )
    ticket = response.json()
    if "description" in ticket:
        ticket["description"] = ticket["description"].strip("~")
    return ticket


async def update_ticket_service(create_ticket_input):
    data = {
        "suppress_user_notification": "false",
        "title": create_ticket_input.title,
        "description": create_ticket_input.description,
        "priority_id": create_ticket_input.priority_id,
        "ticket_type_id": create_ticket_input.ticket_type_id,
        "user": {
            "email": environment.ticket_mail_list,
        },
        "cc": create_ticket_input.email,
    }
    requests.put(
        mojo_url
        + "tickets/%s" % create_ticket_input.ticket_id
        + "?access_key="
        + environment.mojo_access_key,
        json=data,
        headers=headers,
    )


async def list_attachment_service(ticket_id):
    response = requests.get(
        mojo_url
        + "tickets/"
        + str(ticket_id)
        + "/attachments?access_key="
        + environment.mojo_access_key,
    )
    ticket = response.json()
    return ticket


async def delete_attachment_service(ticket_id):
    response = requests.get(
        mojo_url
        + "/attachments"
        + str(ticket_id)
        + "?access_key="
        + environment.mojo_access_key
        + " -X DELETE",
    )
    ticket = response.json()
    return ticket


async def update_ticket_with_attachments_service(update_ticket_input, attachments):
    data = {
        "suppress_user_notification": "false",
        "title": update_ticket_input.title,
        "description": update_ticket_input.description,
        "priority_id": update_ticket_input.priority_id,
        "ticket_type_id": update_ticket_input.ticket_type_id,
        "cc": update_ticket_input.email,
    }
    files = {}
    for i in range(len((attachments))):
        files[f"attachment[{i}][content]"] = attachments[i].file.read()

    requests.put(
        mojo_url
        + "tickets/%s" % update_ticket_input.ticket_id
        + "?access_key="
        + environment.mojo_access_key,
        files=files,
        data=data,
    )
