from typing import Optional

from enums.Enums import OperationStatus
from pandas import DataFrame
from pricesmart.database.operations.exception_details import CodeException
from pydantic import BaseModel


class CommonResponseDataModel(BaseModel):
    tag:Optional[str] = None
    data: Optional[object] = None
    message : Optional[str] = "Success"
    status:Optional[OperationStatus] = OperationStatus.FAIL
    exception: Optional[CodeException] =CodeException()
