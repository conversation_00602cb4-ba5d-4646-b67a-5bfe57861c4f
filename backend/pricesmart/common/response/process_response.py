import json

from fastapi.responses import JSONResponse
from pandas import DataFrame
from pricesmart.common.response.response_model import CommonResponseDataModel


async def send_response(response_data: CommonResponseDataModel):
    """
    The send_response function is used to send a response back to the client.
    It takes in a response_data object and returns a dictionary containing the status, message, error and data attributes of that object.

    :param response_data: CommonResponseDataModel: Send the response data to the client
    :return: A dictionary with the response data
    :author: <PERSON><PERSON>[1533]
    """
    if isinstance(response_data, list):
        response_data = response_data[0]

    if isinstance(response_data.data, DataFrame):
        data_result = json.loads(response_data.data.to_json(orient="records"))
    elif isinstance(response_data.data, dict):
        data_result = json.loads(json.dumps(response_data.data, indent=3, default=str))
    else:
        data_result = response_data.data

    status = response_data.status.value if response_data.status is not None else None
    message = response_data.message if response_data.message is not None else None
    error_message = (
        response_data.exception.error_message
        if response_data.exception is not None
        else None
    )

    response = JSONResponse(
        {
            "status": status,
            "message": message,
            "error": error_message,
            "data": data_result,
        },
        media_type="application/json",
    )
    return response
