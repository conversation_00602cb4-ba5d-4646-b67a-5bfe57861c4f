from typing import Optional

# from pricesmart.common.FilterModel import FilterModel
from enums.Enums import CRUDOperation
from pydantic import BaseModel


class CommonRequestDataModel(BaseModel):
    id: object
    operation: CRUDOperation = CRUDOperation.GET
    tag: Optional[str] = None
    associated_module: object = object()
    attributes: Optional[dict] = None
    # filter: Optional[FilterModel]
    refresh_data: Optional[bool] = False
    execution_sequence: int = 0
    parameter_column_name: Optional[str] = None
    dependent_parameter_name: Optional[str] = None
