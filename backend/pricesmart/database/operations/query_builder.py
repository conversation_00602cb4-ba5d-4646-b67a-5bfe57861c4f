
from multimethod import multimethod

from pricesmart.database.operations.postgres.execution_query_object import QueryObject



class PrepareSQLQueries():

    def get_insert_query(self, table_name, data, id_column_name = ""):
        """
        The get_insert_query function accepts a table name, a dictionary of column names and values,
        and an optional id_column_name. It returns the SQL query string that will insert the given data into 
        the specified table. If an id_column is provided, it will return the value of that column from 
        the row created by this insert.
        
        :param self: Allow the function to refer to the class that it is part of
        :param table_name: Specify the table name in which to insert the data
        :param data: Pass in all the data that needs to be inserted into the table
        :param id_column_name: Specify the name of the column that is used to return the id of a newly inserted row
        :return: The query string that will be used to insert a new record into the database
        :author: <PERSON><PERSON> kumar
        """
        if table_name == 'public.temp_event_details':
            query = f"""insert into public.temp_event_details (event_id, start_date, end_date, store_h6_list, product_h2_list, ad_type, channel_type ) 
                        select 
                            em.event_id,
                            em.start_date,
                            em.end_date,
                            array_agg(distinct es.store_id) as store_id_list,
                            array_agg(distinct eh.hierarchy_name) as product_h2_list,
                            em.ad_type,
                            em.channel_type
                        from 
                            public.event_master em 
                        left join 
                            public.event_store es on em.event_id = es.event_id 
                        left join 
                            public.event_hierarchy eh on em.event_id = eh.event_id 
                        where 
                            em.event_id = {data[id_column_name]}
                            and eh.hierarchy_level_name = 'product_h2'
                            and eh.hierarchy_type = 'product_hierarchy'
                        group by
                            em.event_id,
                            em.start_date,
                            em.end_date,
                            em.ad_type,
                            em.channel_type
                        returning {id_column_name}
            """
        else:
            columns = data.keys()
            cols_comma_separated = ', '.join(columns)
            binds_comma_separated = ', '.join(['%(' + item + ')s' for item in columns])
            if id_column_name == "" or id_column_name is None or id_column_name == "None":
                query = f'INSERT INTO {table_name} ({cols_comma_separated}) VALUES ({binds_comma_separated})'
            else:
                query = f'INSERT INTO {table_name} ({cols_comma_separated}) VALUES ({binds_comma_separated}) RETURNING {id_column_name}'
        return self.get_query_object(data, query, id_column_name)
    
    
    def get_update_query(self, table_name, data, where_condition, return_parameter:str):
        """
        The get_update_query function takes in a table name, data (a dictionary of column names and values),
        and where_condition (a string containing the where condition). It returns an update query that can be executed on 
        the database. For example, if we had a table called 'users' with columns 'id', 'name', and 'email':
        
        :param self: Reference the class object
        :param table_name: Specify the table name in which the data is to be inserted
        :param data: Store the values of the columns that are to be updated
        :param where_condition: Specify the condition for which the data is to be updated
        :return: The query string for updating the table with the given data
        :author: Arun kumar
        """
        _where_condition = self.get_where_condition(where_condition)
        columns = data.keys()
        binds_comma_separated = ', '.join([item +'=' + '%(' + item + ')s' for item in columns])
        # update_column_data = ''
        # for key in data.keys():
        #     update_column_data = f"{update_column_data}  {key} = {data[key]}"
        query = f"update {table_name} set {binds_comma_separated} {_where_condition} RETURNING {return_parameter} "
        return self.get_query_object(data, query,return_parameter)

    def get_query_object(self, _data, _query,id_column_name = None):
        obj_query_details = QueryObject(query=_query,data=_data, parameter_column = id_column_name )
        return obj_query_details

    def get_delete_query(self, table_name, where_condition,return_parameter):
        """
        The get_delete_query function takes in a table name and where condition.
        It returns the delete query for that table with the given where condition
        
        :param self: Access variables that belongs to the class
        :param table_name: Specify the table from which data is to be deleted
        :param where_condition: Specify the condition for which you want to delete rows from a table
        :return: The query to delete a row in the table
        :author: Arun kumar
        """
        _where_condition = self.get_where_condition(where_condition)
        query = f"delete from {table_name} {_where_condition} RETURNING {return_parameter} "
        return self.get_query_object(None, query,return_parameter)

    @multimethod
    def get_select_query(self, table_name:str, column_name:str, where_condition:str , order_by_clause:str = "order by start_date, event_id"):
        """
        The get_select_query function accepts three parameters:
            1. table_name - the name of the table to query
            2. column_name - the name of a column in that table to return as result data
            3. where_condition - a condition specifying which rows should be returned from this query
        
        :param self: Access variables that belongs to the class
        :param table_name: Specify the table name in the database
        :param column_name: Specify the column name that we want to select
        :param where_condition: Specify the condition to filter the data
        :return: A string that is a sql query to select the desired data from the table
        :author: Arun kumar
        """
        _where_condition = self.get_where_condition(where_condition)
        query = f"select {column_name} as result_data from {table_name} {_where_condition} {order_by_clause}"
        return self.get_query_object(None, query)

    @multimethod
    def get_select_query(self, table_name:str, where_condition:str , order_by_clause:str = "order by start_date, event_id"):

        _where_condition = self.get_where_condition(where_condition)
        query = f"select *  from {table_name} {_where_condition} {order_by_clause}"
        return self.get_query_object(None, query)


    @multimethod
    def get_select_query(self, table_name:str, column_names:list , order_by_clause:str = "order by start_date , event_id"):
            """
            The get_select_query function takes in three parameters: table_name, column_name, and where_condition.
            It returns a string that is the SQL query to be executed by the database. If no column name is specified, 
            it will return all columns of data from the specified table. If a single column name is provided as a list with only 
            one item (e.g., ['column']), it will return just that one piece of data from all rows in the table.
            
            :param self: Reference the class itself
            :param table_name: Specify the table to be queried
            :param column_name: Specify the columns that we want to select
            :param where_condition: Specify the condition to be used in the where clause
            :return: A string that is the sql query for a select statement
            :author: Arun kumar
            """
            
            if(column_names is None or len(column_names) == 0):
                query = f"select * from {table_name} {order_by_clause} "
            else:
                query = f"select {','.join(column_names)} from {table_name} {order_by_clause}"
            return self.get_query_object(None, query)

    @multimethod
    def get_select_query(self, table_name: str, column_names: list, where_condition: str, order_by_clause: str = "order by start_date , event_id"):
        """
        The get_select_query function takes in three parameters: table_name, column_name, and where_condition.
        It returns a string that is the SQL query to be executed by the database. If no column name is specified,
        it will return all columns of data from the specified table. If a single column name is provided as a list with only
        one item (e.g., ['column']), it will return just that one piece of data from all rows in the table.

        :param self: Reference the class itself
        :param table_name: Specify the table to be queried
        :param column_name: Specify the columns that we want to select
        :param where_condition: Specify the condition to be used in the where clause
        :return: A string that is the sql query for a select statement
        :author: Arun kumar
        """
        _where_condition = self.get_where_condition(where_condition)
        if (column_names is None or len(column_names) == 0):
            query = f"select * from {table_name} {_where_condition} {order_by_clause}"
        else:
            query = f"select {','.join(column_names)} from {table_name} {_where_condition} {order_by_clause}"
        return self.get_query_object(None, query)

    @multimethod
    def get_select_query(self, table_name:str , order_by_clause:str = "order by start_date , event_id"):

            query = f"select * from {table_name} {order_by_clause} "
            return self.get_query_object(None, query)

    def get_where_condition(self, where_condition):
        """
        The get_where_condition function takes in a where_condition string and returns the where condition if it exists.
        If the where_condition is empty or None, then an empty string is returned.
        
        :param self: Access variables that belongs to the class
        :param where_condition: Get the where condition for the sql query
        :return: The where condition for the select query
        :author: Arun kumar
        """
        if(where_condition == "" or where_condition == None):
            return ""
        else:
            return f" where {where_condition}"