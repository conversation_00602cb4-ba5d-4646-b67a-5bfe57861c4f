import json
import sys
import traceback as SysTraceback
from typing import List

import aiopg
import numpy
import pandas as pd
import pricesmart_common.constants as global_constants
from configuration.environment import environment
from enums.Enums import OperationStatus
from logger.logger import logger
from pricesmart.common.response.response_model import CommonResponseDataModel
from pricesmart.database.connections.postgres_connections import PostgresConnection
from pricesmart_common.utils import async_execute_query
from pricesmart.database.operations.exception_details import CodeException
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from psycopg2 import errors
from psycopg2.extensions import AsIs, register_adapter
from psycopg2.extras import RealDictCursor


class QueryExecutorPostgres:
    obj_postgres_client = None

    def __init__(self):
        """
        The __init__ function is called automatically every time the class is instantiated.
        It sets up all of the attributes that will be used by instances of this class, and
        makes calls to other methods that must also be defined.

        :param self: Represent the instance of the object itself
        :return: The object of the class that it is called upon
        :author: <PERSON><PERSON>[1533]
        """
        # The above code snippet in Python is creating an instance of the `CommonResponseDataModel`
        # class and assigning it to the `objCommonResponseDataModel` attribute of the current object
        # (assuming it is inside a class definition). This allows the current object to have access to
        # the methods and attributes of the `CommonResponseDataModel` class through the
        # `objCommonResponseDataModel` attribute.
        # self.objCommonResponseDataModel = CommonResponseDataModel()
        register_adapter(numpy.float64, self.addapt_numpy_float64)
        register_adapter(numpy.int64, self.addapt_numpy_int64)

    def addapt_numpy_float64(self, input_float_64):
        """
        The addapt_numpy_float64 function is a custom function that converts the numpy float64 data type to a python float.
        This is necessary because psycopg2 does not recognize the numpy data type and will throw an error if it encounters one.
        The addapt_numpy_float64 function was created by referencing https://github.com/psycopg/psycopg2/issues/613

        :param self: Reference the class itself
        :param input_float_64: Specify the data type of the input to this function
        :return: The input_float_64 as is
        :author: Arun Kumar[1533]
        """
        return AsIs(input_float_64)

    def addapt_numpy_int64(self, numpy_int64):
        """
        The addapt_numpy_int64 function is a custom function that converts the numpy int64 data type to a postgresql int8 data type.
        The numpy int64 data type is not supported by postgresql and therefore needs to be converted.

        :param self: Access the class attributes
        :param numpy_int64: Specify the type of data that is being passed into the function
        :return: The numpy_int64 variable as is
        :author: Arun Kumar[1533]
        """
        return AsIs(numpy_int64)

    async def execute_non_query_multiple(
        self, execution_object: List[QueryObject], name_of_business_operation: str
    ):
        """
        The execute_non_query_multiple function is used to execute multiple queries in a single transaction.
        The function takes two parameters:
            1) execution_object - A dictionary of queries and values that need to be executed.
            The key is the query and the value is the list of values for each query.

        :param self: Access variables that belongs to the class
        :param execution_object: Pass the query and its values
        :param name_of_business_operation:str: Store the name of the business operation being performed
        :return: A list of commonresponsedatamodel objects
        :author: Arun Kumar[1533]
        """
        parameter_value = None
        parameter_key = None
        self.failed_query = []
        self.failed_query_data = []
        list_of_ObjCommonResponseDataModel = []
        localCommonResponseDataModel = CommonResponseDataModel()
        try:
            logger.info(
                f" {environment.LOG_TITLE}: Bulk operation in single transaction"
            )
            async with aiopg.connect(dsn=environment.conn_string, timeout=300) as conn:
                async with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    print(cursor)
                    await cursor.execute(global_constants.BEGIN_TRANSACTION)
                    try:
                        for item in execution_object:
                            localCommonResponseDataModel = CommonResponseDataModel()
                            try:
                                if (
                                    parameter_value != None
                                    and item.parameter_column != None
                                    and item.parameter_column != ""
                                ):
                                    try:
                                        item.data = self.process_parameters(
                                            item.data, parameter_value, parameter_key
                                        )
                                    except Exception as e:
                                        print(e)
                                if item.data_type == "dict":
                                    await cursor.execute(item.query, item.data)
                                else:
                                    df = pd.DataFrame(item.data)
                                    df[parameter_key] = pd.to_numeric(df[parameter_key])
                                    try:
                                        for df_item in list(df.to_records()):
                                            await cursor.execute(item.query, df_item)
                                    except Exception as ex:
                                        print("Batch insert error:", ex)
                                    # cursor.executemany(item.query, df.values)
                                if (
                                    item.parameter_column != ""
                                    and item.parameter_column != None
                                ):
                                    parameter_value = (
                                        await cursor.fetchone()
                                    ) or parameter_value
                                    parameter_key = item.parameter_column
                                else:
                                    parameter_value = item.parameter_value
                                    parameter_key = item.parameter_column
                                localCommonResponseDataModel.data = parameter_value
                                localCommonResponseDataModel.status = (
                                    OperationStatus.SUCCESS
                                )
                                list_of_ObjCommonResponseDataModel.append(
                                    localCommonResponseDataModel
                                )
                            except Exception as ex:
                                print(ex)
                                await cursor.execute(
                                    global_constants.ROLLBACK_TRANSACTION
                                )
                                logger.info(
                                    f" {environment.LOG_TITLE}: Query: {item.query}, Values: {item.data}"
                                )
                                localCommonResponseDataModel.status = (
                                    OperationStatus.FAIL
                                )
                                localCommonResponseDataModel.message = "Failed"
                                localCommonResponseDataModel.exception = (
                                    self.get_exception_details(ex)
                                )
                                list_of_ObjCommonResponseDataModel.append(
                                    localCommonResponseDataModel
                                )
                                break
                        await cursor.execute(global_constants.COMMIT_SP)
                        await cursor.execute(global_constants.END_TRANSACTION)

                    finally:
                        await cursor.execute(global_constants.END_TRANSACTION)
        except Exception as ex:
            localCommonResponseDataModel = CommonResponseDataModel()
            localCommonResponseDataModel.status = OperationStatus.FAIL
            localCommonResponseDataModel.exception = self.get_exception_details(ex)
            list_of_ObjCommonResponseDataModel.append(localCommonResponseDataModel)
        return list_of_ObjCommonResponseDataModel

    def process_parameters(self, data, parameter_value, parameter_key):
        """
        The process_parameters function is used to replace the parameter value in the request body with a new value.
        The function takes three parameters:
            1) self - The object instance of this class. This is passed automatically by Python's method binding functionality.
            2) data - The request body that was sent by the client, which contains all of the parameters for our query and their values.
                For example, if we were querying a table called &quot;users&quot; and wanted to filter it based on some user_id, our data would look like this:

        :param self: Reference the class itself
        :param data: Store the data that is being processed
        :param parameter_value: Pass the value of the parameter that needs to be changed
        :param parameter_key: Specify the key in the data dictionary that needs to be updated
        :return: A dictionary with the parameter_value
        :author: Arun Kumar[1533]
        """
        for key, value in data.items():
            if key == parameter_key:
                for item, item_value in data[key].items():
                    data[key][item] = parameter_value[parameter_key]
        return data

    async def execute_non_query(self, query_object: QueryObject):
        """
        The execute_non_query function is used to execute a query that does not return any data.
        It takes two parameters:
            1) A string representing the SQL query to be executed.
            2) A dictionary containing the values for any placeholder parameters in the SQL statement.

        :param self: Access variables that belongs to the class
        :param query:str: Pass the query string to be executed
        :param data:dict: Pass the data to be inserted in the query
        :return: The query_response which is the number of rows affected by the query
        :author: Arun Kumar[1533]
        """
        self.objCommonResponseDataModel = CommonResponseDataModel()
        try:
            async with aiopg.connect(dsn=environment.conn_string, timeout=300) as conn:
                async with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    try:
                        await cursor.execute(global_constants.BEGIN_TRANSACTION)
                        if query_object.data == None or len(query_object.data) == 0:
                            await cursor.execute(query_object.query)
                        else:
                            await cursor.execute(query_object.query, query_object.data)
                            self.objCommonResponseDataModel.status = (
                                OperationStatus.SUCCESS
                            )
                            try:
                                self.objCommonResponseDataModel.data = (
                                    await cursor.fetchone()
                                )
                            except Exception as ex:
                                self.objCommonResponseDataModel.message = (
                                    "No value in DB"
                                )
                                self.objCommonResponseDataModel.status = (
                                    OperationStatus.SUCCESS_WITH_NO_RETURN
                                )
                    except errors.UniqueViolation as pg_ex_uk:
                        await cursor.execute(global_constants.ROLLBACK_TRANSACTION)
                        self.objCommonResponseDataModel.status = OperationStatus.FAIL
                        self.objCommonResponseDataModel.message = "Duplicate"
                        self.objCommonResponseDataModel.exception = (
                            self.get_exception_details(pg_ex_uk)
                        )
                    except Exception as ex:
                        await cursor.execute(global_constants.ROLLBACK_TRANSACTION)
                        self.objCommonResponseDataModel.status = OperationStatus.FAIL
                        self.objCommonResponseDataModel.message = "Failed"
                        self.objCommonResponseDataModel.exception = (
                            self.get_exception_details(ex)
                        )
                    finally:
                        await cursor.execute(global_constants.END_TRANSACTION)
        except Exception as ex:
            self.objCommonResponseDataModel.status = OperationStatus.FAIL
            self.objCommonResponseDataModel.exception = self.get_exception_details(ex)
        return self.objCommonResponseDataModel

    def get_exception_details(self, _exception):
        """
        The get_exception_details function is a helper function that takes in an exception and returns the error code,
        error message, and stack trace. This function is used to return the details of any errors that occur during processing.

        :param self: Access variables that belongs to the class
        :param _exception: Get the exception details
        :return: The error message and the stack trace
        :author: Arun Kumar[1533]
        """

        error_detail = SysTraceback.format_exc()
        ObjCodeException = CodeException()
        ObjCodeException.complete_error_stack = error_detail
        ObjCodeException.error_type = type(_exception).__name__
        try:
            if _exception.pgerror != None:
                ObjCodeException.error_message = _exception.pgerror
            else:
                ObjCodeException.error_message = error_detail
        except Exception as ex:
            ObjCodeException.error_message = error_detail
            logger.info(f" {environment.LOG_TITLE}: {ex}")
        (
            ObjCodeException.error_code,
            ObjCodeException.error_type,
            ObjCodeException.complete_error_stack,
        ) = sys.exc_info()
        return ObjCodeException

    def get_data(self, query_object):
        """
        The get_data function is used to retrieve data from the database.
        It takes a query as an argument and returns a pandas dataframe.

        :param self: Access variables that belongs to the class
        :param query: Query the database for a specific table
        :return: A pandas dataframe
        :author: Arun kumar
        """
        self.objCommonResponseDataModel = CommonResponseDataModel()
        try:
            obj_postgres_client = (
                PostgresConnection.get_postgres_sqlalchemy_connection()
            )
            if query_object.query == "multiple":
                multiple_data_result = {}
                for key in query_object.data:
                    result_dataframe = pd.read_sql(
                        query_object.data[key], obj_postgres_client
                    )
                    multiple_data_result[key] = json.loads(
                        result_dataframe.to_json(orient="records")
                    )
                self.objCommonResponseDataModel.data = multiple_data_result
            else:
                self.objCommonResponseDataModel.data = pd.read_sql(
                    query_object.query, obj_postgres_client
                )
            self.objCommonResponseDataModel.status = OperationStatus.SUCCESS
        except Exception as ex:
            print(ex)
            self.objCommonResponseDataModel.status = OperationStatus.FAIL
            self.objCommonResponseDataModel.message = "Failed"
            self.objCommonResponseDataModel.exception = self.get_exception_details(ex)

        return self.objCommonResponseDataModel

    async def get_data_async(self, query_object):
        """
        The get_data function is used to retrieve data from the database.
        It takes a query as an argument and returns a pandas dataframe.

        :param self: Access variables that belongs to the class
        :param query: Query the database for a specific table
        :return: A pandas dataframe
        :author: Arun kumar
        """
        self.objCommonResponseDataModel = CommonResponseDataModel()
        try:
            if query_object.query == "multiple":
                multiple_data_result = {}
                for key in query_object.data:
                    result_dataframe = await async_execute_query(
                        query_object.data[key], timelimit=600
                    )
                    multiple_data_result[key] = result_dataframe
                self.objCommonResponseDataModel.data = multiple_data_result
            else:
                query_result = await async_execute_query(
                    query_object.query, timelimit=600
                )
                self.objCommonResponseDataModel.data = pd.DataFrame(query_result)

            self.objCommonResponseDataModel.status = OperationStatus.SUCCESS
        except Exception as ex:
            print(ex)
            self.objCommonResponseDataModel.status = OperationStatus.FAIL
            self.objCommonResponseDataModel.message = "Failed"
            self.objCommonResponseDataModel.exception = self.get_exception_details(ex)

        return self.objCommonResponseDataModel

    def get_data_sqlalchemy(self, query):
        """
        The get_data_sqlalchemy function is used to get the data from postgres database using sqlalchemy.
        The function accepts query as an argument and returns result_set.

        :param self: Access variables that belongs to the class
        :param query: Pass the sql query to the function
        :return: A result set
        :author: Arun Kumar[1533]
        """
        engine = PostgresConnection.get_postgres_sqlalchemy_connection()
        result_set = engine.execute(query)
        return result_set
