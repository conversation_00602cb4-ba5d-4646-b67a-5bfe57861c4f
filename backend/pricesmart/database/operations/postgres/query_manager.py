from multimethod import multimethod
from pricesmart.database.operations.query_builder import PrepareSQLQueries
class QueryManager():
    def __init__(self):
        self.query_processor = PrepareSQLQueries()
        

    @multimethod
    async def get_dataframe(self, table_name:str):
        """
        The get_dataframe function accepts a table name as an argument and returns the dataframe of that table.

        :param table_name: Get the qualified name of the table
        :return: A dataframe with all the records from the table
        :author: <PERSON><PERSON> kumar
        """
        return self.query_processor.get_select_query(table_name, None)
        # query_result  , result_data_frame = self.query_executor_postgres.get_data(query)
        # return query_result ,result_data_frame

    @multimethod
    async def get_dataframe( self, table_name:str, where_condition:str):
        """
        The get_dataframe function accepts a table name and where condition as input.
        It returns the dataframe of the result set.

        :param table_name: Specify the table name that we want to get data from
        :param where_condition: Filter the dataframe
        :return: A dataframe with the query results
        :author: <PERSON><PERSON> kumar
        """
        return self.query_processor.get_select_query(table_name, None,where_condition=where_condition)
        # query_result,result_data_frame = self.query_executor_postgres.get_data(query)
        # return query_result,result_data_frame

    @multimethod
    async def get_dataframe(self, table_name:str, columns:list):
        """
        The get_dataframe function accepts a table name and column names as input,
        and returns a dataframe containing the specified columns from the specified table.

        :param table_name: Specify the table name to be queried
        :param columns: Specify the columns that should be returned
        :return: A dataframe containing the data from the table
        :author: Arun kumar
        """
        return self.query_processor.get_select_query(table_name, columns)
        # query_result,result_data_frame = self.query_executor_postgres.get_data(query)
        # return query_result,result_data_frame

    @multimethod
    async def get_dataframe( self,table_name:str, columns:list, where_condition:str):
        """
        The get_dataframe function accepts a table name, column names and where condition as input.
        It returns the dataframe of the result set.

        :param table_name: Specify the table name
        :param columns: Specify which columns to select from the table
        :param where_condition: Filter the data that is returned from the database
        :return: A dataframe with the result of a query
        :author: Arun kumar
        """
        return self.query_processor.get_select_query(table_name, columns,where_condition)
        # query_result,result_data_frame = self.query_executor_postgres.get_data(query)
        # return query_result,result_data_frame

    async def update_table_data(self,table_name:str, data:dict , where_condition:str, return_parameter:str):
        """
        The update_table_data function updates the data in a table.
        The function takes three parameters:
            1) self - the object that is invoking this method (i.e., some instance of a class).  This parameter must be provided and does not have a default value.
            2) table_name - The name of the database table to update data from, as a string.  This parameter must be provided and does not have a default value.
            3) data - A dictionary containing key-value pairs for each column/value pair to update in the database table specified by 'table_name'.  Each key should match up
        
        :param self: Access the class variables
        :param table_name:str: Specify the table name
        :param data:dict: Update the data in the table
        :param where_condition:str: Specify the where condition in the update query
        :return: The result of the update query
        :author: Arun kumar
        """
        return self.query_processor.get_update_query(table_name, data, where_condition,return_parameter)
        # update_result = await self.query_executor_postgres.execute_non_query(query)
        # return update_result

    async def insert_data_to_table(self,table_name:str, data, id_column_name : str = ""):
        """
        The insert_data_to_table function inserts data into a table.
        
        :param self: Access the class attributes and methods
        :param table_name:str: Specify the table name in which we want to insert data
        :param data:dict: Insert the data into the table
        :param id_column_name : str: Specify the column name of the primary key
        :return: The result of the insert query
        :author: Arun kumar
        """
        return self.query_processor.get_insert_query(table_name, data, id_column_name)
        # insert_result = await self.query_executor_postgres.execute_non_query(query,data,id_column_name)
        # return insert_result

    async def delete_table_data(self,table_name:str , where_condition:str, parameter_column : str):
        """
        The delete_table_data function is used to delete data from a table.
        The function takes two parameters:
            1) table_name - the name of the table that we want to delete data from.
            2) where_condition - this parameter specifies what rows should be deleted.  If no condition is specified, all rows will be deleted.
        
        :param self: Access the class attributes
        :param table_name:str: Specify the table name
        :param where_condition:str: Specify the condition on which the data will be deleted
        :return: The number of rows deleted
        :author: Arun kumar
        """
        return self.query_processor.get_delete_query(table_name, where_condition,parameter_column)
        # delete_result = await self.query_executor_postgres.execute_non_query(query)
        # return delete_result

    async def get_column_data(self,table_name:str, column_name:str , where_condition:str):
        """
        The get_column_data function accepts a table name, column name and where condition as input parameters.
        It returns the data from the database for that particular table, column and where condition
        
        :param self: Access the attributes and methods of the class in python
        :param table_name:str: Specify the table name
        :param column_name:str: Specify the column name that is to be queried
        :param where_condition:str: Get the data for a specific column
        :return: The data from the column of a table
        :author: Arun kumar
        """
        return self.query_processor.get_select_query(table_name, column_name, where_condition)
        # result_data_array = await self.query_executor_postgres.get_data(query)
        # return list(result_data_array["result_data"])

    def get_table_data(self, table_name: str, column_names: list, where_condition: str):
        """
        The get_table_data function accepts a table name, column names and where condition as input parameters.
        It returns the query result of the select query executed on Postgres database.
        
        :param self: Reference the class itself
        :param table_name: str: Specify the table name
        :param column_name: list: Specify the columns that we want to retrieve from the table
        :param where_condition: str: Specify the where condition for the select query
        :return: The query result of the select statement
        :author: Arun kumar
        """
        print(f"table_name: {table_name}, column_name: {column_names}, where_cond: {where_condition}")
        if column_names is None:
            return self.query_processor.get_select_query(table_name, where_condition)
        return self.query_processor.get_select_query(table_name, column_names, where_condition)
        # query_result= self.query_executor_postgres.get_data(query)
        # return query_result

