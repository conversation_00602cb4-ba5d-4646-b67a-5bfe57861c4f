from pricesmart_common.staticclass import StaticClass
from multimethod import multimethod
from pricesmart.database.operations.postgres.execution_query_object import QueryObject
from pricesmart.modules.events.events_model import Calendar, EventProductHierarchy, EventRequest, TransformationInfo, EventEditValidate
from pricesmart.database.operations.postgres.query_manager import QueryManager
from pricesmart.common.requests.request_model import CommonRequestDataModel
import pandas as pd
from pricesmart.modules.events.event_queries import EventQueries
from configuration.environment import environment
from dateutil.relativedelta import relativedelta
from enums.Enums import CRUDOperation
from pricesmart.modules.events.entities.store_hierarchy import StoreHierarchyMeta
from pricesmart.modules.events.entities.product_hierarchy import ProductHierarchyMeta
from pricesmart.modules.events.entities.event_backup import EventBackupMeta


class PerformCurdOperations ():
    def __init__(self):
        """
        The __init__ function is called when a new instance of the class is created.
        It initializes all of the variables that are part of the class, and it can take arguments
        (in addition to self) that initialize these variables.
        
        :param self: Access variables that belongs to the class
        :param _module_name:str: Store the module name
        :return: A dictionary of the module information
        :author: <PERSON><PERSON>[1533]
        """
        self.db_manager = QueryManager()

    async def insert(self,input_data: CommonRequestDataModel):
        """
        The insert function inserts a new event into the database.
        It takes in an CommonRequestDataModel object and returns a tuple containing the insert result and refreshed data.

        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass the event attributes to the insert function
        :return: The insert_result which is the id of the inserted data
        :author: Arun Kumar[1533]
        """
        try:
            if hasattr(input_data.associated_module,"transformation_info") and input_data.associated_module.transformation_info.key_name != '':
                transformed_object = await self.insert_transformation(input_data)
                if transformed_object:
                    transformed_object.data_type = 'dataframe'
                return transformed_object
            else:
                if StaticClass.get_client_qualified_name(input_data.associated_module.table_name) == 'public.temp_event_details':
                    input_data.attributes = {input_data.parameter_column_name: input_data.attributes[input_data.parameter_column_name]}
                insert_result = await self.db_manager.insert_data_to_table(
                    f"{StaticClass.get_client_qualified_name(input_data.associated_module.table_name)}", input_data.attributes, input_data.parameter_column_name)
                insert_result.parameter_column = input_data.parameter_column_name
                return insert_result
        except Exception as ex:
            print("transformation not required")
            insert_result = await self.db_manager.insert_data_to_table(
                    f"{StaticClass.get_client_qualified_name(input_data.associated_module.table_name)}", input_data.attributes, input_data.parameter_column_name)
            insert_result.parameter_column = input_data.parameter_column_name
            return insert_result

    async def insert_transformation(self,input_data: CommonRequestDataModel):
        """
        The insert_transformation function is a helper function that takes in the input_data and transforms it into a dataframe.
        The transformed dataframe is then converted to dictionary format and stored as the attributes of the input_data object.
        
        
        :param self: Access the class attributes and methods
        :param input_data: CommonRequestDataModel: Pass the data that will be inserted into the database
        :return: The transformed dataframe
        :author: Arun Kumar[1533]
        """
        transformation_info: TransformationInfo = input_data.associated_module.transformation_info
        df_cols = []
        if transformation_info.dependent_key_column != "": df_cols.append(transformation_info.dependent_key_column)
        if transformation_info.key_name != "": df_cols.append(transformation_info.key_name)
        if transformation_info.Value_name != "": df_cols.append(transformation_info.Value_name)
        if input_data.associated_module.type_of_object_column_name != "": df_cols.append(input_data.associated_module.type_of_object_column_name)
        # df_cols = [transformation_info.dependent_key_column, transformation_info.key_name, transformation_info.Value_name, input_data.associated_module.type_of_object_column_name]
        transformed_dataframe = pd.DataFrame(columns=df_cols)
        for item in input_data.attributes:
            for inner_item in input_data.attributes[item]:
                # data_row = [input_data.dependent_parameter_name, item, inner_item, input_data.associated_module.type_of_object]
                data_row = []
                if input_data.dependent_parameter_name != "" : data_row.append(input_data.dependent_parameter_name)
                if item != "" : data_row.append(item)
                if inner_item != "" : data_row.append(inner_item)
                if input_data.associated_module.type_of_object != "": data_row.append(input_data.associated_module.type_of_object)

                row1 = pd.Series(data_row, index=transformed_dataframe.columns)
                transformed_dataframe = pd.concat([transformed_dataframe, row1.to_frame().T], ignore_index=True )
        if transformed_dataframe.empty == False:
            input_data.attributes = transformed_dataframe.to_dict()
            input_data.associated_module.transformation_info.key_name = ""
            return await self.insert(input_data)
        else:
            return None

    async def update(self, input_data: CommonRequestDataModel):
        """
        The update function updates the event data in the database.
        It takes an CommonRequestDataModel as input and returns a tuple of (update_result, refreshed_data).
        The update result is a boolean value that indicates whether or not the update was successful. 
        The refreshed data is an updated list of events from the database.
        
        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass in the id and event_attributes
        :return: The update result and refreshed data
        :author: Arun Kumar[1533]
        """
        condition = ''
        if(type(input_data.id) == type([])):
            condition = f"{input_data.associated_module.id_column_name} in ({','.join([ str(item)  for item in input_data.id])})"
        else:
            condition = f"{input_data.associated_module.id_column_name}={input_data.id}"
        update_result = await self.db_manager.update_table_data(
           f"{StaticClass.get_client_qualified_name(input_data.associated_module.table_name)}", input_data.attributes["attributes"], condition,input_data.parameter_column_name)
        return update_result

    async def delete(self, input_data: CommonRequestDataModel):
        """
        The delete function is used to delete an event from the database.
        It takes in a CommonRequestDataModel object and returns a boolean value indicating whether or not the deletion was successful.
        If refresh_data is true, it will also return all of the data after deleting.
        
        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass the data that is used to filter the table
        :return: A tuple
        :author: Arun Kumar[1533]
        """
        condition = self.get_where_condition(input_data)
        delete_result = await self.db_manager.delete_table_data(
            f"{StaticClass.get_client_qualified_name(input_data.associated_module.table_name)}", condition,parameter_column=input_data.parameter_column_name)
        return delete_result

    def get_where_condition(self, input_data: CommonRequestDataModel):
        """
        The get_where_condition function is a helper function that takes in an input_data object and returns the where condition for the query.
        The input_data object contains information about which table to query, what column to use as a key, and what value should be used as the key.
        
        
        :param self: Access variables that belongs to the class
        :param input_data: Pass the id of the record that needs to be deleted
        :return: The where condition that is used to filter the data
        :author: Arun Kumar[1533]
        """
        key_column_name = ""
        if (input_data.parameter_column_name != None):
            key_column_name = input_data.parameter_column_name
        else:
            key_column_name = input_data.associated_module.id_column_name

        condition = ''
        if(type(input_data.id) == type([])):
            condition = f"{key_column_name} in ({','.join(input_data.id)})"
        else:
            condition = f"{key_column_name}={input_data.id}"

        if input_data.operation == CRUDOperation.DELETE and (isinstance(input_data.associated_module, StoreHierarchyMeta) or isinstance(input_data.associated_module, ProductHierarchyMeta)):
            condition = condition + f" and {input_data.associated_module.type_of_object_column_name} = '{input_data.associated_module.type_of_object}'"

        return condition

    @multimethod
    async def get(self, input_data: CommonRequestDataModel):
        """
        The get function is used to retrieve data from the database.
        It takes in a dictionary of input_data and returns a list of dictionaries containing the requested data.
        
        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass the data that is used to filter the results
        :return: A tuple of a boolean and the data
        :author: Arun Kumar[1533]
        """
        
        query_result= self.get_filter_data(input_data,True)
        return query_result

    @multimethod
    async def get(self, event_validate_request: EventEditValidate):
        query = self.validate_event_with_new_date_and_stores(event_validate_request.event_id, event_validate_request.start_date, event_validate_request.end_date, event_validate_request.store_id, event_validate_request.product_h2_id, event_validate_request.ad_type)
        obj_query_details = QueryObject(query=query, data={})
        return obj_query_details

    @multimethod
    async def get(self, events_request : EventRequest):
        """
        The get function is used to retrieve data from the database.
        It takes in a dictionary of input_data and returns a list of dictionaries containing the requested data.
        
        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass the data that is used to filter the results
        :return: A tuple of a boolean and the data
        :author: Arun Kumar[1533]
        """
        query_type = "multiple"
        data_queries = {}
        if(events_request.return_type_flat):
            data_queries["events"]= self.get_events_based_on_ids_query(events_request.event_ids)
            if(events_request.with_promo == True):
                data_queries["promos"] = self.get_events_based_promos_query(events_request.event_ids)
            if(events_request.with_product_hierarchy == True):
                data_queries["product_hierarchy"]= self.get_events_products_hierarchy_query(events_request.event_ids)
            if(events_request.with_store_hierarchy == True):
                data_queries["store_hierarchy"]= self.get_events_store_hierarchy_query(events_request.event_ids)
            query_type = "multiple"
            obj_query_details = QueryObject(query=query_type,data=data_queries) 
            return obj_query_details
        else:
            obj_event_queries = EventQueries()
            query= obj_event_queries.get_events_query(events_request.event_ids,events_request.with_promo,events_request.with_product_hierarchy,events_request.with_store_hierarchy)
            obj_query_details = QueryObject(query=query,data={}) 
            return obj_query_details

    def get_events_based_promos_query(self , event_ids , is_complex:bool = False):
        """
        The get_events_based_promos_query function accepts a list of event_ids and returns the query that will be used to
        retrieve all events with the specified event_ids. The function is called by get_events_based_promos().
        
        :param self: Allow the method to reference variables that belongs to the class
        :param event_ids: Get the events that have a promo
        :return: The query that is used to get the events based on the promos
        :author: Arun Kumar[1533]
        """
        obj_event_queries = EventQueries()
        query = ''
        if is_complex:
            query = obj_event_queries.get_events_promo_query(event_ids,is_complex)
        else:
            query = obj_event_queries.get_events_promo_query(event_ids)
        return query

    def get_events_based_on_ids_query(self , event_ids):
        """
        The get_events_based_on_ids_query function accepts a list of event ids and returns the corresponding events.
        The function is used to retrieve the events based on the given event ids.
        
        :param self: Allow the method to reference attributes or methods of the class
        :param event_ids: Get the events based on the event ids
        :return: The events based on the event ids provided
        :author: Arun Kumar[1533]
        """
        obj_event_queries = EventQueries()
        return obj_event_queries.get_events_query(event_ids)

    def get_events_products_hierarchy_query(self,event_ids ,  is_complex : bool = False):
        """
        The get_events_products_hierarchy_query function accepts a list of event_ids and returns the query that will be used to retrieve the products associated with those events.
                
        :param self: Allow the method to reference attributes or methods of the class
        :param event_ids: Pass the event_ids to get_events_product_hierarchy query
        :return: A query that contains the products hierarchy for all events in event_ids
        :author: Arun Kumar[1533]
        """
        obj_event_queries = EventQueries()
        if is_complex:
            return obj_event_queries.get_events_product_hierarchy_query(event_ids,is_complex)
        else:
            return obj_event_queries.get_events_product_hierarchy_query(event_ids)

    def validate_event_with_new_date_and_stores(self, event_id, start_date, end_date, store_ids, product_h2_ids, ad_type):
        obj_event_queries = EventQueries()
        return obj_event_queries.get_validate_event_edit_query(event_id, start_date, end_date, store_ids, product_h2_ids, ad_type)

    def get_events_store_hierarchy_query(self,event_ids, is_complex : bool = False):
        """
        The get_events_store_hierarchy_query function accepts a list of event_ids and returns the store hierarchy for each event.
        The function is used to create a dataframe that can be used to merge with other dataframes in order to create an analysis
        of events by store.
        
        :param self: Allow the method to reference attributes or methods of the class
        :param event_ids: Specify which events to retrieve
        :return: The query to get the store hierarchy for a list of events
        :author: Arun Kumar[1533]
        """
        obj_event_queries = EventQueries()
        if is_complex:
           return obj_event_queries.get_events_store_hierarchy_query(event_ids,is_complex)
        else:
            return obj_event_queries.get_events_store_hierarchy_query(event_ids)

    @multimethod
    async def get(self, filters: dict):
        """
        The get function is used to get the data from the database.
        It takes in a dictionary of filters and returns a QueryObject with query and data attributes.
        The query attribute contains the SQL query string that will be executed by sqlalchemy engine, while 
        the data attribute contains any additional information that may be needed for post processing on 
        the client side.
        
        :param self: Access variables that belongs to the class
        :param filters: dict: Pass the filters to the get function
        :return: A list of all the events that match the filters
        :author: Arun Kumar[1533]
        """
        obj_event_queries = EventQueries()
        queries = [];
        product_hierarchies = filters["filters"]["product_hierarchy"]
        store_hierarchies = filters["filters"]["store_hierarchy"]
        calendar_filter:Calendar = filters["filters"]["calendar"]
        current_start_date = calendar_filter["start_date"]
        current_end_date = calendar_filter["end_date"]
        channel_type = filters["filters"]["channel_type"]
        ad_type = filters["filters"]["ad_type"]
        current_end_date = calendar_filter["end_date"]
        events = calendar_filter["event"]
        promotions = calendar_filter["promotion"]

        # current_year_query = self.get_filters_query(obj_event_queries, queries, product_hierarchies, store_hierarchies, current_start_date, current_end_date,channel_type,ad_type)
        current_year_query = self.get_events_query(obj_event_queries, queries, product_hierarchies, current_start_date, current_end_date, channel_type, ad_type)

        if filters["show_last_year"]:
            last_year_start_date = StaticClass.add_years(current_start_date,-1)
            last_year_end_date = StaticClass.add_years(current_end_date,-1)
            # last_year_query = self.get_filters_query(obj_event_queries, queries, product_hierarchies, store_hierarchies, last_year_start_date, last_year_end_date)
            last_year_query = self.get_events_query(obj_event_queries, queries, product_hierarchies, last_year_start_date, last_year_end_date, channel_type, ad_type)
            data_queries = {"current-year": current_year_query , "last-year" :last_year_query}
            obj_query_details = QueryObject(query="multiple", data=data_queries)
            return obj_query_details
        else:            
            obj_query_details = QueryObject(query=current_year_query, data={})
            return obj_query_details

    def get_events_query(self, obj_event_queries, queries, product_hierarchies, current_start_date, current_end_date, channel_type, ad_type):
        filter_string_channel = ""
        filter_string_ad = ""
        if channel_type and len(channel_type) > 0:
            filter_string_channel = f"em.channel_type in ({','.join(str(x) for x in channel_type)})"
        if ad_type and len(ad_type) > 0:
            filter_string_ad = f" and em.ad_type in ({','.join(str(x) for x in ad_type)})"

        hierarchy_filter_list = [" and h.hierarchy_type = 'product_hierarchy'"] if (filter_string_channel or filter_string_ad) else [" h.hierarchy_type = 'product_hierarchy'"]
        hierarchy_promo_filter_list = [" and h.hierarchy_type = 'product_hierarchy'"]
        for hierarchy, values in product_hierarchies.items():
            if values:
                hierarchy_filter_list.append(
                    f"""and h.hierarchy_level_name = '{hierarchy}' 
                        and h.hierarchy_name in ({','.join(str(x) for x in values)})
                    """
                )
                hierarchy_promo_filter_list.append(
                    f"""and h.hierarchy_level = '{hierarchy}' 
                        and h.hierarchy_value in ({','.join(str(x) for x in values)})
                    """
                )

        query = f"""
            with event_list_cte as (
                select 
                    em.event_id 
                from 
                    public.event_master em
                left join 
                    public.event_hierarchy h on h.event_id = em.event_id 
                where 
                    {filter_string_channel}
                    {filter_string_ad}
                    {' '.join(str(x) for x in hierarchy_filter_list)}
                    and em.start_date >= to_date('{current_start_date}', 'yyyy/mm/dd')
                    and em.end_date <= to_date('{current_end_date}', 'yyyy/mm/dd') 
                    and em.is_deleted = 0
                group by 
                    em.event_id
            ),
            event_products_cte as (
                select 
                    pm.event_id, 
                    count(*) as event_products_count
                from 
                    public.promo_products pp
                left join
                    public.promo_master pm on pp.promo_id = pm.promo_id 
                where 	
                    pm.event_id in (select distinct event_id from event_list_cte)
                group by
                    pm.event_id
            ),
            event_stores_cte as (
                select 
                    event_id, 
                    count(*) as event_stores_count
                from 
                    public.event_store es 
                where 	
                    es.event_id in (select distinct event_id from event_list_cte)
                group by
                    es.event_id
            ),
            event_promos_cte as (
                select 
                    pm.event_id,
                    count(distinct pm.promo_id) as event_all_promo_count,
                    max(is_under_processing) as is_under_processing
                from 
                    public.promo_master pm
                where 	
                    pm.event_id in (select distinct event_id from event_list_cte)
                group by
                    pm.event_id 
            ),
            event_promos_filtered_cte as (
                select 
                    pm.event_id,
                    count(distinct pm.promo_id) as event_filtered_promo_count 
                from 
                    public.promo_master pm
                left join 
                    public.promo_hierarchy h on h.promo_id = pm.promo_id 
                where 	
                    pm.event_id in (select distinct event_id from event_list_cte)
                    {' '.join(str(x) for x in hierarchy_promo_filter_list)}
                group by
                    pm.event_id 		
            ),
            event_rules_cte as (
                select 
                    pm.event_id,
                    sum(pr.markdown_budget) AS finalized_markdown_budget,
                    sum(pr.revenue_target) AS finalized_revenue_target,
                    sum(pr.gross_margin_target) AS finalized_gross_margin_target,
                    sum(pr.units_target) AS finalized_units_target
                from 
                    public.promo_master pm
                left join 
                    public.ps_rules pr on pm.promo_id = pr.promo_id 
                where 	
                    pm.event_id in (select distinct event_id from event_list_cte)
                group by
                    pm.event_id 
            ),
            finalized_event_metrics_cte as (
                select 
                    prf.event_id,
                    sum(prf.sales_units) AS finalized_sales_units,
                    sum(prf.baseline_sales_units) AS finalized_baseline_sales_units,
                    sum(prf.incremental_sales_units) AS finalized_incremental_sales_units,
                    sum(prf.revenue) AS finalized_revenue,
                    sum(prf.baseline_revenue) AS finalized_baseline_revenue,
                    sum(prf.incremental_revenue) AS finalized_incremental_revenue,
                    sum(prf.margin) AS finalized_margin,
                    sum(prf.baseline_margin) AS finalized_baseline_margin,
                    sum(prf.incremental_margin) AS finalized_incremental_margin,
                    sum(prf.promo_spend) AS finalized_promo_spend,
                    CASE
                        WHEN sum(prf.baseline_margin) = 0::double precision THEN NULL::double precision
                        ELSE sum(prf.incremental_margin) / sum(prf.baseline_margin) * 100::double precision
                    END AS performance
                from 
                    simulation.ps_recommended_finalized prf 
                where 
                    event_id in (select distinct event_id from event_list_cte)
                group by
                    prf.event_id
            ),
            ia_proj_event_metrics_cte as (
                select 
                    prip.event_id,
                    sum(prip.sales_units) AS recommended_sales_units,
                    sum(prip.baseline_sales_units) AS recommended_baseline_sales_units,
                    sum(prip.incremental_sales_units) AS recommended_incremental_sales_units,
                    sum(prip.revenue) AS recommended_revenue,
                    sum(prip.baseline_revenue) AS recommended_baseline_revenue,
                    sum(prip.incremental_revenue) AS recommended_incremental_revenue,
                    sum(prip.margin) AS recommended_margin,
                    sum(prip.baseline_margin) AS recommended_baseline_margin,
                    sum(prip.incremental_margin) AS recommended_incremental_margin,
                    sum(prip.promo_spend) AS recommended_promo_spend
                from 
                    simulation.ps_recommended_ia_projected prip
                where 
                    prip.event_id in (select distinct event_id from event_list_cte)
                group by
                    prip.event_id
            ),
            event_metrics_cte as (
                select 
                    elc.event_id,
                    prf.finalized_sales_units,
                    prf.finalized_baseline_sales_units,
                    prf.finalized_incremental_sales_units,
                    prf.finalized_revenue,
                    prf.finalized_baseline_revenue,
                    prf.finalized_incremental_revenue,
                    prf.finalized_margin,
                    prf.finalized_baseline_margin,
                    prf.finalized_incremental_margin,
                    prf.finalized_promo_spend,
                    prf.performance,
                    prip.recommended_sales_units,
                    prip.recommended_baseline_sales_units,
                    prip.recommended_incremental_sales_units,
                    prip.recommended_revenue,
                    prip.recommended_baseline_revenue,
                    prip.recommended_incremental_revenue,
                    prip.recommended_margin,
                    prip.recommended_baseline_margin,
                    prip.recommended_incremental_margin,
                    prip.recommended_promo_spend
                from
                    event_list_cte elc
                left join
                    finalized_event_metrics_cte prf on prf.event_id = elc.event_id
                left join 
                    ia_proj_event_metrics_cte prip on prip.event_id = elc.event_id	
            )
            select 
                em.event_id,
                em.name,
                to_char(em.start_date, 'MM/dd/YYYY') as start_date,
                to_char(em.end_date, 'MM/dd/YYYY') as end_date,
                em.customer_segment,
                em.description,
                em.channel,
                em.channel_type,
                em.ad_type,
                em.marketing_channel,
                em.status as is_locked,
                to_char(em.created_at, 'mm/dd/yyyy HH24:mi:ss') as created_at,
                to_char(em.updated_at, 'mm/dd/yyyy HH24:mi:ss') as updated_at,
                em.is_deleted,
                to_char(em.submit_offer_by, 'MM/dd/YYYY') as submit_offer_by,
                em.marketing_notes,
                em.event_type,
                em.event_objective,
                em.event_objective_description,
                --
                uc.name as created_by,
                uu.name as updated_by,
                --
                emc.finalized_sales_units,
                emc.finalized_baseline_sales_units,
                emc.finalized_incremental_sales_units,
                emc.finalized_revenue,
                emc.finalized_baseline_revenue,
                emc.finalized_incremental_revenue,
                emc.finalized_margin,
                emc.finalized_baseline_margin,
                emc.finalized_incremental_margin,
                emc.finalized_promo_spend,
                coalesce (emc.performance, 0) as performance,
                CASE
                    WHEN emc.performance < (- 5::double precision) THEN 'toxic'::text
                    WHEN emc.performance >= (- 5::double precision) AND emc.performance <= 5::double precision THEN 'average'::text
                    WHEN emc.performance IS NULL THEN 'average'::text
                    ELSE 'good'::text
                END AS status,
                CASE
                    WHEN em.end_date < now() THEN 'Completed'::text
                    WHEN now() >= em.start_date AND now() <= em.end_date THEN 'Ongoing'::text
                    WHEN now() < em.start_date THEN 'Upcoming'::text
                    ELSE ''::text
                END AS event_lifecycle_status,
                emc.recommended_sales_units,
                emc.recommended_baseline_sales_units,
                emc.recommended_incremental_sales_units,
                emc.recommended_revenue,
                emc.recommended_baseline_revenue,
                emc.recommended_incremental_revenue,
                emc.recommended_margin,
                emc.recommended_baseline_margin,
                emc.recommended_incremental_margin,
                emc.recommended_promo_spend,
                --
                epc.event_products_count,
                esc.event_stores_count,
                coalesce(eprc.event_all_promo_count, 0) as event_all_promo_count,
                coalesce(eprc.is_under_processing, 0) as is_under_processing,
                coalesce(eprfc.event_filtered_promo_count, 0) as event_promo_count,
                 --
                erc.finalized_markdown_budget,
                erc.finalized_revenue_target,
                erc.finalized_gross_margin_target,
                erc.finalized_units_target
            from 
                public.event_master em 
            left join 
                um.users uc on uc.id = em.created_by 
            left join 
                um.users uu on uu.id = em.updated_by 
            left join 	
                event_metrics_cte emc on emc.event_id = em.event_id 
            left join 
                event_products_cte epc on epc.event_id = em.event_id 
            left join 
                event_stores_cte esc on esc.event_id = em.event_id 
            left join 
                event_promos_cte eprc on eprc.event_id = em.event_id 
            left join 
                event_promos_filtered_cte eprfc on eprfc.event_id = em.event_id 
            left join 
                event_rules_cte erc on erc.event_id = em.event_id
            where 
                em.event_id in (select distinct event_id from event_list_cte)
        """

        return query

    def get_filters_query(self, obj_event_queries, queries, product_hierarchies, store_hierarchies, current_start_date, current_end_date,channel_type,ad_type):
        """
        The get_filters_query function is used to create a query that will be used to get the events from the database.
        The function takes in 5 parameters:
        obj_event_queries - An object of EventQueries class which contains all queries related to event data.
        queries - A list of queries that are created by calling functions in EventQueries class. These functions return a query based on the given parameters and they also append these queries into this list after creation. 
        product_hierarchies - A dictionary containing product hierarchies as keys and lists of products under each hierarchy as values, if any such hierarchies were requested by
        
        :param self: Reference the class in which the function is called
        :param obj_event_queries: Get the queries for each event
        :param queries: Store the queries that are generated by the get_filters_query function
        :param product_hierarchies: Get the events based on product hierarchy
        :param store_hierarchies: Get the events based on store hierarchy
        :param current_start_date: Get the events for a specific date
        :param current_end_date: Get the events for a particular date
        :return: A query which is a union of all the queries that were created by passing different hierarchy values
        :author: Arun Kumar[1533]
        """
        is_hierarchy_requested = False
        if(product_hierarchies != None):
            for hierarchy in product_hierarchies:
                if(len(product_hierarchies[hierarchy]) > 0):
                    is_hierarchy_requested = True
                    queries.append(obj_event_queries.get_events_based_on_hierarchy_query(environment.client_schema ,product_hierarchies[hierarchy],hierarchy,"product_hierarchy",current_start_date , current_end_date))
        if(store_hierarchies != None):
            for store_hierarchy in store_hierarchies:
                if(len(store_hierarchies[store_hierarchy]) > 0):
                    is_hierarchy_requested = True
                    queries.append(obj_event_queries.get_events_based_on_hierarchy_query(environment.client_schema ,store_hierarchies[store_hierarchy],store_hierarchy,"store_hierarchy",current_start_date , current_end_date))
        if is_hierarchy_requested == False:
            queries.append(obj_event_queries.get_events_based_on_hierarchy_query(environment.client_schema ,[],"","",current_start_date , current_end_date))
        final_query = obj_event_queries.get_union_of_queries(queries)
        final_query = str(final_query).replace("\n" , "")
        filter_string_channel = ""
        filter_string_ad = ""
        if channel_type and len(channel_type) >0:
            filter_string_channel = f" AND A.channel_type in ({','.join(str(x) for x in channel_type)})"
        if ad_type and len(ad_type) >0:
            filter_string_ad = f" AND A.ad_type in ({','.join(str(x) for x in ad_type)})"
              
        query = f"""
        Select A.* , B.product_hierarchy, B.store_hierarchy,B.promos 
        from ({final_query}) A, {environment.client_schema}.vw_event_details_complex_structure B
        where A.event_id = B.event_id {filter_string_channel} {filter_string_ad}  order by order_start_date desc
        """
        
        return query


    def get_data_from_query(self, query):
        print (query)

    def get_filter_data(self, input_data: CommonRequestDataModel, filter_with_id:bool =False):
        """
        The get_filter_data function is used to get the data from the database based on the filter provided by user.
        The function takes in input_data as an argument and returns a list of dictionaries containing all matching records.
        
        :param self: Access variables that belongs to the class
        :param input_data: CommonRequestDataModel: Pass the data attributes and filter parameters
        :param filter_with_id:bool: Determine if the filter should be applied to the id
        :return: A list of dictionaries
        :author: Arun Kumar[1533]
        """
        columns = []
        if filter_with_id:
            condition = self.get_where_condition(input_data)
        if input_data.filter is not None and len(input_data.filter) > 0:
            condition = condition + " and " if len(condition) > 0 else '' + ' and '.join('='.join((key, val))
                                    for (key, val) in input_data.filter.items())
        if(type(input_data.attributes) == 'dict'):
            columns = list(input_data.attributes.keys())
        elif  (type(input_data.attributes) == 'list'):
            columns =input_data.attributes
        elif  (type(input_data.attributes) == 'str'):
            columns =[input_data.attributes]
        query_result= self.db_manager.get_table_data(
            f"{StaticClass.get_client_qualified_name(input_data.associated_module.table_name)}", columns, condition)
        return query_result




