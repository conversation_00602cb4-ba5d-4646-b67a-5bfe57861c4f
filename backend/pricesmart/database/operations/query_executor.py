import traceback
import aiopg
import pandas as pd
from psycopg2.extras import RealDictCursor
from pricesmart.database.connections.postgre_connections import database_connections
from pricesmart.configuration.environment import environment

class QueryExecutorPostgres(database_connections):
    obj_postgres_client = None

    def __init__(self) -> None:
        super().__init__()
        self.obj_postgres_client = super().get_postgres_connection()

    async def execute_non_query(self,query):
        """
        The execute_non_query function is used to execute a query that does not return any rows.
        It takes in a string as an argument, which is the SQL query to be executed. It returns True if the 
        query executes successfully and False otherwise.
        
        :param self: Access the class attributes
        :param query: Pass the query to be executed
        :return: A cursor object
        :author: Arun kumar
        """
        
        try:
            async with aiopg.connect(dsn=super().dsn, timeout=300) as conn:
                async with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    print(cursor)
                    await cursor.execute("BEGIN Transaction;")
                    await cursor.execute(query)
                    await cursor.execute("END Transaction;")
            return True
        except Exception as Ex:
            error_detail = traceback.format_exc()
            print(f"Error Details: {error_detail}")
            return False

    def get_data(self,query):
        """
        The get_data function is used to retrieve data from the database.
        It takes a query as an argument and returns a pandas dataframe.
        
        :param self: Access variables that belongs to the class
        :param query: Query the database for a specific table
        :return: A pandas dataframe
        :author: Arun kumar
        """
        
        data = pd.read_sql(query, self.obj_postgres_client)
        return data


query_executor_postgres = QueryExecutorPostgres()
