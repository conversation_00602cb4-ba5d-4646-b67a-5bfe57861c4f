import asyncio

import aiopg
from configuration.environment import environment
from google.cloud import bigquery
from pricesmart.database import database_constants as common_constants
from py_linq import Enumerable

gbq_client = bigquery.Client(project="saksfifthavenue-27032024")

def run_blocking_gbq_query(query):
    return gbq_client.query(query).result()


async def execute_async_gbq_query(query):
    loop = asyncio.get_running_loop()
    response = await loop.run_in_executor(None, run_blocking_gbq_query, query)
    columns = [col_schema.name for col_schema in response.schema]
    result = []
    for row in response:
        record = {col: row[col] for col in columns}
        result.append(record)
    return result


async def async_execute_query(query, transaction_mode=False, timelimit=300.00)-> list:
    async with aiopg.connect(dsn=environment.conn_string, timeout=timelimit) as conn:
        async with conn.cursor() as cursor:
            if transaction_mode:
                await cursor.execute(common_constants.BEGIN_TRANSACTION)
                await cursor.execute(query)
                await cursor.execute(common_constants.END_TRANSACTION)
            else:
                await cursor.execute(query)
            return await fetchallasync(cursor)


async def async_execute_stored_procedure(sp_name, *procedure_inputs, timelimit=300.00):
    async with aiopg.connect(dsn=environment.conn_string, timeout=timelimit) as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(common_constants.BEGIN_SP)
            await cursor.callproc(sp_name, list(procedure_inputs))
            results = await fetchallasync(cursor)
            await cursor.execute(common_constants.COMMIT_SP)
            await cursor.execute(common_constants.END_SP)
            return results


async def fetchallasync(cursor):
    if not cursor.description:
        return []

    enumerable = Enumerable(
        await cursor.fetchall()
    )


    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in enumerable]
