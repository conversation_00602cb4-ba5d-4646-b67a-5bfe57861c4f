from pydantic import BaseModel


class CodeException (BaseModel):
    error_code : str = "None"
    error_type : str = "None"
    line_num: int = -1
    error_message: str = "None"
    complete_error_stack : str = "None"
    executed_query : str ="None" 
    executed_query_data :dict = {}


class QueryResult(BaseModel):
    query_response: object
    status:bool = False
    message:str = "None"
    exception : CodeException = CodeException()




