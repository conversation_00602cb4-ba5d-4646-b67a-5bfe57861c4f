import string
from typing import overload
from multipledispatch import dispatch
from multimethods import multimethod
from pricesmart_common.staticclass import StaticClass
from pricesmart.database.operations.query_executor import query_executor_postgres

class DatabaseManager():

    overload = multimethod

    @overload(str, int)
    def get_dataframe(table_name):
        """
        The get_dataframe function accepts a table name as an argument and returns the dataframe of that table.

        :param table_name: Get the qualified name of the table
        :return: A dataframe with all the records from the table
        :author: <PERSON><PERSON> kumar
        """
        query = f"select * from {StaticClass.get_qualified_name(table_name)}"
        result_data_frame = query_executor_postgres.get_data(query)
        return result_data_frame

    @overload(str, str)
    def get_dataframe(table_name, where_condition):
        """
        The get_dataframe function accepts a table name and where condition as input.
        It returns the dataframe of the result set.

        :param table_name: Specify the table name that we want to get data from
        :param where_condition: Filter the dataframe
        :return: A dataframe with the query results
        :author: <PERSON><PERSON> kumar
        """

        query = f"select * from {StaticClass.get_qualified_name(table_name)} where {where_condition}"
        result_data_frame = query_executor_postgres.get_data(query)
        return result_data_frame

    @overload(str, list)
    def get_dataframe(table_name, columns):
        """
        The get_dataframe function accepts a table name and column names as input,
        and returns a dataframe containing the specified columns from the specified table.

        :param table_name: Specify the table name to be queried
        :param columns: Specify the columns that should be returned
        :return: A dataframe containing the data from the table
        :author: Arun kumar
        """

        query = f"select {columns} from {StaticClass.get_qualified_name(table_name)} "
        result_data_frame = query_executor_postgres.get_data(query)
        return result_data_frame

    @overload(str, list, str)
    def get_dataframe(table_name, columns, where_condition):
        """
        The get_dataframe function accepts a table name, column names and where condition as input.
        It returns the dataframe of the result set.

        :param table_name: Specify the table name
        :param columns: Specify which columns to select from the table
        :param where_condition: Filter the data that is returned from the database
        :return: A dataframe with the result of a query
        :author: Arun kumar
        """

        query = f"select {columns} from {StaticClass.get_qualified_name(table_name)} where {where_condition}"
        result_data_frame = query_executor_postgres.get_data(query)
        return result_data_frame

    def update_table_data(self,table_name:str, data:dict , where_condition:str):
        """
        The update_table_data function is used to update the data in a table.
        The function takes three parameters:
            1) self - this is the instance of the class that calls this method, and it's used to access attributes of that class instance.
            2) table_name - This parameter represents the name of a table in our database.  It should be passed as a string value (e.g., &quot;users&quot;).  The value can also be accessed as an attribute on self (e.g., self._table_name).
            3) data - This parameter represents new values we want to update into existing rows in our

        :param self: Access the class variables
        :param table_name:str: Specify the table to be updated
        :param data:dict: Pass the data that is to be updated in the table
        :param where_condition:str: Specify the where condition in the update query
        :return: The result of the update query
        :author: Arun kumar
        """

        update_column_data = ''
        for key in data.keys:
            update_column_data = f"{update_column_data}  {key} = {data[key]}"
        query = f"update {StaticClass.get_qualified_name(table_name)} set {update_column_data} where {where_condition}"
        update_result = query_executor_postgres.execute_non_query(query)
        return update_result

    def insert_data_to_table(self,table_name:str, data:dict):
        """
        The insert_data_to_table function inserts data into a table.

        :param self: Access the class attributes and methods
        :param table_name:str: Specify the table name in which the data is to be inserted
        :param data:dict: Insert the data into the table
        :return: The number of rows affected by the insert statement
        :author: Arun kumar
        """

        insert_column_data = ''
        for key in data.keys:
            insert_column_data = f"{insert_column_data}  {key} = {data[key]}"
        query = f"insert into  {StaticClass.get_qualified_name(table_name)} values {insert_column_data} "
        insert_result = query_executor_postgres.execute_non_query(query)
        return insert_result

    def delete_table_data(self,table_name:str , where_condition:str):
        """
        The delete_table_data function deletes all the data from a table.
        The function takes two parameters:
            1) self - The instance of the class that calls this method. This parameter is automatically passed in when called by an instance of a class.
            2) table_name - The name of the table to delete data from.

        :param self: Access the class variables
        :param table_name:str: Specify the table name
        :param where_condition:str: Specify the condition for which data is to be deleted
        :return: The number of rows deleted from the table
        :author: Arun kumar
        """
        query = f"delete from {StaticClass.get_qualified_name(table_name)} where {where_condition}"
        delete_result = query_executor_postgres.execute_non_query(query)
        return delete_result

    def get_data(self,table_name, column_name , where_condition):
        """
        The get_data function accepts three arguments:
            1. self - the object that is invoking this function
            2. table_name - the name of the table to be queried from
            3. column_name - a string containing all of the column names to be returned in query result set, separated by commas (no spaces) 
            e.g., &quot;columnA,columnB&quot; or &quot;tableB__colC,tableD__colF&quot; 

        :param self: Access the class variables
        :param table_name: Specify the table name
        :param column_name: Specify the column name from which we want to fetch data
        :param where_condition: Filter the data
        :return: The data in the form of a list
        :author: Arun kumar
        """
        query = f"select {column_name} from {StaticClass.get_qualified_name(table_name)} where {where_condition}"
        result_data_array = query_executor_postgres.get_data(query)
        return list(result_data_array)

