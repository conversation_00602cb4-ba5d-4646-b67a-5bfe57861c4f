from configuration.environment import environment


UTF_ENCODING_KEY = "utf-8"
EMAIL_KEY = environment.EMAIL_KEY
EMAIL_ENDPOINTS = "https://api.mailgun.net/v3/impactanalytics.co/messages"
API_PREFIX = "/api/v1"
API_TITLE = "BigLots"
PROJECT_ID = "pricesmart-e1078"
PROJECT_RESOURCE = "biglots"
BEGIN_TRANSACTION = "Begin Transaction;"
END_TRANSACTION = "End Transaction;"
BEGIN_SP = "BEGIN"
COMMIT_SP = "COMMIT"
END_SP = "END"

# API Tags
PRODUCT_API_TAG = 'products'
PROMOTION_API_TAG = 'promotions'
EVENT_API_TAG = 'events'
AUTHENTICATION_API_TAG = 'authentication'
ACCESS_MANAGEMENT_API_TAG = 'access_management'



