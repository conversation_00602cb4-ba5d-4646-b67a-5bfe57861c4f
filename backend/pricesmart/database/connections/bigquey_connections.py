import enum
import psycopg2
from google.cloud import bigquery
from configuration.environment import environment
from logger import logger
class bigquery_connection:
    dsn = environment.conn_string
    postgres_client = None
    big_query_client: object = None
    dataset: str = environment.dataset

    def get_big_query_connection(self):
        """
        The get_big_query_connection function creates a connection to the BigQuery client.
        It uses the environment variables set in .env file to create a connection.
        
        
        :param self: Refer to the object of the class
        :return: The big_query_client
        :author: <PERSON><PERSON>[1533]
        """
        
        logger.debug(
            f"project id:{environment.project_id} and dataset {environment.dataset}"
        )
        self.big_query_client = bigquery.Client(project=environment.project_id)
        return self.big_query_client


obj_bigquery_connection = bigquery_connection()
big_query_connection = obj_bigquery_connection.get_big_query_connection()
