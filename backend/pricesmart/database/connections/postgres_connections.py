from sqlalchemy import create_engine
import psycopg2
from configuration.environment import environment

class PostgresConnection():
    def __new__(cls):
         raise TypeError('Static classes cannot be instantiated')

    postgres_client = None
    connection_string = environment.conn_string
    connection_url_sqlalchemy = f"postgresql://{environment.db_user}:{environment.db_password}@{environment.db_host}:{environment.db_port}/{environment.db_name}"
    # postgreSQL_pool = psycopg2.pool.SimpleConnectionPool(1, 20,dbname=connection_string['database'],
    #                                               user=connection_string['user'],
    #                                               password=connection_string['password'],
    #                                               host=connection_string['host'],
    #                                               port=connection_string['port'])
    @staticmethod
    def get_postgres_connection():
        """
        The get_postgres_connection function is a helper function that returns the postgres client connection.
        It checks to see if the postgres_client has been created and is open, if not it creates one.

        :param self: Access variables that belongs to the class
        :return: A connection to the postgres database
        :author: <PERSON><PERSON>[1533]
        """
        if(PostgresConnection.connection_string != ""):
            if not PostgresConnection.postgres_client or (PostgresConnection.postgres_client and not PostgresConnection.postgres_client.closed):
                postgres_client = psycopg2.connect(PostgresConnection.connection_string)
        return postgres_client


    @staticmethod
    def get_postgres_sqlalchemy_connection():
        """
        The get_postgres_sqlalchemy_connection function returns a SQLAlchemy connection object to the Postgres database.
        The function is called by other functions in this file, and it is not meant to be used directly.

        :return: A sqlalchemy engine object
        :author: Arun Kumar[1533]
        """
        if(PostgresConnection.connection_url_sqlalchemy != ""):
            postgres_client_sqlalchemy = create_engine(PostgresConnection.connection_url_sqlalchemy)
        return postgres_client_sqlalchemy
