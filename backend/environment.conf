[local]
deployment_env=dev
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=pricesmart_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash= False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-generic-report-generator-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-dev
SAKS_URL_PREFIX = https://pricesmart.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=dev
LOG_TITLE = pricesmart-generic-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://pricesmart.devs.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate


[saks-dev]
deployment_env=dev
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=saksfifthavenue_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-saks-markdown-report-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-dev
SAKS_URL_PREFIX = https://saksfifthavenue.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=dev
LOG_TITLE = saks-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit




[saks-demo]
deployment_env=dev
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=saksfifthavenue_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-saks-markdown-report-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-dev
SAKS_URL_PREFIX = https://saksfifthavenue.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=dev
LOG_TITLE = saks-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://pricesmart.demo.impactsmartsuite.com/pricesmart/api/v1/server-callback 
OPT_EVENT_EDIT_API_URL = https://saksfifthavenue.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit



[saks-test]
deployment_env=test
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=saksfifthavenue_test
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash= False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://saksfifthavenue.test.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://saksfifthavenue.test.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-saks-markdown-report-test
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-test
#DOWNSTREAM_CLOUD_FUNCTION_URL = http://127.0.0.1:8080/downstream_function
SAKS_URL_PREFIX = https://saksfifthavenue.test.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=test
LOG_TITLE = saks-promo-test
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://saksfifthavenue.test.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://saksfifthavenue.test.impactsmartsuite.com/pricesmart/api/v1/server-callback
OPT_EVENT_EDIT_API_URL = https://saksfifthavenue.test.impactsmartsuite.com/pricesmart/promo/optimization/event-edit

[saks-uat]
deployment_env=uat
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=saksoff5th_uat
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash= False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://saksoff5th.uat.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://saksoff5th.uat.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-saks-markdown-report-uat
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-uat
SAKS_URL_PREFIX = https://saksoff5th.uat.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-uat
ENVIRONMENT=uat
LOG_TITLE = saks-promo-uat
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://saksfifthavenue.uat.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://saksfifthavenue.uat.impactsmartsuite.com/pricesmart/api/v1/server-callback
OPT_EVENT_EDIT_API_URL = https://saksfifthavenue.uat.impactsmartsuite.com/pricesmart/promo/optimization/event-edit



[pricesmart-dev]
deployment_env=dev
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=pricesmart_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-generic-report-generator-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-dev
SAKS_URL_PREFIX = https://pricesmart.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=dev
LOG_TITLE = pricesmart-generic-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://pricesmart.devs.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://pricesmart.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate





[pricesmart-test]
deployment_env=test
meta_schema=metaschema
secret_project = saksfifthavenue-27032024
secret_version=latest
secret_id=pricesmart_test
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = saksfifthavenue
project_id = saksfifthavenue-27032024
project_resource = saksfifthavenue
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://pricesmart.test.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://pricesmart.test.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/pricesmart-generic-report-generator-test
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-central1-saksfifthavenue-27032024.cloudfunctions.net/downstream-saks-cf-dev
SAKS_URL_PREFIX = https://pricesmart.test.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-saks-dev
ENVIRONMENT=test
LOG_TITLE = pricesmart-generic-promo-test
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://pricesmart.test.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://pricesmart.test.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://pricesmart.test.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://pricesmart.test.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate