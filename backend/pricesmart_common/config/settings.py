from functools import lru_cache

from pricesmart_common.config import base as b
from pricesmart_common.utils import get_env_config_value
from dotenv import load_dotenv

load_dotenv()


class SettingsLoader(b.Config<PERSON>oader):
    async def get_config(self, **kwargs):
        environment, config = get_env_config_value()
        return config[environment]


class ItemLoader(b.ConfigItemLoader):
    def __init__(self, config):
        self.config = config

    async def get_config_item(self, key):
        return self.config.get(key, None)


@lru_cache()
def get_settings():
    environment, config = get_env_config_value()
    return config[environment]
