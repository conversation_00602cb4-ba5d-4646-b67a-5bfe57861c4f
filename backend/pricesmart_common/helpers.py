from pydantic import PydanticUserError


def validate_non_id_fields(values):
    event_ids = values.get("event_ids")
    promo_ids = values.get("promo_ids")
    start_date = values.get('start_date')
    end_date = values.get('end_date')

    if not (event_ids or promo_ids) and not (start_date and end_date):
        if not start_date:
            raise PydanticUserError(
                "The 'start_date' field is required",
                code="value_error.missing"
            )
        if not end_date:
            raise PydanticUserError(
                "The 'end_date' field is required",
                code="value_error.missing"
            )
    return values