from datetime import datetime
from configuration.environment import environment
class ParameterProcessing():

    async def process_system_parameters(self,parameter_text:str , data_type:str = "string"):
        self.final_value = None
        if(parameter_text == "{?SystemDate}"):
            current_datetime = datetime.now()
            self.final_value =  current_datetime.strftime(environment.app_date_format)
        elif(parameter_text == "{?SystemDateTime}"):
            current_datetime = datetime.now()
            self.final_value =  current_datetime.strftime(environment.app_date_time_format)
        elif(parameter_text == "{?NULL}"):
            self.final_value = None
        elif(parameter_text == "{?EmptyString}"):
            self.final_value = ""
        elif(parameter_text == "{?CurrentUserID}"):
            self.final_value = int(await self.get_current_user_id())
        else:
            self.final_value = parameter_text
        return self.final_value

    async def get_current_user_id(self):
        return 1