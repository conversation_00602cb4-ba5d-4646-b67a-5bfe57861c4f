GET_CLIENT_CONFIGURATION = """
    select
        module,
        config_name, 
        config_value,
        config_value_type
    from
        price_promo.tb_tool_configurations
    {where_str}
"""


GET_MODULE_DISPLAY_CONFIGURATION = """
    SELECT 
        id,
        fe_identifier,
        fe_order_id,
        fe_display_name,
        fe_component_type,
        fe_placeholder_text,
        fe_is_mandatory,
        fe_extra_config,
        fe_component_group,
        be_is_master_attr
    FROM 
        price_promo.attribute_master
    WHERE 
        is_active = true
        and module = {module}
    ORDER BY 
        module, fe_component_group, fe_order_id
    """