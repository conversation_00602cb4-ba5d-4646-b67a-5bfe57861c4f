from fastapi.routing import APIRouter
from fastapi.responses import FileResponse
from fastapi import Depends
from app.dependencies import UserDependency
from pricesmart_common import constants as global_constants

from pricesmart_common import utils as common_utils 
from pricesmart_common import service as common_service
from pricesmart_common.data import get_client_configuration as get_client_configuration_cached
from logger.logger import logger

common_router = APIRouter(tags=[global_constants.COMMON_API_TAG])



@common_router.get(path="/configuration")
async def get_client_configuration():
    """
    Get client configuration settings.
    
    Returns:
        dict: Response containing:
            - data (dict): Client configuration data
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    data = await common_service.get_client_configuration(fe_config=True)
    return common_utils.create_response(
        data=data
    )


@common_router.get(path="/template-download")
async def template_download(
    type: str
):
    if type not in global_constants.TEMPLATE_LOCATIONS:
        return common_utils.create_response(
            message="Invalid template type",
            status=400
        )

    file_path, file_name = global_constants.TEMPLATE_LOCATIONS[type]

    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@common_router.get(path="/display-configuration")
async def get_module_display_configuration(module: str):
    data = await common_service.get_module_display_configuration(module)
    return common_utils.create_response(
        data=data
    )

@common_router.post("/clear-cache")
async def clear_client_configuration_cache(
    user_id: int = Depends(UserDependency),
):
    
    # wipe our LRU cache
    get_client_configuration_cached.cache_clear()

    # audit
    logger.info(f"Client configuration cache cleared by user_id={user_id}")

    # generic message in case we want to clear other caches here in the future
    return common_utils.create_response(
        message="Cache cleared successfully",
        status=200
    )