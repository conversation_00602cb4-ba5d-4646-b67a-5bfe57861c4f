from typing import Any
from pricesmart_common import queries as common_queries
from pricesmart_common import utils as common_utils
from collections import defaultdict
from enums.Enums import ConfigModuleEnum, ConfigKeyEnum
from async_lru import alru_cache
from pricesmart_common import models as common_models
from collections import defaultdict

def format_client_config_data(data):
    config_data = defaultdict(dict)
    for cd in data:
        config_data[cd["module"]][cd["config_name"]] = (
            common_utils.convert_to_type(
                cd["config_value"],
                cd["config_value_type"]
            )
        )
    return config_data


@alru_cache(ttl=3600)
async def get_client_configuration(fe_config: bool = False):
    where_str = "where is_used_by_fe = true" if fe_config else ""
    query = common_queries.GET_CLIENT_CONFIGURATION.format(where_str=where_str)
    data = await common_utils.async_execute_query(query)
    return format_client_config_data(data)


async def get_config_value(module: ConfigModuleEnum, key: ConfigKeyEnum)-> Any:
    config_data = await get_client_configuration()
    return config_data[module.value][key.value]


def format_display_config(data):
    # Group components by their group ID
    groups = defaultdict(list)
    for row in data:
        groups[row['fe_component_group']].append(row)
    
    # Format into final structure
    formatted_data = []
    for group_id, components in groups.items():
        group_data = {
            "id": f"group-{group_id}",
            "order": group_id,
            "attributes": []
        }
        
        # Sort components by order_id
        components.sort(key=lambda x: x['fe_order_id'])
        
        # Format each component
        for component in components:
            component_config = {
                "id": component['fe_identifier'],
                "order": component['fe_order_id'],
                "label": component['fe_display_name'],
                "type": component['fe_component_type'],
                "placeholder": component['fe_placeholder_text'],
                "required": component['fe_is_mandatory'],
                "is_master_attr": component['be_is_master_attr']
            }
            
            # Add extraConfig if it exists
            if component['fe_extra_config']:
                component_config["extraConfig"] = component['fe_extra_config']
                
            group_data["attributes"].append(component_config)
            
        formatted_data.append(group_data)
    
    return formatted_data


async def get_module_display_configuration(module: str):
    query = common_queries.GET_MODULE_DISPLAY_CONFIGURATION.format(module=common_utils.get_str_repr(module))
    data = await common_utils.async_execute_query(query)
    return format_display_config(data)
