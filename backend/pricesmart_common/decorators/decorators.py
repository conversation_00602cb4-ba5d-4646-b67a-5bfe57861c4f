import inspect
import json
import os
import pickle
import time
from functools import wraps

import bcrypt as bc
from configuration.environment import environment
from logger.logger import decorator_logger

api_data_cache = {}
cache_hash_keys = []


class FunctionInfo:
    @staticmethod
    def func_pass_cache_dict(cache, first_function_frame):
        # first_frame = (
        #     traceback.format_stack()[0].split("<module>")[-1].split("(")[0]
        # ).strip()
        for dict_key, dict_value in cache[first_function_frame].items():
            print(dict_key.func_name, dict_key.func_args, dict_value)

    @staticmethod
    def function_time_logging(calling_function):
        @wraps(calling_function)
        def decorator_function(*args, **kwargs):
            function_start_time = time.time()
            result = calling_function(*args, **kwargs)
            function_end_time = time.time()
            if environment.log_function_execution_time:
                decorator_logger.info(
                    f" {environment.LOG_TITLE} :  {calling_function.__name__}: function ran in:: { (function_end_time - function_start_time)}s"
                )
            return result

        return decorator_function

    @staticmethod
    def app_cache_decorator(calling_function):
        @wraps(calling_function)
        async def cache_decorator(*args, **kwargs):
            params = inspect.getfullargspec(calling_function)
            all_parameters = []
            for param in params.args:
                param_value = kwargs.get(param)
                request_res = await param_value.json()
                for pp in request_res:
                    all_parameters.append("".join(request_res[pp]))
            string_of_parameters = "".join(all_parameters)
            hash_of_parameters = hashed(
                string_of_parameters + calling_function.__name__
            )
            if environment.cache_enabled and hash_of_parameters in api_data_cache:
                result_set = api_data_cache[hash_of_parameters]
            else:
                result_set = await calling_function(*args, **kwargs)
                if environment.cache_enabled:
                    FunctionInfo.set_cache_data_by_key(hash_of_parameters, result_set)
                    api_data_cache[hash_of_parameters] = result_set
            return result_set

        def hashed(string_value_to_hash):
            _hashed = bc.hashpw(string_value_to_hash, "$2a$04$6LJ8Rszo6ce7sO8vo6cG6e")
            return _hashed.replace("/", "")

        return cache_decorator

    @staticmethod
    def set_cache_data_by_key(key, data):
        print("Data Storing in Cache ---------", key)
        if key not in cache_hash_keys:
            cache_hash_keys.append(key)
            # save_cache_details(key)
        rootUrl = f"{os.getcwd()}/ApplicationCache/{environment.client_name}-{environment.deployment_env} "
        if not os.path.exists(rootUrl):
            os.makedirs(rootUrl)
        db = {}
        db[key] = data
        filename = f"{rootUrl}/{key}.pickle"
        check_file = os.path.isfile(filename)
        if check_file:
            os.remove(filename)
        with open(filename, "ab") as dbfile:
            pickle.dump(db, dbfile)
        api_data_cache[key] = data
        try:
            with open("cache_keys.json", "w") as f:
                json.dump(cache_hash_keys, f)
        except Exception as ex:
            pass


function_time_logging = FunctionInfo.function_time_logging
app_cache_decorator = FunctionInfo.app_cache_decorator
