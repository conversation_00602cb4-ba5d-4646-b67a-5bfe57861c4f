import os
from configuration.environment import environment
from datetime import datetime, date, timedelta
import bcrypt
import pricesmart_common.constants as global_constants
from dateutil.relativedelta import relativedelta


class StaticClass:
    def __new__(cls):
        raise TypeError('Static classes cannot be instantiated')

    @staticmethod
    def get_client_qualified_name(_objectName: str):
        """
        The get_client_qualified_name function takes a string representing the name of an object in the client schema and returns
        the qualified name of that object in the client schema. For example, if you pass get_client_qualified_name(&quot;User&quot;) as an argument, 
        it will return &quot;Client.User&quot;. This is useful for creating SQL queries to insert or update data into tables.
        
        :param _objectName:str: Pass the name of the table that is being queried
        :return: The qualified name of the client schema
        :author: <PERSON><PERSON>[1533]
        """
        return f"{environment.client_schema}.{_objectName}"

    @staticmethod
    def get_meta_qualified_name(_objectName: str):
        """
        The get_meta_qualified_name function takes a string representing the name of an object in the meta schema and returns
        the qualified name for that object. The qualified name is used to identify objects in the meta schema.
        
        :param _objectName: str: Pass the name of the object that is being qualified
        :return: The schema and name of the object in a single string
        :author: Arun Kumar[1533]
        """
        return f"{environment.meta_schema}.{_objectName}"

    @staticmethod
    def get_current_directory( append_to_current_dir : str = None):
        """
        The get_current_directory function returns the current directory of the application.
        If an argument is passed, it will append that argument to the current directory and return it.

        :param append_to_current_dir : str: Append a string to the current directory
        :return: The current working directory
        :author: Arun Kumar[1533]
        """
        directory = os.getcwd()
        if append_to_current_dir != None:
            return os.path.join (directory , append_to_current_dir)
        else:
            return directory

    @staticmethod
    def get_export_directory():
        """
        The get_export_directory function returns the directory where all exported files will be stored.
        The function is called by other functions in this module, and it does not need to be called directly.
        
        :return: The directory where the exported files will be stored
        :author: Arun Kumar[1533]
        """

        return StaticClass.get_current_directory() + "/exports"

    @staticmethod
    def get_download_directory():
        """
        The get_download_directory function returns the path to the download directory.
        The download directory is located in the current working directory of this script.
        
        :return: The download directory of the current user
        :author: Arun Kumar[1533]
        """

        return StaticClass.get_current_directory() + "/downloads"

    @staticmethod
    def get_file_with_path(file_name: str, file_extension: str, append_to_current_dir : str = None):
        """
        The get_file_with_path function is a helper function that returns the path to a file with the given name and extension.
        It takes in three parameters:
            1) file_name - The name of the file, without an extension.
            2) file_extension - The extension of the filename, without a period.  For example, &quot;log&quot; or &quot;txt&quot;.
            3) append_to_current_dir (optional): If you want to append something to your current directory path instead of using it as-is, pass in this parameter.

        :param file_name: str: Specify the name of the file to be created
        :param file_extension: str: Determine the file extension to be used when creating a new file
        :param append_to_current_dir : str: Append a directory to the current working directory
        :return: A string that is the path to a file with the given name, extension and directory
        :author: Arun Kumar[1533]
        """

        if (file_extension == "log"):
            return os.path.join(f"{StaticClass.get_current_directory()}/logs", file_name + "." + file_extension)
        if (append_to_current_dir != None):
            return os.path.join(StaticClass.get_current_directory(),append_to_current_dir, file_name + "." + file_extension)
        return os.path.join(StaticClass.get_current_directory(), file_name + "." + file_extension)

    @staticmethod
    def get_current_date_and_time(format: str):
        """
        The get_current_date_and_time function returns the current date and time in a specified format.
        The function takes one argument, format, which is a string that specifies how the date and time should be formatted.
        The function returns a string containing the current date and time in the specified format.
        
        :param format:str: Specify the format of the date and time that is returned
        :return: The current date and time in the format specified by the user
        :author: Arun Kumar[1533]
        """

        now = datetime.now()
        formatted_current_date_time = datetime.now().strftime(format)
        return formatted_current_date_time

    @staticmethod
    def hash_password(password):
        """
        The hash_password function takes a password as input and returns the hashed version of that password.
        The hashed version is returned in string format.
        
        :param password: Store the password that is entered by the user
        :return: A hashed password
        :author: Arun Kumar[1533]
        """
        hashed_password = bcrypt.hashpw(
            password.encode(global_constants.UTF_ENCODING_KEY), bcrypt.gensalt()
        )
        hashed_password = hashed_password.decode(global_constants.UTF_ENCODING_KEY)
        return hashed_password

    @staticmethod
    def add_years(d, years):
        """Return a date that's `years` years after the date (or datetime)
        object `d`. Return the same calendar date (month and day) in the
        destination year, if it exists, otherwise use the following day
        (thus changing February 29 to March 1).

        """
        try:
            return d.replace(year=d.year + years)
        except ValueError:
            return d + (date(d.year + years, 1, 1) - date(d.year, 1, 1))

    @staticmethod
    def get_parsed_date(input_date: str) -> date:
        """
        The get_parsed_date function takes in a string of the format mm/dd/yyyy and returns a date object.
        If no input is given, it defaults to the global constants start_date and end_date.
        
        :param input_date:str: Pass the date that is going to be parsed
        :return: The parsed date from the input_date
        :author: Arun Kumar[1533]
        """

        _month: str
        _day: str
        _year: str
        default_start_date: str
        default_end_date: str
        if input_date != "" or input_date is not None:
            _month, _day, _year = input_date.split("/")
        else:
            if input_date.__name__ == "start_date":
                default_start_date = global_constants.start_date
                _month, _day, _year = default_start_date.split("/")
            if input_date.__name__ == "end_date":
                default_end_date = global_constants.end_date
                _month, _day, _year = default_end_date.split("/")
        parsed_date = date(year=int(_year), month=int(_month), day=int(_day))
        return parsed_date

    @staticmethod
    def get_default_dates():
        current_date = datetime.now().date()
        end_date = current_date + timedelta(days=114)
        #end_date = date(2023,5,31)
        start_date = current_date + timedelta(days=0)
        return start_date, end_date

    @staticmethod
    def parse_quarter(quarter: str):
        """
        The parse_quarter function takes a string as an argument and returns the start date and end date of that quarter.
        The function is called by the parse_date_range function to get the start and end dates for each quarter.
        
        :param quarter:str: Determine which quarter to parse
        :return: The start and end dates of the selected quarter
        :author: Arun Kumar[1533]
        """
        selected_quarter_date = datetime.now()
        currQuarter = int((selected_quarter_date.month - 1) / 3 + 1)
        if quarter == "current":
            start_date, end_date = StaticClass.get_current_quarter_start_end_dates(selected_quarter_date, currQuarter)
            return start_date, end_date
        if quarter == "last":
            start_date, end_date = StaticClass.get_current_quarter_start_end_dates(selected_quarter_date, currQuarter)
            start_date = start_date + relativedelta(months=-3, days=-1)
            end_date = end_date + relativedelta(months=-3, days=-1)
            return start_date, end_date

    @staticmethod
    def get_current_quarter_start_end_dates(selected_quarter_date, currQuarter):
        """
        The get_current_quarter_start_end_dates function accepts a date and returns the start and end dates of that quarter.
        For example, if you pass in a date of March 15th, 2020 it will return (2020-03-01) and (2020-03-31).
        
        
        :param selected_quarter_date: Get the start and end dates of the selected quarter
        :param currQuarter: Determine which quarter of the year we are currently in
        :return: The start and end date for the current quarter
        :author: Arun Kumar[1533]
        """
        start_date = datetime(selected_quarter_date.year, 3 * currQuarter - 2, 1)
        end_date = start_date + relativedelta(months=3, days=-1)
        return start_date, end_date

    @staticmethod
    def get_quarter_dates():
        """
        The get_quarter_dates function returns the start and end date of the quarter.
        The function takes no arguments, but it does use global variables defined in 
        the rest_api module. If a quarter is not specified, then the current quarter is used.
        
        :return: The start and end date of the quarter
        :author: Arun Kumar[1533]
        """
        start_date: date
        end_date: date
        try:
            quarter_value = global_constants.QUARTER
            if quarter_value != "":
                start_date, end_date = StaticClass.parse_quarter(quarter_value)
            elif global_constants.START_DATE == "default" and global_constants.END_DATE == "default":
                start_date, end_date = StaticClass.get_default_dates()
            elif global_constants.START_DATE != "" and global_constants.END_DATE != "":
                start_date = StaticClass.get_parsed_date(global_constants.START_DATE)
                end_date = StaticClass.get_parsed_date(global_constants.END_DATE)
            else:
                start_date, end_date = StaticClass.parse_quarter("current")
        except Exception as ex:
            print(ex)
            start_date, end_date = StaticClass.parse_quarter("current")
        return start_date, end_date
