from fastapi import BackgroundTasks
from fastapi.routing import APIRouter
from app.dependencies import UserDependency
from product_group import models as product_group_models
from product_group import service as product_group_service
from product_group import constants as product_group_constants
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as global_constants

router = APIRouter(tags=[product_group_constants.PRODUCT_GROUP_API_TAG])

@router.post(
    "/get-product-groups"
)
async def get_product_groups(
    product_hierarchy_filter: product_group_models.FetchSpecificProductGroups
):
    data = await product_group_service.get_product_groups(
        product_hierarchy_filter
    )
    return common_utils.create_response(data=data)


@router.post(
    "/product-group",
)
async def insert_product_group(
    product_group: product_group_models.ProductGroupWrite,
    user_id: UserDependency
):
    await product_group_service.insert_product_group(product_group, user_id)
    return common_utils.create_response(
        message=f"Product Group {product_group.product_group_name} created successfully."
    )

@router.post(
    "/product-group-download"
)
async def download_product_group_report(
    request_payload: product_group_models.FetchSpecificProductGroups,
    user_id: UserDependency
):
    await product_group_service.download_product_group_report(request_payload, user_id)
    return common_utils.create_response(
        message=global_constants.REPORT_DOWNLOAD_MESSAGE
    )

@router.get(
    "/product-group",
)
async def get_product_group(product_group_id: int):
    data = await product_group_service.get_product_group(product_group_id)
    return common_utils.create_response(data=data)

@router.post(
    path="/product-group-process",
)
async def product_group_process(
    products_info: product_group_models.ProductGroupProcess,
):
    await product_group_service.check_pg_process(products_info.product_group_id)

    return common_utils.create_response(message="")

@router.post(
    "/get-ungrouped-product-details",
)
async def get_ungrouped_product_details(
    ungrouped_products_filter: product_group_models.UnGroupedProductsRead,
):
    """
    This Method will return the un grouped products info.

    Param: only_meta_data : bool
    if it is set to False, will return the ungrouped products.
    Exp:    [
                {   "product_id": int,
                    "l5_name": str,
                    "l4_name": str,
                    "l3_name": str,
                    "l2_name": str,
                    "l1_name": str,
                    "l0_name": str
                },
                {....}
            ]

    if it is set to True, Will return the numbers
    Exp :   {
                "ungrouped_count": int,
                "ungrouped_percentage": int
            }
    """
    data = await product_group_service.get_ungrouped_products(ungrouped_products_filter)
    return common_utils.create_response(data=data)

@router.get(
    "/product-groups/{product_group_id}/effected-promos"
)
async def get_effected_promos_of_product_group(
    product_group_id: int
):
    data = await product_group_service.get_effected_promos_of_product_group(product_group_id)
    return common_utils.create_response(data=data)

@router.get(
    "/product-groups/{product_group_id}/promos",
)
async def get_product_group_promos(product_group_id: int):
    data = await product_group_service.get_product_group_promos(product_group_id)
    return common_utils.create_response(data=data)

@router.put(
    path="/product-groups-delete",
)
async def delete_product_groups(
    pgs_info: product_group_models.ProductGroupsDelete,
    user_id: UserDependency
):
    await product_group_service.delete_product_groups(
        pgs_info=pgs_info, user_id=user_id
    )
    return common_utils.create_response(message="successfully deleted product groups")

@router.put(
    path="/product-group",
)
async def update_product_group(
    products_info: product_group_models.ProductGroupUpdate,
    user_id: UserDependency,
    background_tasks: BackgroundTasks,
):
    await product_group_service.check_pg_process(products_info.product_group_id)
    await product_group_service.pg_name_unique_check(products_info)
    await product_group_service.update_pg_process(products_info, 1)
    await product_group_service.strategies_promo_process_check(products_info)

    background_tasks.add_task(
        product_group_service.update_product_group,
        products_info,
        user_id,
    )
    return common_utils.create_response(
        message="Product Group edit started, will notify once completed"
    )

@router.get(
    "/product-groups/{product_group_id}/products"
)
async def get_product_details_from_product_group(product_group_id: int):
    data = await product_group_service.get_product_details_from_product_group(product_group_id)
    return common_utils.create_response(data=data)
