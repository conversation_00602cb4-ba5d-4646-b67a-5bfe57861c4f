import json
from typing import Any
from configuration.environment import environment
from enums.Enums import Config<PERSON><PERSON><PERSON>num, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from pricesmart_common.utils import async_execute_query, get_array_format, get_key_value_str, get_str_repr
from logger.logger import logger
from product_group import queries as product_group_queries
from product_group import models as product_group_models
from product_group import constants as product_group_constants
from exceptions.exceptions import ConflictException, UniqueConstraintViolation
from product import models as product_models
from pricesmart_common import constants as common_constants
from product_group import exceptions as product_group_exceptions
from exceptions.exceptions import ConflictException
from promotions import queries as promo_queries
from product_group import constants as product_group_constants
from client_configuration import constants as client_configuration_constants
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from exceptions.exceptions import CommonException
from product_group import utils as product_group_utils

async def insert_product_group(
    product_group: product_group_models.ProductGroupWrite, user_id: int
):
    if not await is_product_group_exists(product_group):
        return None

    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    pg_hierarchy = get_pg_hierarchy(product_group.product_hierarchy, product_hierarchy_config)
    pg_hierarchy_filter = get_pg_hierarchy_filters(pg_hierarchy, product_hierarchy_config)
    for pg_h in pg_hierarchy_filter:
        if "lifecycle_indicator_id" in pg_h:
            pg_hierarchy_filter.remove(pg_h)
    lifecycle_indicator_where = ""
    if product_group.product_hierarchy.get("lifecycle_indicator_ids"):
        lifecycle_indicator_where = "and (tplm.lifecycle_indicator is null or array{arr} && tplm.lifecycle_indicator)"
        lifecycle_indicator_where = lifecycle_indicator_where.format(
            arr=product_group.product_hierarchy.get("lifecycle_indicator_ids")
        )
    if pg_hierarchy:
        products_count, sub_query = await get_pg_hierarchy_sub_query(
            product_group.is_hierarchy_level,
            pg_hierarchy,
            pg_hierarchy_filter,
            lifecycle_indicator_where,
        )
    if product_group.is_hierarchy_level == 1:
        get_product_ids_query = product_group_queries.GET_PRODUCTS_IDS_QUERY.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            hierarchies_list=(common_constants.AND + common_constants.AND.join(pg_hierarchy_filter) if pg_hierarchy_filter else "") ,
            lifecycle_indicator_where=lifecycle_indicator_where,
        )
        query = product_group_queries.INSERT_HIERARCHY_LEVEL_PRODUCT_GROUP.format(
            global_schema=environment.global_schema,
            pg_name=get_str_repr(product_group.product_group_name),
            description=get_str_repr(product_group.product_group_description),
            created_by=user_id,
            pg_grouping_type=product_group.is_hierarchy_level,
            sub_query=sub_query,
            products_count=products_count,
            get_product_ids_query=get_product_ids_query,
        )
    else:
        if not pg_hierarchy:
            select_list = product_group_utils.get_product_hierarchies_select_list(product_hierarchy_config)
            union_list = product_group_utils.get_product_hierarchies_union_list(product_hierarchy_config)
            sub_query = product_group_queries.GET_PRODUCTS_HIERARCHIES.format(
                promo_schema=environment.promo_schema,
                global_schema=environment.global_schema,
                product_ids=product_group.product_ids,
                select_list=select_list,
                union_list=union_list
            )

        query = product_group_queries.INSERT_PRODUCT_LEVEL_PRODUCT_GROUP.format(
            global_schema=environment.global_schema,
            pg_name=get_str_repr(product_group.product_group_name),
            description=get_str_repr(product_group.product_group_description),
            created_by=user_id,
            product_ids=json.dumps(product_group.product_ids),
            pg_grouping_type=product_group.is_hierarchy_level,
            products_count=len(product_group.product_ids),
            sub_query=sub_query,
        )
    try:
        await async_execute_query(query)
    except Exception as err:
        detail = "Duplicate products present in Product Group."
        raise ConflictException(
            message=detail
        ) from err

async def is_product_group_exists(product_group: product_group_models.ProductGroupWrite):
    is_product_group_exists_query = (
        product_group_queries.GET_PRODUCT_GROUP_IF_NOT_DELETED.format(
            global_schema=environment.global_schema,
            product_group_name=get_str_repr(product_group.product_group_name),
        )
    )
    is_product_group_exists_data = await async_execute_query(
        is_product_group_exists_query
    )
    if is_product_group_exists_data:
        raise UniqueConstraintViolation(
            product_group_constants.PG_UNIQUE_CONSTRAINT_VIOLATION_KEY, product_group.product_group_name
        )
    return True


def get_pg_hierarchy(
    product_hierarchy: product_models.ProductHierarchy | None,
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType]
):
    if not product_hierarchy:
        return {}
    
    # Add PG Hierarchy Mapping.
    pg_hierarchy: dict[int, list[int] | None] = {
        value["id"]: product_hierarchy[key]
        for key, value in product_hierarchy_config.items()
        if (value["id"] is not None) and (key in product_hierarchy)
    }

    if not any(pg_hierarchy.values()):
        return {}

    return pg_hierarchy

def get_pg_hierarchy_filters(pg_hierarchy, product_hierarchy_config):
    pg_hierarchy_filter = []
    for key, val in product_hierarchy_config.items():
        pg_hierarchy_key = val["id"]
        pg_hierarchy_value = pg_hierarchy[pg_hierarchy_key] if (pg_hierarchy_key is not None and pg_hierarchy_key in pg_hierarchy) else None
        # Skip the iteration if the value is empty.
        if not pg_hierarchy_value:
            continue
        source_table = "pm."

        # Generate Filters Dict 
        pg_hierarchy_filter.append(
            get_key_value_str(source_table + val["id_column"], pg_hierarchy_value)
        )

    return pg_hierarchy_filter

async def get_pg_hierarchy_sub_query(
    is_hierarchy_level, pg_hierarchy, pg_hierarchy_filter, lifecycle_indicator_where
):
    products_count = 0
    common_sub_query = ""
    max_hierarchy_level = 0
    actual_hierarchy = set()
    for key, val in pg_hierarchy.items():
        # Skip the iteration if the value is empty.
        if not val:
            continue

        # Generate Common Sub Query.
        if not is_hierarchy_level or key < 0 or val:
            if common_sub_query:
                common_sub_query += f" union all select {key} as hierarchy_level, unnest(array{val}) as hierarchy_value, 0 as is_temporary "
            else:
                common_sub_query = f" select {key} as hierarchy_level, unnest(array{val}) as hierarchy_value, 0 as is_temporary "
            actual_hierarchy.add(key)

        # Find Highest hierarchy_level.
        max_hierarchy_level = max(max_hierarchy_level, key)

    if is_hierarchy_level:
        get_products_count_query = product_group_queries.GET_PRODUCTS_COUNT_QUERY.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            hierarchies_list=(common_constants.AND + common_constants.AND.join(pg_hierarchy_filter) if pg_hierarchy_filter else ""),
            lifecycle_indicator_where=lifecycle_indicator_where,
        )
        products_count = await async_execute_query(get_products_count_query)
        products_count = products_count[0]["count"]
        common_sub_query = get_sub_query(
            products_count,
            pg_hierarchy,
            pg_hierarchy_filter,
            common_sub_query,
            actual_hierarchy,
            max_hierarchy_level,
            lifecycle_indicator_where,
        )
    return products_count, common_sub_query

def get_sub_query(
    products_count,
    pg_hierarchy,
    pg_hierarchy_filter,
    common_sub_query,
    actual_hierarchy,
    max_hierarchy_level,
    lifecycle_indicator_where,
):
    if not products_count and len(actual_hierarchy) == 1 and -2 in actual_hierarchy:
        common_sub_query += (
            common_constants.UNION_ALL
            + product_group_queries.GET_HIERARCHY_WITH_ALL_DIVISION_IDS.format(
                promo_schema=environment.promo_schema
            )
        )
        return common_sub_query
    
    if not products_count:
        return common_sub_query

    agg_selection, union_selection = get_select_statements(
        products_count, pg_hierarchy, actual_hierarchy, max_hierarchy_level
    )

    if not pg_hierarchy.get(-1) and pg_hierarchy.get(-2):
        agg_selection += f"{',' if agg_selection else ''} array_agg(distinct pm.brand_cid) as brand_ids "
        union_selection += f"""
            {common_constants.UNION_ALL if union_selection else ''} 
            select -1 as hierarchy_level, unnest(brand_ids) as hierarchy_value, 1 as is_temporary 
            from pg_hierarchy_agg_data 
        """

    if agg_selection and union_selection:
        temp_sub_query = product_group_queries.GET_PG_HIERARCHY_AGG_DATA.format(
            agg_selection=agg_selection,
            union_selection=union_selection,
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            hierarchies_list=(common_constants.AND + common_constants.AND.join(pg_hierarchy_filter) if pg_hierarchy_filter else ""),
            lifecycle_indicator_where=lifecycle_indicator_where,
        )
        common_sub_query = temp_sub_query + common_constants.UNION_ALL + common_sub_query
    return common_sub_query

def get_select_statements(
    products_count, pg_hierarchy, actual_hierarchy, max_hierarchy_level
):
    agg_selection = ""
    union_selection = ""
    query = " select {h_level} as hierarchy_level, unnest(l{h_level}_ids) as hierarchy_value, 1 as is_temporary from pg_hierarchy_agg_data  "
    if products_count:
        # If atleast one product is present, so consider we have till l4_ids.
        if pg_hierarchy.get(-1) or pg_hierarchy.get(-2):
            max_hierarchy_level = 4
        for h_level in range(max_hierarchy_level + 1):
            if h_level in actual_hierarchy:
                continue
            if agg_selection:
                agg_selection = (
                    agg_selection
                    + f" , array_agg(distinct pm.l{h_level}_cid) as l{h_level}_ids"
                )
                union_selection = (
                    union_selection + common_constants.UNION_ALL + query.format(h_level=h_level)
                )
            else:
                agg_selection = (
                    f"array_agg(distinct pm.l{h_level}_cid) as l{h_level}_ids"
                )
                union_selection = query.format(h_level=h_level)
    return agg_selection, union_selection


async def get_product_groups(
    product_hierarchy_filter: product_group_models.FetchSpecificProductGroups
):
    query = product_group_queries.GET_PRODUCT_GROUPS.format(
        product_hierarchies=get_str_repr(product_hierarchy_filter.product_hierarchies),
        pg_grouping_type=get_str_repr(product_hierarchy_filter.pg_grouping_type),
        client_schema=environment.promo_schema,
        restriction_type = get_str_repr(product_hierarchy_filter.with_exclusions),
        event_id= get_str_repr(product_hierarchy_filter.event_id)
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_product_group(product_group_id):
    
    client_product_id = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.CLIENT_PRODUCT_ID_KEY)
    get_pg_grouping_type = product_group_queries.GET_PG_GROUPING_TYPE.format(
        global_schema=environment.global_schema,
        promo_schema=environment.promo_schema,
        product_group_id=product_group_id
    )
    pg_grouping_type = await async_execute_query(get_pg_grouping_type)
    pg_grouping_type = pg_grouping_type[0]['pg_grouping_type']

    get_pg_products_query = product_group_queries.GET_PG_PRODUCTS.format(
        global_schema=environment.global_schema,
        promo_schema=environment.promo_schema,
        product_group_id=product_group_id,
        client_product_id=client_product_id
    )

    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    select_pg_hierarchy_agg_data_condition = product_group_utils.get_select_pg_hierarchy_agg_data_condition(product_hierarchy_config)
    select_hierarchy_select_query_condition = product_group_utils.get_hierarchy_select_query_condition(product_hierarchy_config)
    pg_hierarchy_agg_data_group_by_condition = product_group_utils.get_pg_hierarchy_agg_data_group_by_condition(product_hierarchy_config)
    query = product_group_queries.GET_PRODUCT_GROUP_DATA_QUERY.format(
        promo_schema=environment.promo_schema,
        product_group_id=product_group_id,
        global_schema=environment.global_schema,
        get_pg_products_query = (get_pg_products_query if pg_grouping_type == 0  else "null"),
        select_pg_hierarchy_agg_data_condition=select_pg_hierarchy_agg_data_condition,
        select_hierarchy_select_query_condition=select_hierarchy_select_query_condition,
        pg_hierarchy_agg_data_group_by_condition=pg_hierarchy_agg_data_group_by_condition
    )
    data = await async_execute_query(query)
    if not data:
        raise product_group_exceptions.ProductGroupIdNotFound
    return data[0]

async def check_pg_process(product_group_id: int):
    query = product_group_queries.GET_PG_PROCESS.format(
        global_schema=environment.global_schema, pg_id=product_group_id
    )
    pg_process = await async_execute_query(query)
    if pg_process:
        detail = "Product group is currently being edited by another user"
        raise ConflictException(detail)
    return True

async def get_ungrouped_products_count_and_percentage(
    product_hierarchies: dict[str, Any],
):
    hierarchies_list = []
    for key, value in product_hierarchies.items():
        if value:
            hierarchies_list.append(get_key_value_str(key, value))

    query = product_group_queries.GET_UNGROUPED_PRODUCTS_COUNT_AND_PERCENTAGE.format(
        hierarchies_list=common_constants.AND.join(hierarchies_list),
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        simulation_schema=environment.simulation_schema
    )
    ungrouped_products_count_and_percentage = await async_execute_query(query)
    return ungrouped_products_count_and_percentage[0]

async def get_ungrouped_products(
    product_hierarchies: dict[str, list[int]],
):
    product_hierarchy_config: dict[str, HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    hierarchies_list = []
    for key, value in product_hierarchies.items():
        if value:
            hierarchies_list.append(get_key_value_str(key, value))
    
    hierarchy_select = ",".join(
        f"pm.{value['value_column']} as l{value['id']}_name"
        for value in product_hierarchy_config.values()
        if value.get("is_hierarchy") is True
    )

    ungrouped_products_query = product_group_queries.GET_UNGROUPED_PRODUCTS.format(
        hierarchies_list=common_constants.AND.join(hierarchies_list),
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        simulation_schema=environment.simulation_schema,
        hierarchy_select=hierarchy_select if hierarchy_select else "null"
    )
    logger.info(ungrouped_products_query)
    ungrouped_products = await async_execute_query(ungrouped_products_query)
    return ungrouped_products

async def get_effected_promos_of_product_group(
    product_group_id: int
):
    query = product_group_queries.GET_EFFECTED_PROMOS_OF_PRODUCT_GROUP.format(
        product_group_id=product_group_id,
        client_timezone=get_str_repr(common_constants.CLIENT_TIMEZONE)
    )
    return await async_execute_query(query)

async def delete_product_groups(
    pgs_info: product_group_models.ProductGroupsDelete, user_id: int
):
    product_group_id_str = ",".join(map(str, pgs_info.product_group_id))
    pg_delete_query = product_group_queries.DELETE_PRODUCT_GROUPS.format(
        global_schema=environment.global_schema,
        product_group_id_str=product_group_id_str,
        user_id=user_id
    )
    await async_execute_query(pg_delete_query)

async def update_pg_process(
    products_info: product_group_models.ProductGroupUpdate, set_to: int
):
    query = product_group_queries.UPDATE_PG_PROCESS.format(
        global_schema=environment.global_schema,
        set_to=set_to,
        pg_id=products_info.product_group_id,
    )
    await async_execute_query(query)


async def strategies_promo_process_check(
    products_info: product_group_models.ProductGroupUpdate,
):
    selected_promos = []
    if products_info.selected_plans:
        selected_promos = [
            i["plan_id"] for i in products_info.selected_plans if i["plan"] == "Offer"
        ]

    query = promo_queries.PROMOS_PROCESS_CHECK.format(
        selected_promos=selected_promos
    )
    process_check = await async_execute_query(query)
    if process_check:
        detail = f"Selected Strategy/{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.capitalize()} are currently being edited by another user"
        raise ConflictException(detail)
    return True


async def pg_name_unique_check(products_info: product_group_models.ProductGroupUpdate):
    is_pg_name_unique_query = product_group_queries.PG_NAME_UNIQUE_QUERY.format(
        global_schema=environment.global_schema,
        product_group_id=products_info.product_group_id,
        product_group_name=get_str_repr(products_info.product_group_name),
    )
    print("------is_pg_name_unique_query------", is_pg_name_unique_query)
    is_pg_name_exists_data = await async_execute_query(is_pg_name_unique_query)
    if is_pg_name_exists_data:
        raise UniqueConstraintViolation(
            product_group_constants.PG_UNIQUE_CONSTRAINT_VIOLATION_KEY, products_info.product_group_name
        )
    return True

async def update_product_group(
    products_info: product_group_models.ProductGroupUpdate,
    user_id: int,
):
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    pg_hierarchy = get_pg_hierarchy(products_info.product_hierarchy, product_hierarchy_config)
    pg_hierarchy_filter = get_pg_hierarchy_filters(pg_hierarchy, product_hierarchy_config)
    for pg_h in pg_hierarchy_filter:
        if "lifecycle_indicator_id" in pg_h:
            pg_hierarchy_filter.remove(pg_h)

    lifecycle_indicator_where = ""
    if products_info.product_hierarchy.get("lifecycle_indicator_ids"):
        lifecycle_indicator_where = product_group_constants.LIFECYCLE_INDICATOR_WHERE
        lifecycle_indicator_where = lifecycle_indicator_where.format(
            arr=products_info.product_hierarchy.get("lifecycle_indicator_ids")
        )
    pg_hierarchy_selection = None
    get_product_ids_query = None
    if pg_hierarchy:
        products_count, sub_query = await get_pg_hierarchy_sub_query(
            products_info.is_hierarchy_level,
            pg_hierarchy,
            pg_hierarchy_filter,
            lifecycle_indicator_where,
        )
        pg_hierarchy_sorted = dict(sorted(pg_hierarchy.items()))
        pg_hierarchy_selection = json.dumps(
            [
                {"hierarchy_level": k, "hierarchy_value": v}
                for k, v in pg_hierarchy_sorted.items()
                if pg_hierarchy_sorted[k]
            ]
        )
    if products_info.is_hierarchy_level == 1:
        get_product_ids_query = product_group_queries.GET_PRODUCTS_IDS_QUERY.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            hierarchies_list=(common_constants.AND + common_constants.AND.join(pg_hierarchy_filter) if pg_hierarchy_filter else ""),
            lifecycle_indicator_where=lifecycle_indicator_where,
        )
    else:
        if not pg_hierarchy:
            select_list = product_group_utils.get_product_hierarchies_select_list(product_hierarchy_config)
            union_list = product_group_utils.get_product_hierarchies_union_list(product_hierarchy_config)
            sub_query = product_group_queries.GET_PRODUCTS_HIERARCHIES.format(
                promo_schema=environment.promo_schema,
                global_schema=environment.global_schema,
                product_ids=products_info.product_ids,
                select_list=select_list,
                union_list=union_list
            )

    products_count = len(products_info.product_ids) or products_count

    selected_strategies = [
        i["plan_id"]
        for i in (products_info.selected_plans or [])
        if i["plan"] == "Markdown strategy"
    ]
    selected_promos = [
        i["plan_id"]
        for i in (products_info.selected_plans or [])
        if i["plan"] == "Offer"
    ]

    query = product_group_queries.UPDATE_PRODUCT_GROUP_QUERY.format(
        promo_schema=environment.promo_schema,
        edit_pg_id=products_info.product_group_id,
        product_group_name=get_str_repr(products_info.product_group_name),
        product_group_description=get_str_repr(products_info.product_group_description),
        user_id=user_id,
        pg_grouping_type=products_info.is_hierarchy_level,
        pg_sub_query=get_str_repr(sub_query),
        get_product_ids_query=get_str_repr(get_product_ids_query),
        pg_products_count=products_count,
        pg_hierarchy_selection=get_str_repr(pg_hierarchy_selection),
        product_ids=products_info.product_ids,
        selected_strategies=selected_strategies,
        selected_promos=selected_promos,
        client_timezone=get_str_repr(common_constants.CLIENT_TIMEZONE),
    )
    print("-----query----", query)
    updated_pg_res = await async_execute_query(query, timelimit=None)
    print("-----pg_updated_res----", updated_pg_res)
    updated_pg_id = updated_pg_res[0]["fn_update_product_group"]
    return updated_pg_id

async def get_product_group_promos(product_group_id: int):
    query = product_group_queries.GET_PRODUCT_GROUP_PROMOS.format(
        promo_schema=environment.promo_schema,
        product_group_id=product_group_id,
        global_schema=environment.global_schema,
    )
    data = await async_execute_query(query)
    return data

async def get_product_details_from_product_group(product_group_id: int):
    client_product_id_key = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.CLIENT_PRODUCT_ID_KEY)
    query = product_group_queries.GET_PRODUCT_DETAILS_OF_PRODUCT_GROUP.format(
        promo_schema=environment.promo_schema,
        product_group_id=product_group_id,
        global_schema=environment.global_schema,
        client_product_id_key=client_product_id_key
    )
    data = await async_execute_query(query)
    return data