from typing import Optional
from pydantic import BaseModel, ValidationInfo, field_validator,Field
from product import models as product_models
from exceptions.exceptions import CommonException


class FetchSpecificProductGroups(BaseModel):
    product_hierarchies: dict[str, list[int]] = Field(default_factory=dict)
    pg_grouping_type: Optional[int] = -1
    event_id: Optional[int] = None
    with_exclusions: Optional[bool] = False


class ProductGroupWrite(BaseModel):
    product_group_name: str
    product_group_description: str
    is_hierarchy_level: int = 0 | 1
    product_ids: list[int] = []
    product_hierarchy: dict[str,list[int]] = Field(default_factory=dict)

    @field_validator("product_group_name")
    @classmethod
    def is_pg_name_empty(cls, value):
        if not value.strip():
            raise CommonException("Product Group Name cannot be empty")
        return value

    @field_validator("product_group_description")
    @classmethod
    def is_pg_desc_empty(cls, value):
        if not value.strip():
            raise CommonException("Product Group Description cannot be empty")
        return value

    @field_validator("product_ids")
    @classmethod
    def is_pg_products_empty(cls, product_ids, values:ValidationInfo):
        print(product_ids,"product_ids")
        print(values,"values")
        if values.data["is_hierarchy_level"] not in {0, 1}:
            raise CommonException("Hierarchy level must be 0 or 1")

        if values.data["is_hierarchy_level"] == 0 and not product_ids:
            raise CommonException(
                "At least one product should be present to create a product level product Group"
            )
        return list(set(product_ids))

    @field_validator("product_hierarchy")
    @classmethod
    def is_pg_hierarchy_empty(cls, product_hierarchy, values:ValidationInfo):
        if values.data["is_hierarchy_level"] not in {0, 1}:
            raise CommonException("Hierarchy level must be 0 or 1")
        is_hierarchy_not_present = product_hierarchy and all(
            not value
            for value in product_hierarchy.values()
        )
        if values.data["is_hierarchy_level"] == 1 and is_hierarchy_not_present:
            raise CommonException(
                "At least one hierarchy level should be present to create a hierarchy level product group"
            )
        return product_hierarchy

class ProductGroupProcess(BaseModel):
    product_group_id: int

class UnGroupedProductsRead(BaseModel):
    product_hierarchies: dict[str, list[int]] = Field(default_factory=dict)
    only_meta_data: bool = False

class ProductGroupsDelete(BaseModel):
    product_group_id: list[int]

class ProductGroupUpdate(ProductGroupWrite):
    product_group_id: int
    product_group_name: str
    product_group_description: str
    is_hierarchy_level: int = 0 | 1
    product_ids: list[int] = []
    product_hierarchy: dict[str,list[int]] = Field(default_factory=dict)
    selected_plans: Optional[list] = []
    guid: str
    application: Optional[str] = 'promo'