from filters.types import HierarchyFiltersInfoType


def get_product_hierarchies_select_list(product_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = []
    for hierarchy_key, cfg in product_hierarchy_config.items():
        if cfg.get('is_linked_to_product_group'):
            table_alias = "pm"
            id_column = cfg["id_column"]
            select_list.append(
                f"array_agg(DISTINCT {table_alias}.{id_column}) AS {hierarchy_key}"
            )
    return ",\n    ".join(select_list)


def get_product_hierarchies_union_list(product_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    union_list = []
    for hierarchy_key, cfg in product_hierarchy_config.items():
        if cfg.get('id') is not None and cfg.get('is_linked_to_product_group'):
            level = cfg['id']
            union_list.append(
                f"select {level} as hierarchy_level, unnest({hierarchy_key}) as hierarchy_value from product_hierarchy_data"
            )
    return "\nunion all\n".join(union_list)


def get_select_pg_hierarchy_agg_data_condition(product_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = []
    for hierarchy_key, cfg in product_hierarchy_config.items():
        if not cfg.get('is_linked_to_product_group'):
            continue
        id = cfg["id"]
        select_list.append(
            f"(select jsonb_agg(json_build_object('value', phd.hierarchy_value, 'label', phd.hierarchy_name)) from pg_hierarchy_data phd where phd.hierarchy_level = {id}) as {hierarchy_key}"
        )
    return ",\n    ".join(select_list)

def get_hierarchy_select_query_condition(product_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = []
    for hierarchy_key, cfg in product_hierarchy_config.items():
        if not cfg.get('is_linked_to_product_group'):
            continue

        select_list.append(
            f"'{hierarchy_key}', phad.{hierarchy_key}"
        )
    return ",\n    ".join(select_list)

def get_pg_hierarchy_agg_data_group_by_condition(product_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = []
    for hierarchy_key, cfg in product_hierarchy_config.items():
        if not cfg.get('is_linked_to_product_group'):
            continue

        select_list.append(
            f"phad.{hierarchy_key}"
        )
    return ",\n    ".join(select_list)