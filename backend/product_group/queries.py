
GET_PRODUCT_GROUPS = """
    select 
        * 
    from 
        {client_schema}.fn_fetch_product_group_landing_page_data(
            {product_hierarchies}::jsonb,
            {pg_grouping_type}::integer,
            {event_id},
            {restriction_type}
        )
"""

GET_PRODUCT_GROUP_IF_NOT_DELETED = """
    select 
        pg_name,
        is_deleted
    from 
        {global_schema}.tb_product_group
    where 
        pg_name ilike({product_group_name})
        and is_deleted = 0
"""

GET_PRODUCTS_COUNT_QUERY = """    
    select
        count(pm.product_id)
    from
        {promo_schema}.product_master pm
    left join
        (select product_id,array_agg(lifecycle_indicator_id) as lifecycle_indicator from {global_schema}.tb_parent_lifecycle_mapping group by product_id) tplm on pm.product_id = tplm.product_id
    where
        is_active = 1
        {hierarchies_list}
        {lifecycle_indicator_where}
"""

GET_PRODUCTS_IDS_QUERY = """    
    select
        pm.product_id
    from
        {promo_schema}.product_master pm
    left join
        (select product_id,array_agg(lifecycle_indicator_id) as lifecycle_indicator from {global_schema}.tb_parent_lifecycle_mapping group by product_id) tplm on pm.product_id = tplm.product_id
    where
        is_active = 1
        {hierarchies_list}
        {lifecycle_indicator_where}
"""

INSERT_HIERARCHY_LEVEL_PRODUCT_GROUP = """
    DO $$
    DECLARE
        _pg_id integer;
        partition_query text; 
    BEGIN
        -- Insert basic Product Group Info.
        insert into {global_schema}.tb_product_group (pg_name, description, created_by, pg_grouping_type, products_count)
        values ({pg_name}, {description}, {created_by}, {pg_grouping_type}, {products_count}) returning pg_id into _pg_id;

        -- Create Partition if not exists.
        call global.pc_create_partition_for_pgs_or_sgs('tb_pg_product', _pg_id);
        
        -- Grant Partition Table Permissions.
        -- execute 'GRANT ALL ON table {global_schema}.tb_pg_product_' || _pg_id::text || ' TO "mtp-dev"';
    
        -- Insert PG hierarchy and products.
        with hierarchy_insert_cte as(
            insert 
                into {global_schema}.tb_pg_hierarchy (pg_id, hierarchy_level, hierarchy_value, is_temporary)
            select
                _pg_id as pg_id,
                dd.hierarchy_level,
                dd.hierarchy_value,
                dd.is_temporary
            from
                (
                    {sub_query}
                )dd
        )
        insert
            into {global_schema}.tb_pg_product (pg_id, product_id)
        select
            _pg_id as pg_id,
            dd1.product_id
        from
            (
                {get_product_ids_query}
            )dd1;
        perform global.fn_refresh_materialized_view('global', 'mvw_pg_hierarchy_agg_data');
        perform price_markdown.fn_update_product_group_products_count(_pg_id);
    END;
    $$;
"""

GET_PRODUCTS_HIERARCHIES = """
    with product_hierarchy_data as (
        select 
            {select_list}
        from 
            {promo_schema}.product_master pm 
        where 
            pm.product_id = any(array{product_ids})
    )
    select 
        t1.hierarchy_level,
        t1.hierarchy_value,
        0 as is_temporary
    from
        (
            {union_list}
        )t1
    where 
        t1.hierarchy_value is not null
"""

INSERT_PRODUCT_LEVEL_PRODUCT_GROUP = """
    DO $$
    DECLARE
        _pg_id integer;
        partition_query text; 
    BEGIN
        -- Insert basic Product Group Info.
        insert into {global_schema}.tb_product_group (pg_name, description, created_by, pg_grouping_type, products_count)
        values ({pg_name}, {description}, {created_by}, {pg_grouping_type}, {products_count}) returning pg_id into _pg_id;

        -- Create Partition if not exists.
        call global.pc_create_partition_for_pgs_or_sgs('tb_pg_product', _pg_id);
        
        -- Grant Partition Table Permissions.
        -- execute 'GRANT ALL ON table {global_schema}.tb_pg_product_' || _pg_id::text || ' TO "mtp-dev"';

        -- Insert PG Products.
        with hierarchy_insert_cte as(
            insert 
                into {global_schema}.tb_pg_hierarchy (pg_id, hierarchy_level, hierarchy_value, is_temporary)
            select
                _pg_id as pg_id,
                dd.hierarchy_level,
                dd.hierarchy_value,
                dd.is_temporary
            from
                (
                    {sub_query}
                )dd
        )
        insert
            into {global_schema}.tb_pg_product (pg_id, product_id)
        select
            _pg_id as pg_id,
            cast(dd.product_id as bigint)
        from
            (
                select json_array_elements_text('{product_ids}') product_id
            )dd;
        perform global.fn_refresh_materialized_view('global', 'mvw_pg_hierarchy_agg_data');
        perform price_markdown.fn_update_product_group_products_count(_pg_id);
    END;
    $$;
"""

GET_PG_HIERARCHY_AGG_DATA = """
	with pg_hierarchy_agg_data as(
        select
            {agg_selection}
        from
            {promo_schema}.product_master pm
        left join
            (select product_id,array_agg(lifecycle_indicator_id) as lifecycle_indicator from {global_schema}.tb_parent_lifecycle_mapping group by product_id) tplm on pm.product_id = tplm.product_id
        where
            is_active = 1
            {hierarchies_list}
            {lifecycle_indicator_where}
    )
    select 
        dd.hierarchy_level,
        dd.hierarchy_value,
        dd.is_temporary
    from 
        (
            {union_selection}
        )dd
"""

GET_HIERARCHY_WITH_ALL_DIVISION_IDS = """
    select 0 as hierarchy_level, l0_cid as hierarchy_value, 1 as is_temporary from {promo_schema}.product_master pm where pm.is_active = 1 group by l0_cid
"""


GET_PG_GROUPING_TYPE = """
    select 
        pg_grouping_type 
    from 
        {global_schema}.tb_product_group tpg
    where 
        tpg.pg_id = {product_group_id}
"""

GET_PG_PRODUCTS = """
    with pg_products as (
        select 
            jsonb_agg(
                        jsonb_build_object(
                            'client_product_id', pm.{client_product_id},
                            'product_id', pm.product_id,
                            'product_name', pm.product_name,
                            'msrp', pm.msrp,
                            'current_price', pm.current_price,
                            'inventory', (case when tlia.total_inventory is null then 0 else tlia.total_inventory end),
                            'cost', pm.cost,
                            'launch_price', pm.launch_price
                        )
                    ) FILTER (WHERE pm.product_id IS NOT NULL)
        from 
            {global_schema}.tb_pg_product tpp 
        left join
            {promo_schema}.product_master pm on tpp.product_id = pm.product_id
        left join 
            {global_schema}.tb_latest_inventory_agg tlia on tpp.product_id = tlia.product_id
        where 
            tpp.pg_id = {product_group_id}
    )
    select * from pg_products
"""

GET_PRODUCT_GROUP_DATA_QUERY = """
    with pg_basic_data_cte as (        
        with pg_data_cte as (
            SELECT 
                tpg.pg_id as product_group_id,
                tpg.pg_name as product_group_name,
                tpg.description as product_group_description,
                tpg.pg_grouping_type
            FROM 
                {global_schema}.tb_product_group tpg
            where 
                tpg.pg_id = {product_group_id}
        ),
        pg_hierarchy_data as( 
            select
                pdc.product_group_id,
                tph.hierarchy_level,
                tph.hierarchy_value,
                thcm.hierarchy_name
            from
                pg_data_cte pdc
            inner join
                {global_schema}.tb_pg_hierarchy tph on pdc.product_group_id = tph.pg_id
            left join
                {global_schema}.tb_hierarchy_cid_mapping thcm on tph.hierarchy_level = thcm.hierarchy_level and tph.hierarchy_value = thcm.hierarchy_value
            where 
                tph.is_temporary = 0
                and tph.is_deleted = 0
            group by
                pdc.product_group_id,
                tph.hierarchy_level,
                tph.hierarchy_value,
                thcm.hierarchy_name
        ),
        pg_hierarchy_agg_data as( 
            select
                {select_pg_hierarchy_agg_data_condition}
        )
        select
            pdc.product_group_id,
            pdc.product_group_name,
            pdc.product_group_description,
            pdc.pg_grouping_type as is_hierarchy_level,
            jsonb_build_object(
                {select_hierarchy_select_query_condition}
            ) as hierarchy  
        from 
            pg_data_cte pdc
        cross join
            pg_hierarchy_agg_data phad
        group by
            pdc.product_group_id,
            pdc.product_group_name,
            pdc.product_group_description,
            pdc.pg_grouping_type,
            {pg_hierarchy_agg_data_group_by_condition}
    )
    select 
        pbdc.*,
        ({get_pg_products_query}) as products_details
    from 
        pg_basic_data_cte pbdc
"""

GET_PG_PROCESS = """
    select tpg.pg_id from {global_schema}.tb_product_group tpg
    where tpg.pg_id = {pg_id} and tpg.is_under_processing = 1
"""

GET_UNGROUPED_PRODUCTS_COUNT_AND_PERCENTAGE = """
    select
        round(cast(ungrouped_products.ungrouped_products_count / nullif(cast(total_products.total_product_count as FLOAT),0)* 100 as DECIMAL),2) as ungrouped_percentage,
        ungrouped_products.ungrouped_products_count as ungrouped_count
    from
        (	
            select
                COUNT(*) as total_product_count
            from
                {promo_schema}.product_master pm
            where
                {hierarchies_list}
                and pm.is_active = 1
        ) total_products
        cross join 
        (
            select
                COUNT(*) as ungrouped_products_count
            from
                {promo_schema}.product_master pm
            where
                pm.product_id not in (
                    select
                        distinct tpp.product_id
                    from
                        {global_schema}.tb_pg_product tpp
                    join {global_schema}.tb_product_group tpg on
                        tpg.pg_id = tpp.pg_id
                    where
                        tpg.is_deleted = 0
                )
                and {hierarchies_list}
                and pm.is_active = 1
        ) ungrouped_products
"""

GET_UNGROUPED_PRODUCTS = """
    select
        pm.product_id,
        {hierarchy_select}
    from
        {promo_schema}.product_master pm
    where
        pm.product_id not in (
            select
                distinct tpp.product_id
            from
                {global_schema}.tb_pg_product tpp
            join {global_schema}.tb_product_group tpg on
                tpg.pg_id = tpp.pg_id
            where
                tpg.is_deleted = 0
        )
        and {hierarchies_list}
        and pm.is_active = 1
"""

GET_EFFECTED_PROMOS_OF_PRODUCT_GROUP = """
    select 
        'Offer' as plan,
        pm.name as plan_name,
        pm.promo_id as plan_id,
        psc.status_name::text as plan_status,
        pm.start_date,
        pm.end_date,
        pr.discount_level as plan_discount_level,
        case when pm.is_under_processing = 0 then 0 else 1 end as under_process,
        um.user_name
    from price_promo.promo_master as pm
    left join price_promo.ps_rules as pr on pr.promo_id = pm.promo_id
    join 
        (select promo_id from price_promo.included_promo_product_groups ippg
        where ippg.product_group_id = {product_group_id} 
        union
        select promo_id from price_promo.excluded_product_groups epg
        where  epg.pg_id = {product_group_id}
        ) as tppg on tppg.promo_id = pm.promo_id
    join global.user_master um on um.user_code = pm.created_by
    inner join price_promo.promo_status_config psc on pm.status = psc.status_id
    where 
        pm.start_date > date(timezone({client_timezone}, now()))
        and pm.status not in (-1, 6) 
"""

DELETE_PRODUCT_GROUPS = """
    update {global_schema}.tb_product_group 
        set is_deleted = 1, updated_at = now(), updated_by = {user_id}
    where pg_id in ({product_group_id_str})
"""

UPDATE_PG_PROCESS = """
    update {global_schema}.tb_product_group set is_under_processing = {set_to}
    where pg_id = {pg_id}
"""

PG_NAME_UNIQUE_QUERY = """
    select 
        pg_name,
        is_deleted
    from 
        {global_schema}.tb_product_group
    where 
        pg_name ilike({product_group_name})
        and is_deleted = 0
        and pg_id != {product_group_id}
"""

UPDATE_PRODUCT_GROUP_QUERY = """
    select * 
    from 
        {promo_schema}.fn_update_product_group(
            {edit_pg_id}::integer,
            {product_group_name}::text,
            {product_group_description}::text,
            {user_id}::integer,
            {pg_grouping_type}::integer,
            {pg_sub_query}::text,
            {get_product_ids_query}::text,
            {pg_products_count}::integer,
            {pg_hierarchy_selection}::jsonb,
            array{product_ids}::integer[], 
            array{selected_strategies}::integer[], 
            array{selected_promos}::integer[],
            {client_timezone}::text
        )
"""

GET_PRODUCT_GROUP_PROMOS = """
    select 
        pm.name as promo_name,
        pm.start_date,
        pm.end_date,
        psc.status_name as promo_status,
        um.user_name as created_by,
        pm.promo_id
    from
        (select promo_id from {promo_schema}.included_promo_product_groups where product_group_id = {product_group_id} 
        union
        select promo_id from {promo_schema}.excluded_product_groups epg where pg_id = {product_group_id}) tpsg
    inner join
        {promo_schema}.promo_master pm using(promo_id)
    inner join 
        {promo_schema}.promo_status_config psc on pm.status = psc.status_id
    left join 
        {global_schema}.user_master um on pm.created_by = um.user_code
    where 
        pm.status not in (6) 
"""

PRODUCT_GROUP_DOWNLOAD_QUERY = """
    select 
        dd.product_group_name as "PRODUCT GROUP NAME",
        dd.product_group_description as "DESCRIPTION",
        dd.products_count as "PRODUCTS",
        dd.product_group_type as "PRODUCT GROUP TYPE",
        array_to_string(dd.l0_name, ', ') as "{l0_hierarchy_label}",
        array_to_string(dd.l1_name, ', ') as "{l1_hierarchy_label}", 
        array_to_string(dd.l2_name, ', ') as "{l2_hierarchy_label}",
        dd.created_by_user as "CREATED BY",
        to_char(timezone({client_timezone}, dd.created_at),'YYYY-MM-DD HH24:MI:SS') as "CREATED ON",
        dd.modified_by_user as "MODIFIED BY",
        to_char(timezone({client_timezone}, dd.modified_at),'YYYY-MM-DD HH24:MI:SS') as "MODIFIED ON",
        dd.promos_count as "PROMOS"
    from 
        price_promo.fn_fetch_product_group_landing_page_data(
            {product_hierarchies}::jsonb,
            -1::integer
        ) dd
"""

GET_PRODUCT_DETAILS_OF_PRODUCT_GROUP = """
    select
        pm.* ,
        pm.{client_product_id_key} as client_product_id,
        tlia.total_inventory
    from
        {global_schema}.tb_pg_product as tpp
        left join {promo_schema}.product_master as pm on tpp.product_id = pm.product_id
        left join {global_schema}.tb_latest_inventory_agg as tlia on pm.product_id = tlia.product_id
        where tpp.pg_id = {product_group_id}
    """    