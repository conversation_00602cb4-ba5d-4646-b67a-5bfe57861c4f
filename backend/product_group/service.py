from datetime import datetime
from enums.Enums import Config<PERSON>eyEnum, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from product_group import models as product_group_models
from product_group import data as product_group_data_module
from product_group import queries as product_group_queries
from logger.logger import logger
import asyncio
from pricesmart_common import constants as global_constants
from pricesmart_common.utils import get_array_format,get_str_repr
from server_sent_events.utils import insert_notification
from promotions import utils as promo_utils


async def get_product_groups(
    product_hierarchy_filter: product_group_models.FetchSpecificProductGroups
):
    return await product_group_data_module.get_product_groups(
        product_hierarchy_filter
    )

async def insert_product_group(
    product_group: product_group_models.ProductGroupWrite, user_id: int
):
    await product_group_data_module.insert_product_group(product_group, user_id)

async def get_product_group(product_group_id):
    return await product_group_data_module.get_product_group(product_group_id)

async def check_pg_process(product_group_id: int):
    await product_group_data_module.check_pg_process(product_group_id)

async def get_ungrouped_products(
    ungrouped_products_filter: product_group_models.UnGroupedProductsRead,
):

    product_hierarchy_config: dict[str, HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)

    product_hierarchies = {
        product_hierarchy_config[hierarchy_key]["id_column"]: hierarchy_values
        for hierarchy_key,hierarchy_values in ungrouped_products_filter.product_hierarchies.items()
    }

    if ungrouped_products_filter.only_meta_data:
        data = await product_group_data_module.get_ungrouped_products_count_and_percentage(
            product_hierarchies
        )
    else:
        data = await product_group_data_module.get_ungrouped_products(
            product_hierarchies
        )
    return data

async def get_effected_promos_of_product_group(
    product_group_id: int
):
    return await product_group_data_module.get_effected_promos_of_product_group(product_group_id)    

async def delete_product_groups(
    pgs_info: product_group_models.ProductGroupsDelete, user_id: int
):
    await product_group_data_module.delete_product_groups(pgs_info, user_id)

async def pg_name_unique_check(products_info: product_group_models.ProductGroupUpdate):
    await product_group_data_module.pg_name_unique_check(products_info)


async def update_pg_process(
    products_info: product_group_models.ProductGroupUpdate, set_to: int
):
    await product_group_data_module.update_pg_process(products_info, set_to)


async def strategies_promo_process_check(
    products_info: product_group_models.ProductGroupUpdate,
):
    await product_group_data_module.strategies_promo_process_check(products_info)

async def update_product_group(
    products_info: product_group_models.ProductGroupUpdate,
    user_id: int,
):
    start_time = datetime.now()
    try:
        updated_pg_id = await product_group_data_module.update_product_group(
            products_info, user_id
        )
        message = (
            f"Product Group {products_info.product_group_name} modified successfully."
        )
        status = True
    except Exception as err:
        message = f"Product Group {products_info.product_group_name} edit failed."
        status = False
        logger.error(err,exc_info=True)
        updated_pg_id = products_info.product_group_id
    time_taken = (datetime.now() - start_time).total_seconds()
    if time_taken < 2:
        """
        sleeping for 5 secs because the background process notification 
        is going first before the api response
        """
        await asyncio.sleep(5-time_taken)
    await product_group_data_module.update_pg_process(products_info, 0)
    await insert_notification(
        request={},
        user_id=user_id,
        module="product_group",
        action="product group edit",
        message=message,
        promo_id=None,
        status=status,
        identifier="product_group",
        header_text="Product Group Edit",
        promo_ids=None,
        navigate_to=str(updated_pg_id)
    )

async def get_product_group_promos(product_group_id: int):
    data = await product_group_data_module.get_product_group_promos(product_group_id)
    return data


async def download_product_group_report(
    request_payload: product_group_models.FetchSpecificProductGroups,
    user_id: int
):
    product_hierarchy_config: dict[str, HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    query = product_group_queries.PRODUCT_GROUP_DOWNLOAD_QUERY.format(
        client_timezone = get_str_repr(global_constants.CLIENT_TIMEZONE),
        product_hierarchies = get_str_repr(request_payload.product_hierarchies),
        l0_hierarchy_label = product_hierarchy_config["l0_ids"]["label"].upper(),
        l1_hierarchy_label = product_hierarchy_config["l1_ids"]["label"].upper(),
        l2_hierarchy_label = product_hierarchy_config["l2_ids"]["label"].upper()
    )
    logger.info(f"product-group download query: {query}")

    await promo_utils.cloud_function_report_handler(
        fetch_query = query,
        report_file_name = "product_group_report",
        report_type = "excel",
        user_id = user_id,
        promo_name = None
    )

async def get_product_details_from_product_group(product_group_id: int):
    data = await product_group_data_module.get_product_details_from_product_group(product_group_id)
    return data