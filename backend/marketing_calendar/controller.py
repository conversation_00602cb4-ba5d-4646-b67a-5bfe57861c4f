from fastapi import APIRouter
from marketing_calendar import models as marketing_calendar_models
from marketing_calendar import service as marketing_calendar_service
from pricesmart_common import utils as common_utils

router = APIRouter(tags=["Marketing Calendar"])


@router.post("/marketing-calendar/promos")
async def get_promos(
    get_promos_request: marketing_calendar_models.GetMarketingCalendarPromosRequest,
):
    """
    Get promotions for the marketing calendar view.
    
    Args:
        get_promos_request (GetMarketingCalendarPromosRequest): Request model containing filters
            and parameters for fetching marketing calendar promotions
            
    Returns:
        dict: Response containing:
            - data (List[dict]): List of promotion objects
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    data = await marketing_calendar_service.get_promos(
        get_promos_request,
    )

    return common_utils.create_response(
        data=data
    )


@router.post("/marketing-calendar/events")
async def get_events(
    get_events_request: marketing_calendar_models.GetMarketingCalendarEventsRequest,
):
    """
    Get events for the marketing calendar view.
    
    Args:
        get_events_request (GetMarketingCalendarEventsRequest): Request model containing filters
            and parameters for fetching marketing calendar events
            
    Returns:
        dict: Response containing:
            - data (List[dict]): List of event objects
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    data = await marketing_calendar_service.get_events(
        get_events_request,
    )

    return common_utils.create_response(
        data=data
    )
