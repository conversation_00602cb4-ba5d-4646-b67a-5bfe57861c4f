FILTER_BY_IDS_QUERY = """
    final_eligible_promos_cte AS (
        SELECT 
            promo_id 
        FROM 
            price_promo.promo_master 
        where 
            is_deleted = 0
            {ids_where_str}
    ),
"""

GET_PROMOS_DATA_QUERY = """
    SELECT
        promo_id,
        promo_name AS promo_name,
        start_date,
        end_date,
        event_id,
        event_name, 
        is_locked,
        created_by,
        offer_comment,
        status_id,
        status,
        timeline_status,
        step_count,
        products_count,
        stores_count,
        product_selection_type_id,
        product_selection_type,
        store_selection_type_id,
        store_selection_type,
        exclusion_selection_type_id,
        exclusion_selection_type,
        customer_type_id,
        customer_type,
        offer_distribution_channel_id,
        offer_distribution_channel,
        last_approved_scenario_id,
        recommendation_type_id,
        recommendation_type,
        is_under_processing,
        is_auto_resimulated,
        is_overridden,
        override_comment,
        override_reason,
        currency_id,
        currency_name,
        currency_symbol,
        discount_level_id,
        discount_level,
        actual_performance,
        finalized_performance,
        jsonb_build_object(
            'margin',finalized_margin,
            'revenue',finalized_revenue,
            'discount',finalized_discount,
            'promo_spend',finalized_promo_spend,
            'sales_units',finalized_sales_units,
            'margin_percent',finalized_margin_percent,
            'contribution_margin',finalized_contribution_margin,
            'contribution_revenue',finalized_contribution_revenue,
            'contribution_margin_percent',finalized_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'finalized_st_percent', finalized_st_percent
        ) as finalized,

        jsonb_build_object(
            'discount',ia_recc_discount
        ) as ia_recommend,

        jsonb_build_object(
            'margin', finalized_baseline_margin,
            'revenue', finalized_baseline_revenue,
            'sales_units', finalized_baseline_sales_units
        ) as baseline,  

        jsonb_build_object(
            'margin',original_margin,
            'revenue',original_revenue,
            'discount',original_discount,
            'promo_spend',original_promo_spend,
            'sales_units',original_sales_units,
            'margin_percent',original_margin_percent,
            'contribution_margin',original_contribution_margin,
            'contribution_revenue',original_contribution_revenue,
            'contribution_margin_percent',original_contribution_margin_percent
        ) as original,

        jsonb_build_object(
            'margin', finalized_stack_baseline_margin,
            'revenue', finalized_stack_baseline_revenue,
            'sales_units', finalized_stack_baseline_sales_units
        ) as stack_baseline,

        jsonb_build_object(
            'margin',finalized_stack_margin,
            'revenue',finalized_stack_revenue,
            'promo_spend',finalized_stack_promo_spend,
            'sales_units',finalized_stack_sales_units,
            'margin_percent',finalized_stack_margin_percent,
            'contribution_margin',finalized_stack_contribution_margin,
            'contribution_revenue',finalized_stack_contribution_revenue,
            'contribution_margin_percent',finalized_stack_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'finalized_stack_st_percent', finalized_stack_st_percent
        ) as finalized_stack,

        jsonb_build_object(
            'margin',original_stack_margin,
            'revenue',original_stack_revenue,
            'promo_spend',original_stack_promo_spend,
            'sales_units',original_stack_sales_units,
            'margin_percent',original_stack_margin_percent,
            'contribution_margin',original_stack_contribution_margin,
            'contribution_revenue',original_stack_contribution_revenue,
            'contribution_margin_percent',original_stack_contribution_margin_percent
        ) as original_stack,

        jsonb_build_object(
            'margin',actualized_margin,
            'revenue',actualized_revenue,
            'promo_spend',actualized_promo_spend,
            'sales_units',actualized_sales_units,
            'margin_percent',actualized_margin_percent,
            'contribution_margin',actualized_contribution_margin,
            'contribution_revenue',actualized_contribution_revenue,
            'contribution_margin_percent',actualized_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'actualized_st_percent',actualized_st_percent
        ) as actualized,

        jsonb_build_object(
            'sales_units', finalized_incremental_sales_units,
            'revenue', finalized_incremental_revenue,
            'margin', finalized_incremental_margin
        ) as incremental
    FROM 
        price_promo.fn_fetch_marketing_calender_data_using_promo({request_payload}::jsonb)
"""

GET_EVENTS_USING_HIERARCHY = """
    with product_filtered_events as (
        select 
            distinct A.event_id 
        from 
            {promo_schema}.event_product_hierarchy A
        inner join
            {promo_schema}.tb_product_hierarchy_lifecycle_combination B on A.hierarchy_id = B.hierarchy_id 
        {product_hierarchical_where_clause}
        
    ),
    store_filtered_events as (
        select 
            distinct A.event_id 
        from 
            {promo_schema}.event_store_hierarchy A
        inner join
            {promo_schema}.tb_store_hierarchy_combination B on A.hierarchy_id = B.hierarchy_id 
        {store_hierarchical_where_clause}
    )
    select
        A.event_id as value,
        A.name as label,
        A.is_locked
    from
        {promo_schema}.event_master A
    inner join 
        product_filtered_events B on A.event_id = B.event_id
    inner join 
        store_filtered_events C on A.event_id = C.event_id
    where
        {date_range_where_clause}
        and A.is_deleted = 0
        {events_promo_finalized_condition}
    group by
        A.event_id 
"""

GET_EVENTS_USING_HIERARCHY_WITH_METRICS = """
    with product_filtered_events as (
        select 
            distinct A.event_id 
        from 
            {promo_schema}.event_product_hierarchy A
        inner join
            {promo_schema}.tb_product_hierarchy_lifecycle_combination B on A.hierarchy_id = B.hierarchy_id 
        {product_hierarchical_where_clause}
    ),
    store_filtered_events as (
        select 
            distinct A.event_id 
        from 
            {promo_schema}.event_store_hierarchy A
        inner join
            {promo_schema}.tb_store_hierarchy_combination B on A.hierarchy_id = B.hierarchy_id 
        {store_hierarchical_where_clause}
    )
    select
        A.event_id,
        A.name as event_name,
        A.start_date,
        A.end_date,
        case
            when A.start_date > timezone('{timezone}', now()) then 'Upcoming'
            when A.end_date < timezone('{timezone}', now()) then 'Completed'
            else 'Ongoing'
        end as status,
        A.is_locked,
        eam.attribute_value as marketing_notes,
        A.created_by,
        A.updated_by,
        A.created_at,
        A.updated_at
    from
        price_promo.event_master A
    inner join product_filtered_events B on
        A.event_id = B.event_id
    inner join store_filtered_events C on
        A.event_id = C.event_id
    left join price_promo.event_attribute_mapping eam on
        A.event_id = eam.event_id 
        and eam.attribute_id = (
            select id from price_promo.attribute_master 
            where module = 'event'
            and be_identifier = 'marketing_notes'
        )
    where
        {date_range_where_clause}
        and A.is_deleted = 0
"""

GET_EVENTS_USING_IDS = """
    select
         A.event_id,
         A.name as event_name,
         A.start_date,
         A.end_date,
         case 
            when A.start_date > timezone('{timezone}', now()) then 'Upcoming'
            when A.end_date < timezone('{timezone}', now()) then 'Completed'
            else 'Ongoing'
         end as status,
         A.is_locked,
         eam.attribute_value as marketing_notes,
         A.created_by,
         A.updated_by,
         A.created_at,
         A.updated_at
    from
        {promo_schema}.event_master A
    left join {promo_schema}.event_attribute_mapping eam on
        A.event_id = eam.event_id
        and eam.attribute_id = (
            select id from {promo_schema}.attribute_master 
            where module = 'event'
            and be_identifier = 'marketing_notes'
        )
    where 
        A.event_id in {event_ids}
"""

GET_EVENTS_WITH_METRICS = """
    with filtered_event_details_cte as (
        {filtered_events_query}
    ),
    event_promos_cte as (
        select
            event_id,
            count(promo_id) as total_promo_count,
            count(case when status in {finalised_promo_status_list} then 1 end) AS finalised_promo_count
        from  
            {promo_schema}.promo_master 
        where
            status not in {deleted_promo_status_list}
            and event_id in (select event_id from filtered_event_details_cte)
        group by
            event_id
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.ps_recommended_scenarios_agg 
                    WHERE promo_id IN (
                        SELECT promo_id FROM {promo_schema}.promo_master 
                        where event_id in (select event_id from filtered_event_details_cte)
                    )
                ),
                {target_currency_id}::integer
        )
    ),
    original_cte AS (
        select
            pa.event_id,
            ROUND(SUM(sales_units * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_sales_units,
            ROUND(SUM(revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_revenue,
            ROUND(SUM(margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_margin,
            ROUND(SUM(promo_spend * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_margin_percent,
            ROUND(SUM(contribution_revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(SUM(contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_contribution_margin_percent,
            MIN(offer_type_combined_display_name) AS original_discount,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_performance
        FROM
            filtered_event_details_cte fe 
        INNER JOIN
            {promo_schema}.ps_recommended_finalized_agg pa using(event_id)
        inner join 
            global.planned_forex_rate pfr 
            on 
                pa.recommendation_date = pfr.date 
                and pfr.source_currency_id = pa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        GROUP by
            pa.event_id
    ),
    stacked_original_cte AS (
        select
            P.event_id,
            ROUND(SUM(sales_units * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(SUM(revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_revenue,
            ROUND(SUM(margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_margin,
            ROUND(SUM(promo_spend * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_stack_margin_percent,
            ROUND(SUM(contribution_revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(SUM(contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS original_stack_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_stack_contribution_margin_percent,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_stack_performance
        FROM
            {promo_schema}.ps_recommended_finalized_stack_agg pa
        INNER join
            {promo_schema}.promo_master P on P.promo_id = any(pa.promo_ids)
        inner join 
            global.planned_forex_rate pfr 
            on 
                pa.recommendation_date = pfr.date 
                and pfr.source_currency_id = pa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where
            P.event_id in (select event_id from filtered_event_details_cte)
        GROUP BY
            P.event_id
    ),
    finalized_scenarios_cte AS (
        select
            pm.event_id,
            pm.promo_id,
            pof.is_default,
            pm.total_inventory
        FROM 
            filtered_event_details_cte fe
        inner JOIN 
            {promo_schema}.promo_master pm ON fe.event_id = pm.event_id
        LEFT JOIN 
            {promo_schema}.tb_promo_override_forecast pof ON pm.promo_id = pof.promo_id AND coalesce(pm.last_approved_scenario_id,0) = pof.scenario_id
    ),
    promo_level_finalized_cte AS (
        SELECT
            sfsc.promo_id,
            sfsc.event_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.sales_units ELSE original.sales_units END::DECIMAL, 2) AS finalized_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_sales_units ELSE original.baseline_sales_units END::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_sales_units ELSE original.incremental_sales_units END::DECIMAL, 2) AS finalized_incremental_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.revenue ELSE original.revenue END::DECIMAL, 2)  * pfr.planned_conversion_multiplier AS finalized_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_revenue ELSE original.baseline_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_revenue ELSE original.incremental_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_incremental_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.margin ELSE original.margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_margin ELSE original.baseline_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_baseline_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_margin ELSE original.incremental_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_incremental_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.promo_spend ELSE original.promo_spend END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.contribution_revenue ELSE original.contribution_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.contribution_margin ELSE original.contribution_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_contribution_margin,
            CASE WHEN sfsc.is_default THEN override.offer_type_combined_display_name ELSE original.offer_type_combined_display_name END AS finalized_discount,
            CASE WHEN sfsc.is_default THEN override.incremental_margin ELSE original.incremental_margin END * pfr.planned_conversion_multiplier AS finalised_incremental_margin,
            sfsc.total_inventory as finalized_promo_level_total_inventory,
            LEAST(100, GREATEST(0, CASE 
            WHEN COALESCE(sfsc.total_inventory, 0) = 0 THEN 0 
            ELSE ROUND(((ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.sales_units ELSE original.sales_units END::DECIMAL, 2) / sfsc.total_inventory) * 100)::DECIMAL, 1) 
            END)) AS finalized_promo_level_st_percent
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            {promo_schema}.ps_recommended_finalized_agg original ON sfsc.promo_id = original.promo_id
        LEFT JOIN 
            {promo_schema}.ps_recommended_finalized_override_agg override ON sfsc.promo_id = override.promo_id and original.recommendation_date = override.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                original.recommendation_date = pfr.date 
                and pfr.source_currency_id = original.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ),

    promo_level_inventory_by_event_cte AS (
        SELECT
            event_id,
            SUM(finalized_promo_level_total_inventory) AS finalized_event_level_total_inventory
        FROM (
            SELECT DISTINCT promo_id, event_id, finalized_promo_level_total_inventory
            FROM promo_level_finalized_cte
        ) sub
        GROUP BY event_id
    ),

    event_level_finalized_cte as (
        select
            sfsc.event_id,
            ROUND(SUM(finalized_sales_units)::DECIMAL, 2) AS finalized_sales_units,
            ROUND(SUM(finalized_baseline_sales_units)::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(SUM(finalized_incremental_sales_units)::DECIMAL, 2) AS finalized_incremental_sales_units,
            ROUND(SUM(finalized_revenue)::DECIMAL, 2) AS finalized_revenue,
            ROUND(SUM(finalized_baseline_revenue)::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(SUM(finalized_incremental_revenue)::DECIMAL, 2) AS finalized_incremental_revenue,
            ROUND(SUM(finalized_margin)::DECIMAL, 2) AS finalized_margin,
            ROUND(SUM(finalized_baseline_margin)::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND(SUM(finalized_incremental_margin)::DECIMAL, 2) AS finalized_incremental_margin,
            ROUND((SUM(finalized_margin) * 100 / NULLIF(SUM(finalized_revenue), 0))::decimal, 2) AS finalized_margin_percent,
            ROUND(SUM(finalized_promo_spend)::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(SUM(finalized_contribution_revenue)::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(SUM(finalized_contribution_margin)::DECIMAL, 2) AS finalized_contribution_margin,
            ROUND((SUM(finalized_contribution_margin) * 100 / NULLIF(SUM(finalized_contribution_revenue), 0))::decimal, 2) AS finalized_contribution_margin_percent,
            min(finalized_discount) as finalized_discount,
            ROUND(((SUM(finalised_incremental_margin) / NULLIF(ABS(SUM(finalized_baseline_margin)), 0)) * 100)::DECIMAL, 2) AS finalized_performance,
            MAX(piec.finalized_event_level_total_inventory) as finalized_event_level_total_inventory,
            LEAST(100, GREATEST(0, CASE 
            WHEN COALESCE(MAX(piec.finalized_event_level_total_inventory), 0) = 0 THEN 0 
            ELSE ROUND(((ROUND(SUM(finalized_sales_units)::DECIMAL, 2) / MAX(piec.finalized_event_level_total_inventory)) * 100)::DECIMAL, 1) 
            END)) AS finalized_event_level_st_percent
        FROM
            promo_level_finalized_cte sfsc 
        JOIN promo_level_inventory_by_event_cte piec ON sfsc.event_id = piec.event_id
        group by
            sfsc.event_id
    ),
    promo_level_stacked_finalized_cte AS (
        SELECT
            sfsc.promo_id,
            sfsc.event_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.sales_units ELSE original.sales_units END::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_sales_units ELSE original.baseline_sales_units END::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_sales_units ELSE original.incremental_sales_units END::DECIMAL, 2) AS finalized_stack_incremental_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.revenue ELSE original.revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_revenue ELSE original.baseline_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_revenue ELSE original.incremental_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_incremental_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.margin ELSE(original.margin) END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.baseline_margin ELSE original.baseline_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_baseline_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.incremental_margin ELSE original.incremental_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_incremental_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.promo_spend ELSE original.promo_spend END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.contribution_revenue ELSE original.contribution_revenue END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.contribution_margin ELSE original.contribution_margin END::DECIMAL, 2) * pfr.planned_conversion_multiplier AS finalized_stack_contribution_margin,
            CASE WHEN sfsc.is_default THEN override.incremental_margin ELSE original.incremental_margin END * pfr.planned_conversion_multiplier AS finalised_stack_incremental_margin,
            sfsc.total_inventory as finalized_stack_promo_level_total_inventory,
            LEAST(100, GREATEST(0, CASE 
            WHEN COALESCE(sfsc.total_inventory, 0) = 0 THEN 0 
            ELSE ROUND(((ROUND(CASE WHEN sfsc.is_default = TRUE THEN override.sales_units ELSE original.sales_units END::DECIMAL, 2) / sfsc.total_inventory) * 100)::DECIMAL, 1) 
            END)) AS finalized_stack_promo_level_st_percent 
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            {promo_schema}.ps_recommended_finalized_stack_agg original ON sfsc.promo_id = ANY(original.promo_ids)
        LEFT JOIN 
            {promo_schema}.ps_recommended_finalized_stack_override_agg override ON sfsc.promo_id = ANY(override.promo_ids) and original.recommendation_date = override.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                original.recommendation_date = pfr.date 
                and pfr.source_currency_id = original.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ),

    promo_level_stack_inventory_by_event_cte AS (
        SELECT
            event_id,
            SUM(finalized_stack_promo_level_total_inventory) AS finalized_stack_event_level_total_inventory
        FROM (
            SELECT DISTINCT promo_id, event_id, finalized_stack_promo_level_total_inventory
            FROM promo_level_stacked_finalized_cte
        ) sub
        GROUP BY event_id
    ),

    event_level_stacked_finalized_cte as (
        select
            sfsc.event_id,
            ROUND(SUM(finalized_stack_sales_units)::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(SUM(finalized_stack_baseline_sales_units)::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(SUM(finalized_stack_incremental_sales_units)::DECIMAL, 2) AS finalized_stack_incremental_sales_units,
            ROUND(SUM(finalized_stack_revenue)::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(SUM(finalized_stack_baseline_revenue)::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(SUM(finalized_stack_incremental_revenue)::DECIMAL, 2) AS finalized_stack_incremental_revenue,
            ROUND(SUM(finalized_stack_margin)::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(SUM(finalized_stack_baseline_margin)::DECIMAL, 2) AS finalized_stack_baseline_margin,
            ROUND(SUM(finalized_stack_incremental_margin)::DECIMAL, 2) AS finalized_stack_incremental_margin,
            ROUND((SUM(finalized_stack_margin) * 100 / NULLIF(SUM(finalized_stack_revenue), 0))::NUMERIC, 2) AS finalized_stack_margin_percent,
            ROUND(SUM(finalized_stack_promo_spend)::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(SUM(finalized_stack_contribution_revenue)::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(SUM(finalized_stack_contribution_margin)::DECIMAL, 2) AS finalized_stack_contribution_margin,
            ROUND(
                (SUM(finalized_stack_contribution_margin) * 100 / 
                NULLIF(SUM(finalized_stack_contribution_revenue), 0)
                )::NUMERIC,
                2
            ) AS finalized_stack_contribution_margin_percent,
            ROUND(((SUM(finalised_stack_incremental_margin) / NULLIF(ABS(SUM(finalized_stack_baseline_margin)), 0)) * 100)::DECIMAL, 2) AS finalized_stack_performance,
            MAX(piec.finalized_stack_event_level_total_inventory) as finalized_stack_event_level_total_inventory,
            LEAST(100, GREATEST(0, CASE 
            WHEN COALESCE(MAX(piec.finalized_stack_event_level_total_inventory), 0) = 0 THEN 0 
            ELSE ROUND(((ROUND(SUM(finalized_stack_sales_units)::DECIMAL, 2) / MAX(piec.finalized_stack_event_level_total_inventory)) * 100)::DECIMAL, 1) 
            END)) AS finalized_stack_event_level_st_percent
        FROM
            promo_level_stacked_finalized_cte sfsc 
        JOIN promo_level_stack_inventory_by_event_cte piec ON sfsc.event_id = piec.event_id
        group by
            sfsc.event_id
    ),
    actualized_cte AS (
        SELECT
            sfsc.event_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS actualized_sales_units,
            ROUND(SUM(baseline_sales_units)::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(SUM(incremental_sales_units)::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(SUM(revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_revenue,
            ROUND(SUM(baseline_revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(SUM(incremental_revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(SUM(margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_margin,
            ROUND(SUM(baseline_margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(SUM(incremental_margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(SUM(promo_spend * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN SUM(baseline_margin) IS NULL OR SUM(baseline_margin) = 0 THEN NULL
                ELSE ROUND((SUM(incremental_margin) / ABS(SUM(baseline_margin)))::DECIMAL * 100::DECIMAL, 2)
            END AS performance,
            SUM(sfsc.total_inventory) as actualized_total_inventory,
            LEAST(100, GREATEST(0, CASE 
            WHEN COALESCE(SUM(sfsc.total_inventory), 0) = 0 THEN 0 
            ELSE ROUND(((ROUND(SUM(sales_units)::DECIMAL, 2) / SUM(sfsc.total_inventory)) * 100)::DECIMAL, 1) 
            END)) AS actualized_st_percent
        FROM
            {promo_schema}.ps_recommended_actuals_agg praa
        INNER JOIN finalized_scenarios_cte sfsc ON praa.promo_id = sfsc.promo_id
        inner join 
            global.actual_forex_rate afr 
            on 
                praa.recommendation_date = afr.date 
                and afr.source_currency_id = praa.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            sfsc.event_id IN (SELECT event_id FROM filtered_event_details_cte)
        GROUP BY
            sfsc.event_id
    )
    select 
        A.event_id,
        A.event_name,
        A.start_date,
        A.end_date,
        A.status,
        A.is_locked,
        A.marketing_notes,
        B.total_promo_count,
        B.finalised_promo_count,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        CASE 
            WHEN acc.performance IS NULL THEN NULL 
            ELSE price_promo.fn_get_performance_repr(acc.performance) 
        END AS actual_performance,
        CASE 
            WHEN fc.finalized_performance IS NULL THEN NULL 
            ELSE price_promo.fn_get_performance_repr(fc.finalized_performance) 
        END AS finalized_performance,
        um1.user_code as created_by,
        um1.name as created_by_name,
        um2.user_code as updated_by,
        um2.name as updated_by_name,
        
        jsonb_build_object(
            'margin',fc.finalized_margin,
            'revenue',fc.finalized_revenue,
            'discount',fc.finalized_discount,
            'promo_spend',fc.finalized_promo_spend,
            'sales_units',fc.finalized_sales_units,
            'margin_percent',fc.finalized_margin_percent,
            'contribution_margin',fc.finalized_contribution_margin,
            'contribution_revenue',fc.finalized_contribution_revenue,
            'contribution_margin_percent',fc.finalized_contribution_margin_percent,
            'incremental_sales_units',fc.finalized_incremental_sales_units,
            'incremental_revenue', fc.finalized_incremental_revenue,
            'incremental_margin', fc.finalized_incremental_margin,
            'total_inventory', fc.finalized_event_level_total_inventory,
            'finalized_st_percent', fc.finalized_event_level_st_percent      
        ) as finalized,
        
        jsonb_build_object(
            'margin', fc.finalized_baseline_margin,
            'revenue', fc.finalized_baseline_revenue,
            'sales_units', fc.finalized_baseline_sales_units
        ) as baseline,  
        
        jsonb_build_object(
            'margin',oc.original_margin,
            'revenue',oc.original_revenue,
            'discount',oc.original_discount,
            'promo_spend',oc.original_promo_spend,
            'sales_units',oc.original_sales_units,
            'margin_percent',oc.original_margin_percent,
            'contribution_margin',oc.original_contribution_margin,
            'contribution_revenue',oc.original_contribution_revenue,
            'contribution_margin_percent',oc.original_contribution_margin_percent
        ) as original,
        
        jsonb_build_object(
            'margin', sfc.finalized_stack_baseline_margin,
            'revenue', sfc.finalized_stack_baseline_revenue,
            'sales_units', sfc.finalized_stack_baseline_sales_units
        ) as stack_baseline,
        
        jsonb_build_object(
            'margin',sfc.finalized_stack_margin,
            'revenue',sfc.finalized_stack_revenue,
            'promo_spend',sfc.finalized_stack_promo_spend,
            'sales_units',sfc.finalized_stack_sales_units,
            'margin_percent',sfc.finalized_stack_margin_percent,
            'contribution_margin',sfc.finalized_stack_contribution_margin,
            'contribution_revenue',sfc.finalized_stack_contribution_revenue,
            'contribution_margin_percent',sfc.finalized_stack_contribution_margin_percent,
            'incremental_sales_units',sfc.finalized_stack_incremental_sales_units,
            'incremental_revenue', sfc.finalized_stack_incremental_revenue,
            'incremental_margin', sfc.finalized_stack_incremental_margin ,
            'total_inventory', sfc.finalized_stack_event_level_total_inventory,
            'finalized_stack_st_percent', sfc.finalized_stack_event_level_st_percent   
        ) as finalized_stack,
        
        jsonb_build_object(
            'margin',soc.original_stack_margin,
            'revenue',soc.original_stack_revenue,
            'promo_spend',soc.original_stack_promo_spend,
            'sales_units',soc.original_stack_sales_units,
            'margin_percent',soc.original_stack_margin_percent,
            'contribution_margin',soc.original_stack_contribution_margin,
            'contribution_revenue',soc.original_stack_contribution_revenue,
            'contribution_margin_percent',soc.original_stack_contribution_margin_percent
        ) as original_stack,
        
        jsonb_build_object(
            'margin',acc.actualized_margin,
            'revenue',acc.actualized_revenue,
            'promo_spend',acc.actualized_promo_spend,
            'sales_units',acc.actualized_sales_units,
            'margin_percent',acc.actualized_margin_percent,
            'contribution_margin',acc.actualized_contribution_margin,
            'contribution_revenue',acc.actualized_contribution_revenue,
            'contribution_margin_percent',acc.actualized_contribution_margin_percent,
            'incremental_sales_units',acc.actualized_incremental_sales_units,
            'incremental_revenue', acc.actualized_incremental_revenue,
            'incremental_margin', acc.actualized_incremental_margin,
            'total_inventory', acc.actualized_total_inventory,
            'actualized_st_percent', acc.actualized_st_percent  
        ) as actualized
    from 
        filtered_event_details_cte A
    left join
        event_promos_cte B on A.event_id = B.event_id
    left join 
        event_level_finalized_cte fc on A.event_id = fc.event_id
    left join  
        event_level_stacked_finalized_cte sfc ON A.event_id = sfc.event_id
    left join
        original_cte oc on A.event_id = oc.event_id
    left join
        stacked_original_cte soc on A.event_id = soc.event_id
    left join 
        actualized_cte acc on A.event_id = acc.event_id
    inner join 
        {global_schema}.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    left join 
         {global_schema}.user_master um1 ON A.created_by = um1.user_code
    left join
         {global_schema}.user_master um2 ON A.updated_by = um2.user_code

"""
