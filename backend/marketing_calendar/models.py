from typing import Optional
from pricesmart_common import models as common_models
from pricesmart_common import helpers as common_helpers
from pydantic import model_validator, PydanticUserError


class GetMarketingCalendarPromosRequest(common_models.OptionalBaseFilters):
    promo_ids: list[int] = []
    event_ids: list[int] = []

    @model_validator(mode="before")
    def validate_params(cls, values):
        return common_helpers.validate_non_id_fields(values)


class GetMarketingCalendarEventsRequest(common_models.BaseFilters):
    event_ids: list[int] = []
    is_finalized: bool = False
    show_metrics: bool = False
    target_currency_id: Optional[int] = None
