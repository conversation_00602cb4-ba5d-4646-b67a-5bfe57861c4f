from marketing_calendar import models as marketing_calendar_models
from marketing_calendar import queries as marketing_calendar_queries
from pricesmart_common.utils import async_execute_query, get_key_value_str, get_str_repr
from configuration.environment import environment
from promotions import utils as promo_utils
from pricesmart_common import constants as common_constants
from promotions import constants as promo_constants
from enums.Enums import PromoStatusEnum,ConfigKeyEnum,ConfigModuleEnum
from pricesmart_common.data import get_config_value
from filters.types import HierarchyFiltersInfoType
from logger.logger import logger


async def get_promos(
    request_payload: marketing_calendar_models.GetMarketingCalendarPromosRequest
):
    request_payload_dict=request_payload.model_dump()

    query = marketing_calendar_queries.GET_PROMOS_DATA_QUERY.format(
        request_payload=get_str_repr(request_payload_dict)
    )
    print(query)
    return await async_execute_query(query)


async def get_events(
    request_payload: marketing_calendar_models.GetMarketingCalendarEventsRequest
):

    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)

    events_promo_finalized_condition = (
        f"and exists (select 1 from {environment.promo_schema}.promo_master PM where PM.event_id = A.event_id" 
        f" and pm.status in ({PromoStatusEnum.FINALIZED.value},{PromoStatusEnum.EXECUTION_APPROVED.value},{PromoStatusEnum.PLACEHOLDER.value}))" 
        if request_payload.is_finalized 
        else 
        ""
    )

    date_range_where_clause = promo_utils.apply_date_range_filter(
        request_payload.start_date, request_payload.end_date, request_payload.show_partially_overlapping_events
    )

    product_hierarchical_where_clause = common_constants.WHERE_CONDITION + common_constants.AND.join(
        [
            f'''{get_key_value_str(product_hierarchy_config[key]["id_column"], val)}'''
            for key, val in request_payload.product_hierarchies.items()
            if val
        ]
    )

    store_hierarchical_where_clause = common_constants.WHERE_CONDITION + common_constants.AND.join(
        [
            f'''{get_key_value_str(store_hierarchy_config[key]["id_column"], val)}'''
            for key, val in request_payload.store_hierarchies.items()
            if val
        ]
    ) if request_payload.store_hierarchies else ""

    if request_payload.show_metrics :
        if request_payload.event_ids:
            filtered_events_query = marketing_calendar_queries.GET_EVENTS_USING_IDS.format(
                promo_schema=environment.promo_schema,
                event_ids=get_str_repr(request_payload.event_ids),
                timezone=common_constants.CLIENT_TIMEZONE
            )
        else:
            filtered_events_query = marketing_calendar_queries.GET_EVENTS_USING_HIERARCHY_WITH_METRICS.format(
                promo_schema=environment.promo_schema,
                global_schema=environment.global_schema,
                date_range_where_clause=date_range_where_clause,
                product_hierarchical_where_clause=product_hierarchical_where_clause,
                store_hierarchical_where_clause=store_hierarchical_where_clause,
                timezone=common_constants.CLIENT_TIMEZONE,
            )

        query = marketing_calendar_queries.GET_EVENTS_WITH_METRICS.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            filtered_events_query=filtered_events_query,
            finalised_promo_status_list=get_str_repr([promo_constants.OFFER_STATUS[promo_constants.FINALIZED]]),
            deleted_promo_status_list=get_str_repr([promo_constants.OFFER_STATUS[promo_constants.ARCHIVED]]),
            target_currency_id=get_str_repr(request_payload.target_currency_id)
        )
    else:
        query = marketing_calendar_queries.GET_EVENTS_USING_HIERARCHY.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            events_promo_finalized_condition=events_promo_finalized_condition,
            date_range_where_clause=date_range_where_clause,
            product_hierarchical_where_clause=product_hierarchical_where_clause,
            store_hierarchical_where_clause=store_hierarchical_where_clause,
            timezone=common_constants.CLIENT_TIMEZONE,
        )
    logger.info(query)

    return await async_execute_query(query)
