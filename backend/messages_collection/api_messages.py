from client_configuration import constants as client_configuration_constants

api_messages = {
    "EVENTS" :{
        "SUCCESS" :{
            "INSERT" :  client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " {?name} created successfully! ",
            "DELETE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " deleted successfully! ",
            "UPDATE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " updated successfully! ",
            "GET"    : "Data successfully fetched! ",
            "LOCKED"   : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " locked successfully! ",
            "UNLOCKED" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " un-locked successfully! "
        },
        "SUCCESS_WITH_NO_RETURN" :{
            "DELETE" : "{?name} " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be deleted as it does not exist! ",
            "UPDATE" : "{?name} " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be updated as it does not exist! "
        },
        "FAIL" :{
            "INSERT" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " {?name}  already available! Try again with different " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY + " name!" ,
            "DELETE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be deleted at the moment! Try again later! or contact the administrator. ",
            "UPDATE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be updated at the moment! Try again later! or contact the administrator. " ,
            "GET"    : "Error in fetching data! Try again later! ",
            "LOCKED"    : "Unable to lock -- {?name} ! Try again later! ",
            "UNLOCKED"    : "Unable to un-lock -- {?name} ! Try again later! "
        },
        "OPERATION_ABORTED" : {
            "INSERT" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " {?name} already available! Try again with different " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY + " name!" ,
            "DELETE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be deleted at the moment! Try again later! or contact the administrator. ",
            "UPDATE" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " can not be updated at the moment! Try again later! or contact the administrator. " ,
            "GET"    : "Error in fetching data! Try again later! ",
            "LOCKED"    : "Unable to lock -- {?name} ! Try again later! ",
            "UNLOCKED"    : "Unable to un-lock -- {?name} ! Try again later! "
        }
    }
}

validation_message = {
         "EVENTS" :{
               "LOCKED" :{
                    "DELETE" : "{?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is locked!, can not be deleted ! please refresh the page !",
                    "UPDATE" : "{?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is locked!, can not be updated ! please refresh the page !",
                    "LOCKED"   : "{?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is already locked! please refresh the page !",
                    "UNLOCKED" : "{?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()  + " is  un-locked!!"
               },
               "UNLOCKED" :{
                    "DELETE" : "{?name} " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is deleted!, can not be unlocked ! please refresh the page !",
                    "UPDATE" : "{?name} " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is locked!, can not be updated ! please refresh the page !",
                    "UNLOCKED"   : "{?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is already  un-locked! please refresh the page !"
               },
               "DELETED" :{
                    "DELETE" : " {?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is already deleted!,  please refresh the page !",
                    "UPDATE" : " {?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is deleted!, can not be updated ! please refresh the page !",
                    "LOCKED"   : " {?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is deleted!, hence can not be locked! please refresh the page !",
                    "UNLOCKED" : " {?name}, " + client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " is deleted!, hence can not be locked! please refresh the page !"
               },
               "NONE" :{
                   "NULL" : client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize() + " not available!, check your input "
               }
         }

}