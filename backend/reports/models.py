from datetime import date, datetime
from typing import Dict, List, Literal, Optional, Union
from pydantic import BaseModel, Field, field_validator
from product import models as product_models
from promotions import models as promo_models


class ProductHierarchy(BaseModel):
    l0_ids: list[int] = []
    l1_ids: list[int] = []
    l2_ids: list[int] = []
    l3_ids: list[int] = []
    l4_ids: list[int] = []
    brand: list[int] = []
    lifecycle_indicator: list[int] = []


class StoreHierarchy(BaseModel):
    s0_ids: list[int] = Field(default_factory=list)
    s1_ids: list[int] = Field(default_factory=list)
    s2_ids: list[int] = Field(default_factory=list)
    s3_ids: list[int] = Field(default_factory=list)
    s4_ids: list[int] = Field(default_factory=list)
    s5_ids: list[int] = Field(default_factory=list)

    @field_validator('s2_ids', mode='before')
    def set_empty_s2_ids(cls, v):
        # Always set s2_ids to an empty list, ignoring the input
        # TODO: Update promo filtering logic.
        return []

class Filters(
    promo_models.PromoDateRange
):
    product_hierarchies: dict[str,list[int]] = {}
    store_hierarchies: dict[str,list[int]] = {}
    target_currency_id: Optional[int] = None


class ReportsFilters(Filters):
    promo_ids: list[int] = []
    event_ids: list[int] = []


class PostOfferAnalysisModel(ReportsFilters):
    product_hierarchy_levels: list[int] = []
    store_hierarchy_levels: list[int] = []
    time_levels: int = 0
    offer_level: int = 0
    for_download : bool = False


class DownloadReportsFilter(ReportsFilters):
    report_type : str 
    report_name : str 
    is_top_10 : bool = True

class DownloadPostOfferAnalysis(PostOfferAnalysisModel):
    report_type : str 
    report_name : str 
    for_download : bool = True
