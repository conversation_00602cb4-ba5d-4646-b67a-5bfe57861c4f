from app.dependencies import UserDependency
from common_utils.custom_route_handler import CustomRouteHandler
from fastapi import BackgroundTasks, Depends, Request, status
from fastapi.routing import APIRouter
from pricesmart_common import constants as global_constants
from pricesmart_common import models as common_models
from pricesmart_common.constants import PROMOTION_API_TAG
from promotions import constants as promo_constants
from promotions import models as promo_models
from promotions import service as promo_service
from promotions import utils as promo_utils
from reports import models as report_models
from reports import service as reports_service

reports_router = APIRouter(route_class=CustomRouteHandler)


@reports_router.post(
    path="/get-reports-graph-data",
    tags=[global_constants.REPORTS_API_TAG],
)
async def get_margin_and_revenue_graph_data(
    report_filters: report_models.ReportsFilters,
):
    """
    Get margin and revenue graph data based on filters.
    
    Args:
        report_filters (ReportsFilters): Filters for the report data
        
    Returns:
        dict: Response containing:
            - data (dict): Graph data
            - message (str): Success/error message
    """
    data = await reports_service.get_margin_and_revenue_graph_data(report_filters)
    return common_models.BaseResponseBody(message="Successful", data=data[0])


@reports_router.post(
    path="/get-top-and-bottom-performing-offers",
    tags=[global_constants.REPORTS_API_TAG],
)
async def get_top_and_bottom_performing_offers(
    report_filters: report_models.ReportsFilters,
):
    """
    Get top and bottom performing offers based on filters.
    
    Args:
        report_filters (ReportsFilters): Filters for the report data
        
    Returns:
        dict: Response containing:
            - data (dict): Performance data for offers
            - message (str): Success/error message
    """
    data = await reports_service.get_top_and_bottom_performing_offers(report_filters)
    return common_models.BaseResponseBody(message="Successful", data=data[0] if data else [])


@reports_router.post(
    path="/get-promos-by-filters",
    tags=[global_constants.REPORTS_API_TAG],
)
async def get_promos_by_filters(
    filters: report_models.ReportsFilters,
):
    """
    Get promotions based on specified filters.
    
    Args:
        filters (Filters): Filter criteria for promotions
        
    Returns:
        dict: Response containing:
            - data (dict): Filtered promotions data
            - message (str): Success/error message
    """
    data = await reports_service.get_promos_by_filters(filters)
    return common_models.BaseResponseBody(message="Successful", data=data[0])


@reports_router.post(
    path="/download-top-and-bottom-performing-offers",
    tags=[global_constants.REPORTS_API_TAG],
)
async def download_top_or_bottom_performing_offers(
    request_body: report_models.DownloadReportsFilter,
    user_id: UserDependency,
    background_tasks: BackgroundTasks,
):
    """
    Download report of top/bottom performing offers.
    
    Args:
        request_body (DownalodReportsFilter): Report download configuration
        request (Request): FastAPI request object
        background_tasks (BackgroundTasks): Background tasks handler
        
    Returns:
        dict: Response containing:
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    data = {}
    _message = "Request received. You will be notified once the downloads are ready."
    fetch_query = await reports_service.get_top_or_bottom_performing_offers_query(
        request_body
    )
    background_tasks.add_task(
        promo_utils.cloud_function_report_handler,
        fetch_query=fetch_query,
        report_file_name=request_body.report_name,
        report_type=request_body.report_type,
        user_id=user_id,
        promo_name=None,
    )
    return common_models.BaseResponseBody(
        message=_message, data=data
    )


@reports_router.post(
    path="/get-post-offer-analysis-report",
    tags=[global_constants.REPORTS_API_TAG],
)
async def get_post_offer_analysis_report(
    post_offer_filters: report_models.PostOfferAnalysisModel,
):
    """
    Get post-offer analysis report based on filters.
    
    Args:
        post_offer_filters (PostOfferAnalysisModel): Filters for post-offer analysis
        
    Returns:
        dict: Response containing:
            - data (dict): Post-offer analysis data
            - message (str): Success/error message
            - status (bool): Operation status
    """
    data = await reports_service.get_post_offer_analysis_report(post_offer_filters)
    return common_models.BaseResponseBody(message="Successful", status=True, data=data)


@reports_router.post(
    path="/download-post-offer-analysis-report",
    tags=[global_constants.REPORTS_API_TAG],
)
async def download_post_offer_analysis_report(
    request_body: report_models.DownloadPostOfferAnalysis,
    background_tasks: BackgroundTasks,
    user_id: UserDependency
):
    """
    Download post-offer analysis report.
    
    Args:
        request_body (DownalodPostOfferAnalysis): Report download configuration
        request (Request): FastAPI request object
        background_tasks (BackgroundTasks): Background tasks handler
        
    Returns:
        dict: Response containing:
            - message (str): Success/error message
            - status (int): HTTP status code
            - user_id (str): User identifier
    """
    data = {}
    _message = "Request received. You will be notified once the downloads are ready."
    fetch_query = reports_service.get_post_offer_analysis_report_query(request_body)
    background_tasks.add_task(
        promo_utils.cloud_function_report_handler,
        fetch_query=fetch_query,
        report_file_name=request_body.report_name,
        report_type=request_body.report_type,
        user_id=user_id,
        is_json_data=True,
        promo_name=None,
        apply_formatter=True,
        formatter_identifier="promo_post_offer_analysis"
    )
    return common_models.BaseResponseBody(
        message=_message,data=data
    )
