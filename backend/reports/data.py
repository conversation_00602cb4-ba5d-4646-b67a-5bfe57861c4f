from configuration.environment import environment
from pricesmart_common.utils import async_execute_query
from pricesmart_common import constants as global_constants
from pricesmart_common import utils as global_utils
from promotions import utils as promo_utils
from reports import models as report_models
from reports import queries as report_queries
from pricesmart_common.utils import get_str_repr, get_array_format


def get_promo_ids_sub_query(
    product_hierarchies,
    store_hierarchies,
    report_filters: report_models.ReportsFilters,
):

    # Forming the dictionary for dates_range.
    promo_start_date = report_filters.start_date.strftime(
        global_constants.PYTHON_DATE_FORMAT
    ) if report_filters.start_date else ""
    promo_end_date = report_filters.end_date.strftime(
        global_constants.PYTHON_DATE_FORMAT
    ) if report_filters.end_date else ""

    promo_ids = report_filters.promo_ids if isinstance(report_filters, report_models.ReportsFilters) else None
    event_ids = report_filters.event_ids if isinstance(report_filters, report_models.ReportsFilters) else None

    if promo_ids or event_ids:
        ids_where_str = global_utils.get_key_value_str("promo_id", promo_ids) if promo_ids else global_utils.get_key_value_str("event_id", event_ids)
        filtered_promos_ids_query = report_queries.GET_PROMO_IDS_BY_IDS.format(
            ids_where_str=ids_where_str
        )
    else:
        filtered_promos_ids_query = report_queries.GET_PROMO_IDS_BY_FILTERS.format(
            start_date = get_str_repr(promo_start_date),
            end_date = get_str_repr(promo_end_date),
            product_hierarchies = get_str_repr(product_hierarchies),
            store_hierarchies = get_str_repr(store_hierarchies),
            show_partial_overlapping = get_str_repr(True)
        )

    return filtered_promos_ids_query


async def get_margin_and_revenue_graph_data(
    product_hierarchies,
    store_hierarchies,
    report_filters: report_models.ReportsFilters,
):
    filtered_promos_ids_query = get_promo_ids_sub_query(
        product_hierarchies, store_hierarchies, report_filters
    )
    fetch_query = report_queries.GET_MARGIN_AND_REVENUE_GRAPH_DATA.format(
        filtered_promos_ids_query=filtered_promos_ids_query,
        promo_schema=environment.promo_schema,
        target_currency_id=get_str_repr(report_filters.target_currency_id)
    )
    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return final_result


async def get_top_and_bottom_performing_offers(
    product_hierarchies,
    store_hierarchies,
    report_filters: report_models.ReportsFilters,
):
    filtered_promos_ids_query = get_promo_ids_sub_query(
        product_hierarchies, store_hierarchies, report_filters
    )
    fetch_query = report_queries.GET_TOP_AND_BOTTOM_PERFORMING_OFFERS.format(
        filtered_promos_ids_query=filtered_promos_ids_query,
        promo_schema=environment.promo_schema,
        target_currency_id=get_str_repr(report_filters.target_currency_id)
    )
    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return final_result


async def get_promos_by_filters(
    product_hierarchies,
    store_hierarchies,
    report_filters: report_models.ReportsFilters,
):
    filtered_promos_ids_query = get_promo_ids_sub_query(
        product_hierarchies, store_hierarchies, report_filters
    )
    fetch_query = report_queries.GET_PROMOS_BY_FILTERS.format(
        filtered_promos_ids_query=filtered_promos_ids_query,
        promo_schema=environment.promo_schema,
        end_date=report_filters.end_date
    )
    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return [final_result[0]["promos"]]


def get_post_offer_analysis_query(
    function_name: str,
    post_offer_filters: report_models.PostOfferAnalysisModel,
):
    fetch_query = report_queries.GET_POST_OFFER_ANALYSIS_REPORT_DATA.format(
        function_name=function_name,
        promo_opt_schema=environment.promo_opt_schema,
        product_hierarchy_levels=post_offer_filters.product_hierarchy_levels,
        store_hierarchy_levels=post_offer_filters.store_hierarchy_levels,
        time_levels=post_offer_filters.time_levels,
        offer_level=post_offer_filters.offer_level,
        start_date=post_offer_filters.start_date,
        end_date=post_offer_filters.end_date,
        product_hierarchies=get_str_repr(post_offer_filters.product_hierarchies),
        store_hierarchies=get_str_repr(post_offer_filters.store_hierarchies),
        promo_ids=get_array_format(post_offer_filters.promo_ids),
        event_ids=get_array_format(post_offer_filters.event_ids),
        target_currency_id=get_str_repr(post_offer_filters.target_currency_id),
        for_download=post_offer_filters.for_download,
    )
    return fetch_query


async def get_post_offer_analysis_report(
    post_offer_filters: report_models.PostOfferAnalysisModel,
):

    if post_offer_filters.offer_level == -200:  # Overall level
        function_name = "fn_reporting_r1_overall_level"
    elif post_offer_filters.offer_level == 0:  # Promo level
        function_name = "fn_reporting_r1_offer_level"
    else:  # Event level
        function_name = "fn_reporting_r1_event_level"

    fetch_query = get_post_offer_analysis_query(function_name, post_offer_filters)
    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return final_result[0]["json_data"]


async def get_top_or_bottom_performing_offers_query(
    product_hierarchies,
    store_hierarchies,
    report_filters: report_models.DownloadReportsFilter,
):
    filtered_promos_ids_query = get_promo_ids_sub_query(
        product_hierarchies, store_hierarchies, report_filters
    )
    if report_filters.is_top_10:
        fetch_query = report_queries.GET_TOP_10_PERFORMING_OFFERS.format(
            filtered_promos_ids_query=filtered_promos_ids_query,
            promo_schema=environment.promo_schema,
            target_currency_id=get_str_repr(report_filters.target_currency_id)
        )
    else:
        fetch_query = report_queries.GET_BOTTOM_10_PERFORMING_OFFERS.format(
            filtered_promos_ids_query=filtered_promos_ids_query,
            promo_schema=environment.promo_schema,
            target_currency_id=get_str_repr(report_filters.target_currency_id)
        )
    print(fetch_query)
    return fetch_query


def get_post_offer_analysis_report_query(
    post_offer_filters: report_models.DownloadPostOfferAnalysis,
):
    function_name = "fn_reporting_r1_offer_level"
    if post_offer_filters.offer_level == -200:
        function_name = "fn_reporting_r1_overall_level"
    fetch_query = get_post_offer_analysis_query(function_name, post_offer_filters)
    print(fetch_query)
    return fetch_query
