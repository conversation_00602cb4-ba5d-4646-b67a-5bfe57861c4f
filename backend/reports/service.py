from enums.Enums import Config<PERSON>eyEnum, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from reports import data as reports_data
from reports import models as report_models
from promotions import constants as promo_constants


async def get_margin_and_revenue_graph_data(
    report_filters: report_models.ReportsFilters,
):

    return await reports_data.get_margin_and_revenue_graph_data(
        report_filters.product_hierarchies, report_filters.store_hierarchies, report_filters
    )


async def get_top_and_bottom_performing_offers(
    report_filters: report_models.ReportsFilters,
):

    return await reports_data.get_top_and_bottom_performing_offers(
        report_filters.product_hierarchies, report_filters.store_hierarchies, report_filters
    )


async def get_promos_by_filters(
    filters: report_models.ReportsFilters,
):
    return await reports_data.get_promos_by_filters(
        filters.product_hierarchies, filters.store_hierarchies, filters
    )


async def get_post_offer_analysis_report(
    post_offer_filters: report_models.PostOfferAnalysisModel,
):
    return await reports_data.get_post_offer_analysis_report(post_offer_filters)


async def get_top_or_bottom_performing_offers_query(
    report_filters: report_models.DownloadReportsFilter,
):

    return await reports_data.get_top_or_bottom_performing_offers_query(
        report_filters.product_hierarchies, report_filters.store_hierarchies, report_filters
    )


def get_post_offer_analysis_report_query(
    post_offer_filters: report_models.DownloadPostOfferAnalysis,
):
    return reports_data.get_post_offer_analysis_report_query(post_offer_filters)
