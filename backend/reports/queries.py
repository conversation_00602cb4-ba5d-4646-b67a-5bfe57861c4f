GET_PROMO_IDS_BY_IDS = """
    select promo_id from price_promo.promo_master where {ids_where_str}
"""


GET_PROMO_IDS_BY_FILTERS = """
    WITH final_eligible_promos_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.ad_type AS ad_type_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing
        FROM
            price_promo.promo_master pm
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping}
            )))
            AND pm.status = 8
    )
    select
        promo_id
    from
        final_eligible_promos_cte
"""


GET_MARGIN_AND_REVENUE_GRAPH_DATA = """
    with filtered_promos_cte as(
        {filtered_promos_ids_query}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.ps_recommended_actuals_agg 
                    WHERE promo_id IN (
                        SELECT promo_id FROM {promo_schema}.promo_master 
                        where event_id in (select event_id from filtered_promos_cte)
                    )
                ),
                {target_currency_id}::integer
        )
    )
    select 
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        foo.*,
        (baseline_margin + incremental_margin) as net_margin,
        (baseline_revenue + incremental_revenue) as net_revenue
    from
        (
            select
                round(sum(baseline_margin * afr.planned_conversion_multiplier)::decimal,2) as baseline_margin,
                round(sum(incremental_margin * afr.planned_conversion_multiplier)::decimal,2) as incremental_margin,
                round(sum(case when incremental_margin > 0 then incremental_margin else 0 end)::decimal,2) as margin_positive,
                round(sum(case when incremental_margin < 0 then incremental_margin else 0 end)::decimal,2) as margin_negative,
                round(sum(baseline_revenue * afr.planned_conversion_multiplier)::decimal,2) as baseline_revenue,
                round(sum(incremental_revenue * afr.planned_conversion_multiplier)::decimal,2) as incremental_revenue,
                round(sum(case when incremental_revenue > 0 then incremental_revenue else 0 end )::decimal,2) as revenue_positive,
                round(sum(case when incremental_revenue < 0 then incremental_revenue else 0 end )::decimal,2) as revenue_negative
            from
                filtered_promos_cte fpc
            left join
                {promo_schema}.ps_recommended_actuals_agg pra using(promo_id)	
            inner join 
                global.actual_forex_rate afr 
                on 
                    pra.recommendation_date = afr.date 
                    and afr.source_currency_id = pra.currency_id
                    and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        ) foo
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
"""


GET_TOP_AND_BOTTOM_PERFORMING_OFFERS = """
    with filtered_promos_cte as(
        {filtered_promos_ids_query}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.ps_recommended_actuals_agg 
                    WHERE promo_id IN (
                        SELECT promo_id FROM {promo_schema}.promo_master 
                        where event_id in (select event_id from filtered_promos_cte)
                    )
                ),
                {target_currency_id}::integer
        )
    ),
    top10 as (
        select
            top.*,
            row_number() over (order by incremental_margin desc) as row_num
        from
            (
                select
                    c.name as offer_name,
                    c.start_date,
                    c.end_date,
                    em.name as event_name,
                    round(sum(a.sales_units)::decimal,2) as actual_unit,
                    round(sum(a.margin * afr.planned_conversion_multiplier)::decimal,2) as actual_margin,
                    round(sum(a.revenue * afr.planned_conversion_multiplier)::decimal,2) as actual_sales,
                    round(sum(a.promo_spend * afr.planned_conversion_multiplier)::decimal,2) as actual_markdown,
                    round((100 * sum(a.margin)/nullif(sum(a.revenue), 0))::decimal, 2) as actual_gm_percentage,
                    round(sum(a.contribution_margin * afr.planned_conversion_multiplier)::decimal,2) as actual_contribution_margin,
                    round((100 * sum(a.contribution_margin)/nullif(sum(a.contribution_revenue), 0))::decimal, 2) as actual_cm_percentage,
                    round(sum(a.incremental_sales_units)::decimal,2) as incremental_units,
                    round(sum(a.incremental_margin * afr.planned_conversion_multiplier)::decimal,2) as incremental_margin,
                    round(sum(a.incremental_revenue * afr.planned_conversion_multiplier)::decimal,2) as incremental_sales,
                    CASE 
                        WHEN SUM(a.baseline_margin) IS NULL OR SUM(a.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(a.margin) - SUM(a.baseline_margin)) / ABS(SUM(a.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS actual_performance,
                        
                    CASE 
                        WHEN SUM(b.baseline_margin) IS NULL OR SUM(b.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(b.margin) - SUM(b.baseline_margin)) / ABS(SUM(b.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS finalized_performance
                from
                    filtered_promos_cte fpc
                left join
                    {promo_schema}.ps_recommended_actuals_agg a using(promo_id)
                left join
                    {promo_schema}.ps_recommended_finalized_agg b using(promo_id)
                join 
                    {promo_schema}.promo_master c on a.promo_id = c.promo_id and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                left join
                    price_promo.event_master em on c.event_id = em.event_id
                inner join 
                    global.actual_forex_rate afr 
                    on 
                        a.recommendation_date = afr.date 
                        and afr.source_currency_id = a.currency_id
                        and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                group by
                    1, 2, 3, 4
            ) top
    ),
    bottom10 as (
        select
            bottom.* ,
            row_number() over (order by incremental_margin ) as row_num
        from
            (
                select
                    c.name as offer_name,
                    c.start_date,
                    c.end_date,
                    em.name as event_name,
                    round(sum(a.sales_units)::decimal,2) as actual_unit,
                    round(sum(a.margin * afr.planned_conversion_multiplier)::decimal,2) as actual_margin,
                    round(sum(a.revenue * afr.planned_conversion_multiplier)::decimal,2) as actual_sales,
                    round(sum(a.promo_spend * afr.planned_conversion_multiplier)::decimal,2) as actual_markdown,
                    round((100 * sum(a.margin)/nullif(sum(a.revenue), 0))::decimal, 2) as actual_gm_percentage,
                    round(sum(a.contribution_margin * afr.planned_conversion_multiplier)::decimal,2) as actual_contribution_margin,
                    round((100 * sum(a.contribution_margin)/nullif(sum(a.contribution_revenue), 0))::decimal, 2) as actual_cm_percentage,
                    round(sum(a.incremental_sales_units)::decimal,2) as incremental_units,
                    round(sum(a.incremental_margin * afr.planned_conversion_multiplier)::decimal,2) as incremental_margin,
                    round(sum(a.incremental_revenue * afr.planned_conversion_multiplier)::decimal,2) as incremental_sales,
                    CASE 
                        WHEN SUM(a.baseline_margin) IS NULL OR SUM(a.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(a.margin) - SUM(a.baseline_margin)) / ABS(SUM(a.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS actual_performance,
                        
                    CASE 
                        WHEN SUM(b.baseline_margin) IS NULL OR SUM(b.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(b.margin) - SUM(b.baseline_margin)) / ABS(SUM(b.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS finalized_performance
                from
                    filtered_promos_cte fpc
                left join
                    {promo_schema}.ps_recommended_actuals_agg a using(promo_id)
                left join
                    {promo_schema}.ps_recommended_finalized_agg b using(promo_id)
                join 
                    {promo_schema}.promo_master c on a.promo_id = c.promo_id and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                left join
                    price_promo.event_master em on c.event_id = em.event_id
                inner join 
                    global.actual_forex_rate afr 
                    on 
                        a.recommendation_date = afr.date 
                        and afr.source_currency_id = a.currency_id
                        and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                group by
                    1, 2, 3, 4
            ) bottom 
    )
    select
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        json_agg(a.*) as top10,
        json_agg(b.*) as bottom10
    from
        top10 a,
        bottom10 b
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    where
        a.row_num = b.row_num
        and a.row_num <= 10
	    and b.row_num <= 10
    group by
    	tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol
"""


GET_PROMOS_BY_FILTERS = """
    with filtered_promos_cte as(
        {filtered_promos_ids_query}
    )
    select 
    	array_agg(
    		json_build_object(
    			'value', fpc.promo_id,
    			'label', pm.name
    		)
    	) promos
    from 
    	filtered_promos_cte fpc
    join 
    	{promo_schema}.promo_master pm using(promo_id)
    where 
        pm.end_date <= '{end_date}'::date
"""


GET_POST_OFFER_ANALYSIS_REPORT_DATA = """
    select 
        {function_name} as json_data
    from 
        {promo_opt_schema}.{function_name}(
            array{product_hierarchy_levels}::integer[],    
            array{store_hierarchy_levels}::integer[],
            {time_levels}::integer,
            {offer_level}::integer,
            '{start_date}'::date,
            '{end_date}'::date,
            {product_hierarchies}::jsonb,
            {store_hierarchies}::jsonb, 
            {promo_ids}::integer[],
            {event_ids}::integer[],
            {target_currency_id}::integer,
            for_download => {for_download}::bool
        )
"""


GET_POST_OFFER_ANALYSIS_REPORT_DATA_OLD = """
    select 
        {function_name} as json_data
    from 
        {promo_schema}.{function_name}(
            array{product_hierarchy_levels}::integer[],    
            array{store_hierarchy_levels}::integer[],
            {time_levels}::integer,
            {offer_level}::integer,
            '{start_date}'::date,
            '{end_date}'::date,
            array{l0_ids}::integer[],    
            array{l1_ids}::integer[],
            array{l2_ids}::integer[],    
            array{l3_ids}::integer[],
            array{l4_ids}::integer[],
            array{brand}::integer[],
            array{s0_ids}::integer[],    
            array{s1_ids}::integer[],
            array{s2_ids}::integer[], 
            array{promo_ids}::integer[],
            array{event_ids}::integer[],
            for_download => {for_download}::bool
        )
"""


GET_TOP_10_PERFORMING_OFFERS = """
    with filtered_promos_cte as(
        {filtered_promos_ids_query}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.ps_recommended_actuals_agg 
                    WHERE promo_id IN (
                        SELECT promo_id FROM {promo_schema}.promo_master 
                        where event_id in (select event_id from filtered_promos_cte)
                    )
                ),
                {target_currency_id}::integer
        )
    ),
    top10 as (
        select
            top.*,
            row_number() over (order by incremental_margin desc) as "RANK"
        from
            (
                select
                    c.name as offer_name,
                    d.name as event_name,
                    c.start_date,
                    c.end_date,
                    round(sum(a.sales_units)::decimal,2) as actual_sales_unit,
                    round(sum(a.margin * afr.planned_conversion_multiplier)::decimal,2) as actual_margin,
                    round(sum(a.revenue * afr.planned_conversion_multiplier)::decimal,2) as actual_revenue,
                    round(sum(a.promo_spend * afr.planned_conversion_multiplier)::decimal,2) as actual_promo_spend,
                    round((100 * sum(a.margin)/nullif(sum(a.revenue), 0))::decimal, 2) as actual_gm_percentage,
                    round(sum(a.contribution_margin * afr.planned_conversion_multiplier)::decimal,2) as actual_contribution_margin,
                    round((100 * sum(a.contribution_margin)/nullif(sum(a.contribution_revenue), 0))::decimal, 2) as actual_cm_percentage,
                    round(sum(a.incremental_sales_units)::decimal,2) as incremental_sales_units,
                    round(sum(a.incremental_margin * afr.planned_conversion_multiplier)::decimal,2) as incremental_margin,
                    round(sum(a.incremental_revenue * afr.planned_conversion_multiplier)::decimal,2) as incremental_revenue,
                    CASE 
                        WHEN SUM(a.baseline_margin) IS NULL OR SUM(a.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(a.margin) - SUM(a.baseline_margin)) / ABS(SUM(a.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS actual_performance,
                        
                    CASE 
                        WHEN SUM(b.baseline_margin) IS NULL OR SUM(b.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(b.margin) - SUM(b.baseline_margin)) / ABS(SUM(b.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS finalized_performance
                from
                    filtered_promos_cte fpc
                left join
                    {promo_schema}.ps_recommended_actuals_agg a using(promo_id)
                left join
                    {promo_schema}.ps_recommended_finalized_agg b using(promo_id)
                join 
                    {promo_schema}.promo_master c on a.promo_id = c.promo_id and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                join
                    {promo_schema}.event_master d on c.event_id = d.event_id
                inner join 
                    global.actual_forex_rate afr 
                    on 
                        a.recommendation_date = afr.date 
                        and afr.source_currency_id = a.currency_id
                        and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                group by
                    1, 2, 3, 4
            ) top
    )
    select
        offer_name,
        event_name,
        start_date,
        end_date,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        incremental_margin,
        incremental_revenue,
        incremental_sales_units,
        actual_promo_spend,
        actual_margin,
        actual_revenue,
        actual_sales_unit,
        actual_gm_percentage,
        actual_contribution_margin,
        actual_cm_percentage,
        actual_performance,
        finalized_performance
    from
        top10 a
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    where
        a."RANK" <= 10
    order by 
        a."RANK"
"""


GET_BOTTOM_10_PERFORMING_OFFERS = """
    with filtered_promos_cte as(
        {filtered_promos_ids_query}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.ps_recommended_actuals_agg 
                    WHERE promo_id IN (
                        SELECT promo_id FROM {promo_schema}.promo_master 
                        where event_id in (select event_id from filtered_promos_cte)
                    )
                ),
                {target_currency_id}::integer
        )
    ),
    bottom10 as (
        select
            bottom.* ,
            row_number() over (order by incremental_margin ) as "RANK"
        from
            (
                select
                    c.name as offer_name,
                    d.name as event_name,
                    c.start_date,
                    c.end_date,
                    round(sum(a.sales_units)::decimal,2) as actual_sales_unit,
                    round(sum(a.margin * afr.planned_conversion_multiplier)::decimal,2) as actual_margin,
                    round(sum(a.revenue * afr.planned_conversion_multiplier)::decimal,2) as actual_revenue,
                    round(sum(a.promo_spend * afr.planned_conversion_multiplier)::decimal,2) as actual_promo_spend,
                    round((100 * sum(a.margin)/nullif(sum(a.revenue), 0))::decimal, 2) as actual_gm_percentage,
                    round(sum(a.contribution_margin * afr.planned_conversion_multiplier)::decimal,2) as actual_contribution_margin,
                    round((100 * sum(a.contribution_margin)/nullif(sum(a.contribution_revenue), 0))::decimal, 2) as actual_cm_percentage,
                    round(sum(a.incremental_sales_units)::decimal,2) as incremental_sales_units,
                    round(sum(a.incremental_margin * afr.planned_conversion_multiplier)::decimal,2) as incremental_margin,
                    round(sum(a.incremental_revenue * afr.planned_conversion_multiplier)::decimal,2) as incremental_revenue,
                    CASE 
                        WHEN SUM(a.baseline_margin) IS NULL OR SUM(a.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(a.margin) - SUM(a.baseline_margin)) / ABS(SUM(a.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS actual_performance,
                        
                    CASE 
                        WHEN SUM(b.baseline_margin) IS NULL OR SUM(b.baseline_margin) = 0 THEN NULL 
                        ELSE price_promo.fn_get_performance_repr(
                        	ROUND(((SUM(b.margin) - SUM(b.baseline_margin)) / ABS(SUM(b.baseline_margin)))::NUMERIC * 100, 2) 
                        )
                    END AS finalized_performance
                from
                    filtered_promos_cte fpc
                left join
                    {promo_schema}.ps_recommended_actuals_agg a using(promo_id)
                left join
                    {promo_schema}.ps_recommended_finalized_agg b using(promo_id)
                join 
                    {promo_schema}.promo_master c on a.promo_id = c.promo_id and a.recommendation_date >= c.start_date and a.recommendation_date <= c.end_date
                join
                    {promo_schema}.event_master d on c.event_id = d.event_id
                inner join 
                    global.actual_forex_rate afr 
                    on 
                        a.recommendation_date = afr.date 
                        and afr.source_currency_id = a.currency_id
                        and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                group by
                    1, 2, 3, 4
            ) bottom 
    )
    select
        offer_name,
        event_name,
        start_date,
        end_date,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        incremental_margin,
        incremental_revenue,
        incremental_sales_units,
        actual_promo_spend,
        actual_margin,
        actual_revenue,
        actual_sales_unit,
        actual_gm_percentage,
        actual_contribution_margin,
        actual_cm_percentage,
        actual_performance,
        finalized_performance
    from
        bottom10 b
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    where
        b."RANK" <= 10
    order by 
        b."RANK"
"""