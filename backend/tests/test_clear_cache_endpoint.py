import pytest
from httpx import AsyncClient
from app.main import app
from pricesmart_common.data import get_client_configuration

BASE = "/pricesmart/api/v3"

@pytest.mark.asyncio
async def test_configuration_and_clear_cache_flow():
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 1) First GET → fresh data, priming cache
        r1 = await client.get(f"{BASE}/configuration")
        assert r1.status_code == 200
        payload1 = r1.json()

        # 2) Second GET → should hit cache (same payload)
        r2 = await client.get(f"{BASE}/configuration")
        assert r2.status_code == 200
        assert r2.json() == payload1

        # 3) Verify cache is non-empty internally
        info = get_client_configuration.cache_info()
        assert info.currsize >= 1

        # 4) POST to clear-cache → invalidates all cached entries
        valid_token = "<paste-your-valid-token-here>"

        r3 = await client.post(
            f"{BASE}/clear-cache",
            headers={"Authorization": valid_token}
        )

        
        assert r3.status_code == 200
        assert "cache cleared" in r3.json().get("message", "").lower()

        # 5) Internal cache should now be empty
        assert get_client_configuration.cache_info().currsize == 0

        # 6) Next GET → fresh fetch again (payload matches, but re-computed)
        r4 = await client.get(f"{BASE}/configuration")
        assert r4.status_code == 200
        assert r4.json() == payload1
