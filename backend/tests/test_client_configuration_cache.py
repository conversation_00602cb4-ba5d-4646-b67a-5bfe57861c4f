import pytest
from pricesmart_common.data import get_client_configuration

@pytest.mark.asyncio
async def test_cache_clear_primes_and_clears():
    # Ensure starting from a clean slate
    get_client_configuration.cache_clear()
    assert get_client_configuration.cache_info().currsize == 0

    # Prime the cache for both True and False variants
    await get_client_configuration(True)
    await get_client_configuration(False)
    info = get_client_configuration.cache_info()
    assert info.currsize == 2, f"expected 2 entries, got {info.currsize}"

    # Now clear and verify
    get_client_configuration.cache_clear()
    info_after = get_client_configuration.cache_info()
    assert info_after.currsize == 0, "cache should be empty after clear"
