from pydantic import BaseModel, model_validator, PydanticUserError
from typing import Optional


class NotificationsGet(BaseModel):
    application: str
    page_number: Optional[int] = 1
    limit: Optional[int] = 100


class NotificationsUpdateModel(BaseModel):
    notification_ids: Optional[list[int]] = []
    mark_all_as_read: bool = False
    application: Optional[str] = None

    @model_validator(mode="before")
    def validate_application_if_mark_all_as_read(cls, values):
        mark_all_as_read = values.get("mark_all_as_read")
        application = values.get("application")
        notification_ids = values.get('notification_ids')
        if mark_all_as_read and not application:
            raise PydanticUserError(
                "The 'application' field is required when 'mark_all_as_read' is True.",
                code="value_error.missing"
            )

        if not mark_all_as_read and not notification_ids:
            raise PydanticUserError(
                "The 'notification_ids' field is required when 'mark_all_as_read' is False.",
                code="value_error.missing"
            )

        return values
