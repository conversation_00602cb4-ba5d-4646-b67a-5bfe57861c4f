from notifications import models as notification_models
from notifications import queries as notification_queries
from pricesmart_common import constants as common_constants
from pricesmart_common.utils import async_execute_query, get_str_repr
from configuration.environment import environment


async def get_notifications(request_payload: notification_models.NotificationsGet, user_id: int):
    query = notification_queries.GET_NOTIFICATIONS_QUERY.format(
        user_id=user_id,
        application=get_str_repr(request_payload.application),
        client_timezone=get_str_repr(common_constants.CLIENT_TIMEZONE),
        db_datetime_format=get_str_repr(common_constants.DB_DATETIME_FORMAT),
        limit=request_payload.limit,
        offset=request_payload.limit * (request_payload.page_number - 1),
    )
    notification_data = await async_execute_query(query)
    return notification_data[0]["fn_fetch_notification"] if notification_data else notification_data


async def update_notifications(request_payload: notification_models.NotificationsUpdateModel, user_id: int):
    formatted_notification_ids = None
    notification_ids = request_payload.notification_ids
    mark_all_as_read = request_payload.mark_all_as_read

    if mark_all_as_read:
        where_clause = f"user_id = {user_id} and read_at is null and application={get_str_repr(request_payload.application)}"
    else:
        where_clause = f"notification_id in {get_str_repr(notification_ids)}"

    update_query = notification_queries.UPDATE_NOTIFICATIONS_QUERY.format(
        where_clause=where_clause,
    )
    return await async_execute_query(update_query)
