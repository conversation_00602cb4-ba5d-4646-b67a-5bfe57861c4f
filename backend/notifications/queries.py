
GET_NOTIFICATIONS_QUERY = """
    select 
        * 
    from 
        global.fn_fetch_notification(
            {user_id}::integer, 
            {application}::varchar, 
            {client_timezone}::text,
            {db_datetime_format}::text,
            {limit}::integer,
            {offset}::integer
    )
"""

UPDATE_NOTIFICATIONS_QUERY = """
    update global.tb_notifications 
    set
        read_at = now()
    where 
        {where_clause}
"""


