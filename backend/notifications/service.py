from notifications import models as notification_models
from notifications import data as notification_data


async def get_notifications(request_payload: notification_models.NotificationsGet, user_id: int):
    return await notification_data.get_notifications(request_payload, user_id)


async def update_notifications(request_payload: notification_models.NotificationsUpdateModel, user_id: int):
    return await notification_data.update_notifications(request_payload, user_id)