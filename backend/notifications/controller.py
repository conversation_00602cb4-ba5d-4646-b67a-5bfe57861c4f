from fastapi import APIRouter, Request
from notifications import constants as notification_constants
from notifications import models as notification_models
from notifications import service as notification_service
from pricesmart_common import utils as common_utils
from app.dependencies import UserDependency


router = APIRouter(tags=[notification_constants.MODULE_NAME])


@router.post(
    path=notification_constants.NOTIFICATIONS_URL
)
async def get_notifications(request_payload: notification_models.NotificationsGet, user_id: UserDependency):
    data = await notification_service.get_notifications(request_payload, user_id)
    return common_utils.create_response(
        data=data
    )


@router.post(
    path='/acknowledge-notifications'
)
async def update_notifications(request_payload: notification_models.NotificationsUpdateModel, user_id: UserDependency):
    data = await notification_service.update_notifications(request_payload, user_id)
    return common_utils.create_response(
        data=data
    )


