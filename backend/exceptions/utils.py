import logging
import traceback

from pricesmart_common.models import BaseJSONResponse

logger = logging.getLogger(__name__)


def log_error_details(exc: Exception):
    # Extract traceback
    tb_lines = traceback.format_exc().splitlines()

    # Default error type
    error_type = type(exc).__name__

    # Extract and format error details
    error_message = str(exc).splitlines()

    # Find file and line info from traceback
    file_line_info = [line for line in tb_lines if "File" in line]

    # Define the lengths and symbols for the borders and dividers
    error_start_symbol = "🔜"  # Symbol indicating the start of an error
    error_end_symbol = "🔚"  # Symbol indicating the end of an error
    category_divider_symbol = "✦"
    border_length = 25
    category_length = 110

    # Create the border strings
    error_start = f"{error_start_symbol * border_length} {'ERROR START'} {error_start_symbol * border_length}"
    category_divider = f"{category_divider_symbol * category_length}"
    error_end = f"{error_end_symbol * border_length} {'ERROR END'} {error_end_symbol * border_length}"

    # Log the error with consistent formatting
    logger.error(error_start)
    logger.error("\n")
    logger.error("Error Type:: %s", error_type)
    logger.error(category_divider)
    logger.error("Error Detail:: %s", error_message)
    logger.error(category_divider)
    logger.error(traceback.format_exc())
    logger.error(error_end)
