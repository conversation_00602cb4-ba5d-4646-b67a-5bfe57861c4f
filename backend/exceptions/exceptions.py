# exceptions.py
from exceptions.constants import (
    BAD_REQUEST_MSG,
    COMMON_ERROR_MSG,
    CONFLICT_MSG,
    EMPTY_EXCLUSION_UPLOAD_MSG,
    INVALID_ACCESS_MSG,
    INVALID_TEMPLATE_MSG,
    NO_FILE_NAME_MSG,
    NO_SKU_COLUMN_MSG,
    NO_SKU_MAPPING_MSG,
    NO_STORE_COLUMN_MSG,
    NO_STORE_MAPPING_MSG,
    NOT_FOUND_MSG,
    OPTIMISATION_ENDPOINT_MSG,
    TIMEOUT_MSG,
    UNDER_MAINTENANCE_MSG,
    UNIQUE_KEY_CONSTRAINT_MSG,
    UNKNOWN_FILE_MSG,
)
from exceptions.custom_base_exception import CustomBaseException
from fastapi import status, HTTPException


class UnderMaintenanceException(CustomBaseException):
    def __init__(self, message: str = UNDER_MAINTENANCE_MSG) -> None:
        super().__init__(message, status.HTTP_503_SERVICE_UNAVAILABLE)


class CommonException(CustomBaseException):
    def __init__(self, message: str = COMMON_ERROR_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class RuntimeExecutionException(CustomBaseException):
    def __init__(
        self,
        message: str = COMMON_ERROR_MSG,
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ) -> None:
        super().__init__(message, status_code)


class BusinessLogicException(CustomBaseException):
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ) -> None:
        super().__init__(message, status_code)


class InvalidAccessException(CustomBaseException):
    def __init__(
        self,
        message: str = INVALID_ACCESS_MSG,
    ) -> None:
        super().__init__(message, status.HTTP_403_FORBIDDEN)


class UniqueKeyConstraintException(CustomBaseException):
    def __init__(self, message: str = UNIQUE_KEY_CONSTRAINT_MSG) -> None:
        super().__init__(message, status.HTTP_409_CONFLICT)


class UniqueConstraintViolation(HTTPException):
    def __init__(self, column_name, value) -> None:
        super().__init__(
            status_code=409, detail=f"{column_name} with '{value}' already exists"
        )

class NoSkuColumnException(CustomBaseException):
    def __init__(
        self,
        message: str = NO_SKU_COLUMN_MSG,
    ) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class NoSkuMappingException(CustomBaseException):
    def __init__(self, message: str = NO_SKU_MAPPING_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class NoStoreColumnException(CustomBaseException):
    def __init__(
        self,
        message: str = NO_STORE_COLUMN_MSG,
    ) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class NoStoreMappingException(CustomBaseException):
    def __init__(self, message: str = NO_STORE_MAPPING_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class OptimisationEndpointException(CustomBaseException):
    def __init__(self, message: str = OPTIMISATION_ENDPOINT_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class ConflictException(CustomBaseException):
    def __init__(self, message: str = CONFLICT_MSG) -> None:
        super().__init__(message, status.HTTP_409_CONFLICT)


class BadRequestException(CustomBaseException):
    def __init__(self, message: str = BAD_REQUEST_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class TimeoutException(CustomBaseException):
    def __init__(self, message: str = TIMEOUT_MSG) -> None:
        super().__init__(message, status.HTTP_504_GATEWAY_TIMEOUT)


class NotFoundException(CustomBaseException):
    def __init__(self, message: str = NOT_FOUND_MSG) -> None:
        super().__init__(message, status.HTTP_404_NOT_FOUND)


class NoFileNameException(CustomBaseException):
    def __init__(self, message: str = NO_FILE_NAME_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class UnknownFileException(CustomBaseException):
    def __init__(self, message: str = UNKNOWN_FILE_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class InvalidTemplateException(CustomBaseException):
    def __init__(self, message: str = INVALID_TEMPLATE_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class EmptyExclusionUploadException(CustomBaseException):
    def __init__(self, message: str = EMPTY_EXCLUSION_UPLOAD_MSG) -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)
