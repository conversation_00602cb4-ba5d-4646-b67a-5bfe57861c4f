from exceptions.constants import COMMON_ERROR_MSG
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from pricesmart_common.models import BaseJSONResponse
from logger.logger import logger
import traceback

async def custom_exception_handler(
    request: Request, exc: Exception
) -> BaseJSONResponse:
    user_id = (
        request.state.user_id if request and hasattr(request.state, "user_id") else None
    )

    if isinstance(exc, HTTPException):
        message = exc.detail
    else:
        message = getattr(exc, "message", COMMON_ERROR_MSG)
    status_code = getattr(exc, "status_code", status.HTTP_500_INTERNAL_SERVER_ERROR)
    

    # Create error summary with extracted details
    details = str(exc).splitlines()

    if len(details) == 1:
        detail_formatted = details[0]
    else:
        detail_formatted = details

    error_summary = {
        "type": type(exc).__name__,
        "detail": detail_formatted,
        "traceback": traceback.format_exc().splitlines(),
        "file_line_info": [
            line for line in traceback.format_exc().splitlines() if "File" in line
        ],
    }
    logger.info(error_summary)

    return BaseJSONResponse(
        status_code=status_code,
        message=message,
        success=False,
        user_id=user_id,
        data=[]
    )