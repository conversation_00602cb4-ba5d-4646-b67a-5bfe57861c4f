# custom_base_exception.py


class CustomBaseException(Exception):
    """
    Base class for custom exceptions.
    """

    def __init__(self, message: str, status_code: int) -> None:
        super().__init__(message)
        self.message = message
        self.status_code = status_code

    def __str__(self) -> str:
        # return f"{self.__class__.__name__}: {self.message}"
        return self.message
