from decision_dashboard import models as dd_models
from decision_dashboard import queries as dd_queries
from configuration.environment import environment
from pricesmart_common.utils import async_execute_query, get_key_value_str, get_str_repr
from pricesmart_common import constants as common_constants
from promotions import utils as promo_utils
from promotions import constants as promo_constants
import json


async def get_decision_dashboard_promos_data(
    request_payload: dd_models.GetDecisionDashboardPromosRequest
):
    request_payload=request_payload.model_dump()

    query = dd_queries.GET_DECISION_DASHBOARD_DATA_QUERY.format(
        request_payload=get_str_repr(request_payload)
    )
    print(query)
    return await async_execute_query(query)