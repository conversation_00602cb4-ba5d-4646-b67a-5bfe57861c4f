from pricesmart_common import models as common_models
from typing import Optional
from pydantic import model_validator, BaseModel
from pricesmart_common import helpers as common_helpers

class Aggregation(BaseModel):
    marketing: list[str] = None
    timeline: int = -1
    product_hierarchy_levels: list[int] = []
    store_hierarchy_levels: list[int] = []
    target_currency_id: Optional[int] = None


class GetDecisionDashboardPromosRequest(common_models.OptionalBaseFilters):
    event_ids: list[int] = []
    promo_ids: list[int] = []
    aggregation: Optional[Aggregation]

    @model_validator(mode="before")
    def validate_params(cls, values):
        return common_helpers.validate_non_id_fields(values)

    
