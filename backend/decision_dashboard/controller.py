from fastapi import APIRouter
from decision_dashboard import models as dd_models
from decision_dashboard import service as dd_service
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as global_constants

router = APIRouter(tags=["decision dashboard"])


@router.post("/decision-dashboard/promos")
async def get_decision_dashboard_promos(
    request_payload: dd_models.GetDecisionDashboardPromosRequest,
):
    """
    Get promotions for the decision dashboard view.
    
    Args:
        request_payload (GetDecisionDashboardPromosRequest): Request model containing filters and parameters
            for fetching promotions
            
    Returns:
        dict: Response containing:
            - data: List of promotions matching the request criteria
            - message: Success/error message
            - status: HTTP status code
    """
    data = await dd_service.get_decision_dashboard_promos(
        request_payload
    )

    return common_utils.create_response(
        data = data
    )