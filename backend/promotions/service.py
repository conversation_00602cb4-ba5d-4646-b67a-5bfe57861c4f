import datetime
from typing import Optional
from fastapi.exceptions import RequestValidation<PERSON>rror
import httpx
from typing import Optional
from configuration.environment import environment
from exceptions.exceptions import BusinessLogicException, ConflictException
from fastapi import BackgroundTasks, UploadFile, status
from filters.types import HierarchyFiltersInfoType, ExclusionUploadConfigInfoType
from logger.logger import logger
from pricesmart_common import constants as common_constant
from pricesmart_common.data import get_config_value
from pricesmart_common.utils import async_execute_query, get_str_repr
from promotions import constants as promo_constants
from promotions import data as promo_data
from promotions import models as promo_models
from promotions import utils as promo_utils
from promotions import queries as promo_queries
from promotions.constants import NO_RESPONSE
from promotions.exceptions import (
    OptCallFailedException,
    PromoAlreadyExistsException,
    OptRefreshApiException,
    SimulationApiCallFailedException,
    UptoDiscountMismatchError
)
from exceptions.exceptions import CommonException
from server_sent_events.utils import insert_notification
from common_utils.notifications import NotificationModules,NotificationHeaderText
from promotions.decorators import log_action_decorator
from common_utils import notifications as notify
from events import data as event_data_module
import events.service as event_service
from events import exceptions as event_exceptions
from enums.Enums import ConfigModuleEnum, ConfigKeyEnum
from client_configuration import constants as client_configuration_constants


async def check_tier_existence_service(promo_id: int,tier_id: Optional[int], tier_name: str):
    return await promo_data.check_tier_existence_data(promo_id, tier_id, tier_name)

async def create_promotion_v3(
    create_promo_request: promo_models.CreatePromoRequest,
    user_id: int
)-> dict:

    if (await promo_data.check_promo_existence(
        create_promo_request.promo_name,
        create_promo_request.start_date,
        create_promo_request.end_date
    )):
        raise PromoAlreadyExistsException(
            f"{client_configuration_constants.PROMO_IDENTIFIER_STANDARD.capitalize()} with the name '{create_promo_request.promo_name}' already exists."
        )
    
    data = await promo_data.create_promotion_v3(
        create_promo_request,
        user_id
    )

    return data

async def handle_refresh_metrics_for_other_affected_promos(
    promo_id: int,
    user_id: int
):
    if not (await is_finalised_or_execution_approved_promos_present([promo_id])):
        return

    req_data: promo_models.InvokeOptModel = {
        "promo_id" : promo_id,
        "user_id" : user_id,
        "action": common_constant.ACTION_EDIT,
        "parameters": {
            "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
        }
    }
    await promo_data.update_strategy_disable_simulation_flag(promo_id,is_simulation_disabled=True)

    status,error_message = await invoke_opt_endpoint(
        action=promo_constants.ACTION_EDIT,
        endpoint=environment.REFRESH_API_URL,
        req_data=req_data,
        message_set={"failure": f"Failed to refresh the affected {client_configuration_constants.PROMO_IDENTIFIER_PLURAL}",
                    "success": ""}
    )
    
    if not status:
        logger.error(error_message)
        raise OptRefreshApiException



async def get_all_promo_details(**kwargs):
    product_hierarchies = {
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["l0_ids"]: kwargs["request"][
            "l0_ids"
        ],
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["l1_ids"]: kwargs["request"][
            "l1_ids"
        ],
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["l2_ids"]: kwargs["request"][
            "l2_ids"
        ],
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["l3_ids"]: kwargs["request"][
            "l3_ids"
        ],
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["l4_ids"]: kwargs["request"][
            "l4_ids"
        ],
        promo_constants.PRODUCT_HIERARCHY_CID_MAPPING["brand"]: kwargs["request"][
            "brand"
        ],
    }

    store_hierarchies = {
        0: kwargs["request"]["s0_ids"],
        1: kwargs["request"]["s1_ids"],
        2: kwargs["request"]["s2_ids"],
    }

    # Forming the dictionary for dates_range.
    dates_range = dict()
    request_data = kwargs.get("request", {}) or None

    if request_data.get("start_date") and request_data.get("end_date"):
        dates_range = dict(
            start_date=request_data.get("start_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
            end_date=request_data.get("end_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
        )

    # The below function will be used to form the WHERE & AND condition for our query.
    promos_details = await promo_data.get_all_promo_workbench(
        product_hierarchies,
        store_hierarchies,
        dates_range,
        request_data["screen_type"],
        request_data.get("promo_ids"),
    )

    return promo_utils.format_promo_details_response(
        promos_details, request_data["screen_type"]
    )


async def promo_downloads_service(**kwargs):
    report_name = kwargs["request"]["report_name"]
    report_type = kwargs["request"]["report_type"]
    report_file_name = kwargs["request"]["report_file_name"]
    promo_id = kwargs["request"]["promo_id"]
    promo_name = kwargs["request"]["promo_name"]
    aggregation = kwargs["request"]["aggregation"]
    aggregation_level = kwargs["request"]["aggregation_level"]
    user_id = kwargs["user_id"]
    
    product_hierarchies = kwargs["request"]["product_hierarchies"]
    store_hierarchies = kwargs["request"]["store_hierarchies"]
    target_currency_id = kwargs["request"]["target_currency_id"]
    show_partially_overlapping_events = kwargs["request"]["show_partially_overlapping_events"]

    dates_range = dict()
    promo_date_details = kwargs.get("request", {}) or None

    if report_name != "promo_simulator_report_extensive_data" and promo_date_details:
        dates_range = dict(
            start_date=promo_date_details.get("start_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
            end_date=promo_date_details.get("end_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
        )
    await promo_data.promo_downloads_data(
        product_hierarchies,
        store_hierarchies,
        show_partially_overlapping_events,
        dates_range,
        promo_id,
        aggregation,
        promo_name,
        aggregation_level,
        report_name=report_name,
        report_type=report_type,
        report_file_name=report_file_name,
        user_id=user_id,
        target_currency_id=target_currency_id
    )


async def get_promo_results(request_payload:promo_models.PromoResults):

    dates_range = dict()
    if request_payload.start_date and request_payload.end_date:
        dates_range = dict(
            start_date=request_payload.start_date.strftime(common_constant.PYTHON_DATE_FORMAT),
            end_date=request_payload.end_date.strftime(common_constant.PYTHON_DATE_FORMAT)
        )

    result = await promo_data.get_promo_results_data(
        product_hierarchies=request_payload.product_hierarchies,
        store_hierarchies=request_payload.store_hierarchies,
        date_range=dates_range,
        view_by_options=request_payload.view_by_options,
        promo_ids=request_payload.promo_ids,
        event_ids=request_payload.event_ids,
        target_currency_id=request_payload.target_currency_id,
        show_partially_overlapping_events=request_payload.show_partially_overlapping_events
    )
    return result


async def get_valid_discounting_level_service(request_payload: promo_models.ValidDiscountingLevel):
    return await promo_data.get_valid_discounting_level_data(request_payload)

async def get_discount_levels_service(promo_id: int):
    return await promo_data.get_discount_levels_data(promo_id)


async def get_promo_status_types_service(screen_type):
    return await promo_data.get_promo_status_types_data(screen_type=screen_type)


async def tiles_service(request_payload: promo_models.Tiles):
    promo_ids = request_payload.promo_ids
    event_ids = request_payload.event_ids
    screen_type = request_payload.screen_type.value if request_payload.screen_type else None 
    # Forming the dictionary for dates_range.
    dates_range = dict()

    if request_payload.start_date and request_payload.end_date:
        dates_range = dict(
            start_date=request_payload.start_date.strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
            end_date=request_payload.end_date.strftime(
                common_constant.PYTHON_DATE_FORMAT
            )
        )

    # The below function will be used to form the WHERE & AND condition for our query.
    tiles_details = await promo_data.tiles_data(
        request_payload.product_hierarchies, request_payload.store_hierarchies, dates_range, promo_ids, event_ids, screen_type, request_payload.target_currency_id, request_payload.show_partially_overlapping_events
    )

    return tiles_details


async def calculate_gross_margin_service(**kwargs):
    dates_range = dict()
    promo_date_details = kwargs.get("request", {}) or None
    metrics = kwargs.get("request", {}).get("metrics")

    if promo_date_details:
        dates_range = {
            "start_date": promo_date_details.get("start_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
            "end_date": promo_date_details.get("end_date", None).strftime(
                common_constant.PYTHON_DATE_FORMAT
            ),
        }
    return await promo_data.calculate_metrics_data(metrics, dates_range)

async def get_valid_offers_service(promo_id, simulator_action):
    return await promo_data.get_offers_data(
        promo_id=promo_id, simulator_action=simulator_action
    )

async def get_ly_targets_service(**kwargs):
    return await promo_data.get_ly_targets_data(**kwargs)


async def call_resimulation_service(request_object):
    logger.info(f"OPT Resimulate Request Payload:: {request_object}")
    logger.info(environment.RESIMULATE_API_URL)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=environment.RESIMULATE_API_URL, json=request_object
            )
    except httpx.HTTPStatusError as e:
        logger.info(
            f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
        )
        response = None
    except httpx.RequestError as e:
        logger.info(f"Request error occurred: {str(e)}")
        response = None
    except Exception as e:
        logger.info(f"An unexpected error occurred: {str(e)}")
        response = None

    logger.info(f"Response from OPT Resimulate API:: {response}")
    if response is None or response.status_code != status.HTTP_200_OK:
        promo_ids = [promo["promo_id"] for promo in request_object["promotions"]]

        request_object.update({"success_promo_ids": [], "failure_promo_ids": promo_ids})
        del request_object["promotions"]

        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=request_object.get("action_log_id"),
            promo_ids=promo_ids,
            action_log_status=-1,
            is_processing_status=0,
            auto_resimulated_flag=-1,
            source=request_object.get("source"),
            user_id=request_object.get("user_id"),
        )

        logger.info(f"BE Server Callback Request Payload:: {request_object}")
        await promo_utils.call_server_callback(request_object)
        return False, response.text if response else NO_RESPONSE

    # logger.info("Success response from opt")
    return True, "Success"

async def call_resimulation_service_v3(request_object):
    logger.info(f"OPT Resimulate Request Payload:: {request_object}")
    logger.info(f"OPT Resimulate API URL:: {environment.RESIMULATE_API_URL}")
    async with httpx.AsyncClient() as client:
        response = await client.post(
            url=environment.RESIMULATE_API_URL, json=request_object
        )
    logger.info(f"Response from OPT Resimulate API:: {response.text}")
    if response.status_code != status.HTTP_200_OK:
        raise SimulationApiCallFailedException



async def call_optimisation_service(request_object):
    logger.info(f"OPT Optimise Request Payload:: {request_object}")
    print(request_object)
    print(environment.OPTIMISE_API_URL)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=environment.OPTIMISE_API_URL, json=request_object
            )
            print(response)
    except httpx.HTTPStatusError as e:
        logger.info(
            f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
        )
        response = None
    except httpx.RequestError as e:
        logger.info(f"Request error occurred: {str(e)}")
        response = None
    except Exception as e:
        logger.info(f"An unexpected error occurred: {str(e)}")
        response = None

    if response is None or response.status_code != status.HTTP_200_OK:
        request_object = {
            "guid": request_object["guid"],
            "user_id": request_object["user_id"],
            "action_log_id": request_object["action_log_id"],
            "action": "optimise",
            "promo_id": request_object["promo_id"],
            "scenario_id": [0],
            "status": 400,
            "message": "Optimisation failed. Reason - {} {}".format(
                response.status_code if response else NO_RESPONSE,
                response.reason_phrase if response else "No Reason Phrase",
            ),
        }
        logger.info(f"BE Server Callback Request Payload:: {request_object}")
        await promo_utils.call_server_callback(request_object)
        return False, response.text if response else NO_RESPONSE

    # logger.info("Success response from opt")
    return True, "Success"

@log_action_decorator(
    get_promo_ids_func=lambda i: i.promo_ids,
    action_name=promo_constants.ACTION_EXECUTION_APPROVED,
    screen_name=promo_constants.SCREEN_DECISION_DASHBOARD
)
async def execution_approve_promotion_service_v3(
    request_payload: promo_models.ExecutionApprovePromosRequest,
    user_id: int
):
    return await promo_data.execution_approve_promotion_data_v3(
        request_payload,
        user_id
    )


async def get_valid_offer_types_service(
    request_payload: promo_models.GetOfferTypesByPriority
):
    return await promo_data.get_offer_types_data(
        request_payload=request_payload
    )


async def get_tier_valid_offer_types_service():
    return await promo_data.get_tier_valid_offer_types_data()


async def update_scenario_name_service(**kwargs):
    await promo_data.update_scenario_name_data(**kwargs)

@log_action_decorator(
    get_promo_ids_func=lambda i: i.promo_ids,
    action_name=promo_constants.ACTION_WITHDRAW,
    screen_name=promo_constants.SCREEN_DECISION_DASHBOARD
)
async def withdraw_promotion_service_v3(
    request_payload: promo_models.WithdrawPromosRequest,
    user_id: int
):
    await promo_data.withdraw_promotion_data_v3(
        request_payload,
        user_id
    )


async def delete_promotion_service_v3(
    request_payload: promo_models.DeletePromosRequest,
    user_id: int
):
    await promo_data.delete_promotion_data_v3(
        request_payload,
        user_id
    )


@log_action_decorator(
    get_promo_ids_func=lambda item: [i.promo_id for i in item.promos],
    action_name= promo_constants.ACTION_COPY,
    screen_name= promo_constants.SCREEN_WORKBENCH
)
async def copy_promotion_service(
    copy_promos_request: promo_models.CopyPromosRequest,
    user_id: int
):
    data = await promo_data.copy_promotion_data(
        copy_promos_request,user_id
    )

    await create_notification_for_copy_promo(
        new_promo_ids=[item['value'] for item in data],
        promo_ids=[item.promo_id for item in copy_promos_request.promos],
        user_id=user_id
    )
    return data


async def create_notification_for_copy_promo(
    new_promo_ids: list[int],
    promo_ids: list[int],
    user_id: int
):

    if new_promo_ids:
        status = True
        header_text = f"Copy {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} Completed"

        if len(new_promo_ids) > 1:
            identifier = f"{len(new_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            message = f"Copy results are ready for {identifier}"
        else:
            promo_name = await get_promo_name_service(new_promo_ids[0])
            new_promo_name = await get_promo_name_service(new_promo_ids[0])
            message = f"Copy successful for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}. New {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} created with name: {new_promo_name}."
            identifier = f"{new_promo_name}."

    else:
        status = False
        header_text = f"Copy {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} Failed"
        if len(promo_ids) > 1:
            identifier = f"{len(promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
            message = f"Copy failed for {identifier}."
        else:
            promo_name = await get_promo_name_service(promo_ids[0])
            identifier = promo_name
            message = f"Copy failed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {identifier}."


    await insert_notification(
        request = {},
        user_id=user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=promo_constants.ACTION_COPY,
        message=message,
        promo_id=None,
        status=status,
        identifier=identifier,
        header_text=header_text,
        promo_ids=new_promo_ids if new_promo_ids else promo_ids
    )

async def get_promo_step0_basics(promo_id: int):
    return await promo_data.get_promo_step0_basics(promo_id)

async def get_promo_step1_basics(promo_id: int):
    return await promo_data.get_promo_step1_basics(promo_id)

async def get_promo_step1_details(promo_id: int):
    return await promo_data.get_promo_step1_details(promo_id)

async def get_promo_step1_exclusions(promo_id: int):
    return await promo_data.get_promo_step1_exclusions(promo_id)

async def get_promo_step2_basics(promo: int):
    return await promo_data.get_promo_step2_basics(promo)

async def get_promo_step3_basics(promo_id: int):
    return await promo_data.fetch_step3_basics_data(promo_id)

async def get_promo_step3_optimisation_details(promo_id: int):
    return await promo_data.get_promo_step3_optimisation_details(promo_id)

async def get_promo_step3_simulation_results(request_payload: promo_models.GetPromoStep3SimulationResults):
    return await promo_data.get_promo_step3_simulation_results(request_payload)

async def get_bmsm_offer_types_service():
    return await promo_data.get_bmsm_offer_types_data()


async def get_tiers_service(**kwargs):
    return await promo_data.get_tiers_data(**kwargs)


async def delete_tier_service(**kwargs):
    return await promo_data.delete_tier_data(**kwargs)


async def tier_management_service(
    request_payload: promo_models.TierManagement,
):
    return await promo_data.tier_management_data(
        request_payload=request_payload
    )


async def fetch_eligible_promos_service(**kwargs):
    return await promo_data.fetch_eligible_promos(**kwargs)


async def resimulate_offers_service(
    action_log_id, guid, user_id, background_task, eligible_promo_id_list, promo_id_list
):
    _message = "Simulation in progress "
    background_task.add_task(
        promo_data.resimulate_offers_data,
        action_log_id=action_log_id,
        guid=guid,
        user_id=user_id,
        eligible_promo_id_list=eligible_promo_id_list,
    )
    if eligible_promo_id_list == promo_id_list:
        if len(eligible_promo_id_list) == 1:
            promo_name = await get_promo_name_service(eligible_promo_id_list[0])
            _message += f"for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}: {promo_name}"
        else:
            _message += f"for {len(promo_id_list)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}:"
    elif len(eligible_promo_id_list) < len(promo_id_list):
        if len(eligible_promo_id_list) == 1:
            promo_name = await get_promo_name_service(eligible_promo_id_list[0])
            _message += f"for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}. {len(promo_id_list) - len(eligible_promo_id_list)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} are not eligible for simulation"
        else:
            _message += (f"for {len(eligible_promo_id_list)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}. {len(promo_id_list) - len(eligible_promo_id_list)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} are not eligible for simulation")
    _status = status.HTTP_200_OK

    return _message, _status


async def edit_promos_info_service(**kwargs):
    await promo_data.edit_promos_data(**kwargs)


async def get_simulation_results_details_service(**kwargs):
    return await promo_data.get_simulation_results_details_data(**kwargs)

async def check_promo_background_process_status_service(
    promo_ids: list[int], processing_action, screen_type
):
    processing_promos = await promo_data.check_promo_background_process_status_data(
        promo_ids, processing_action, screen_type
    )

    if not processing_promos:
        return {
            "is_processing": False,
            "is_valid": True,
            "message": None,
            "data": None,
        }

    message = generate_processing_message(processing_promos)
    data = format_promo_details(processing_promos)

    return {
        "is_processing": True,
        "is_valid": False,
        "message": message,
        "data": data,
    }

def generate_processing_message(processing_promos):
    if len(processing_promos) > 1:
        return generate_multiple_promos_message(processing_promos)
    return generate_single_promo_message(processing_promos[0])

def generate_multiple_promos_message(processing_promos):
    processing_actions = {row["processing_action"] for row in processing_promos}
    promo_names = {row["promo_name"] for row in processing_promos}

    if len(processing_actions) > 1:
        return (
            f"Multiple actions are in progress across different {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            if len(promo_names) > 1
            else f"Multiple actions are in progress for {promo_names.pop()}."
        )
    return f"{processing_actions.pop().capitalize()} is in progress on multiple {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."

def generate_single_promo_message(promo):
    promo_name = promo["promo_name"]
    action = promo["processing_action"].lower()

    action_messages = {
        "resimulate": f"Simulation is in progress for {promo_name}.",
        "optimise": f"Optimization is in progress for {promo_name}.",
        "override": f"Override is in progress for {promo_name}.",
        "refresh": f"{promo_name} is refreshing in background.",
        "copy": f"Copy operation is in progress for {promo_name}.",
        "auto-simulate": f"Auto-simulation is in progress for {promo_name}.",
        "finalize": f"Finalization is in progress for {promo_name}.",
        "approve": f"Approval is in progress for {promo_name}.",
        "delete": f"Deletion is in progress for {promo_name}.",
        "withdraw": f"Withdrawal is in progress for {promo_name}.",
        "execution_approved": f"Execution approval is in progress for {promo_name}.",
        "edit": f"Editing is in progress for {promo_name}.",
        "create": f"Creation is in progress for {promo_name}.",
        "inline_edit": f"Inline editing is in progress for {promo_name}.",
        "scenario_name_edit": f"Scenario name editing is in progress for {promo_name}.",
    }
    return action_messages.get(action, f"{action.capitalize()} is in progress for {promo_name}.")

def format_promo_details(processing_promos):
    return [
        {
            "promo_id": row["promo_id"],
            "promo_name": row["promo_name"],
            "screen": row["screen"],
            "processing_action": row["processing_action"],
        }
        for row in processing_promos
    ]


async def get_view_by_service(type, screen_type):
    return await promo_data.get_view_by_data(
        screen_type=screen_type, type=type
    )


async def update_is_processing_service(**kwargs):
    await promo_data.update_is_processing_data(**kwargs)


async def update_is_auto_resimulated_service(**kwargs):
    await promo_data.update_is_auto_resimulated_data(**kwargs)


async def insert_action_log_service(**kwargs):
    return await promo_data.insert_action_log_data(**kwargs)


async def update_action_log_service(**kwargs):
    await promo_data.update_action_log_data(**kwargs)


async def get_priority_number_service(
    request_payload: promo_models.GetPriorityNumber
):
    return await promo_data.get_priority_number_data(
        request_payload=request_payload
    )


async def get_exclusions_from_file_service(file: UploadFile):

    product_exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.EXCLUSION_UPLOAD_CONFIGURATION
    )
    
    uploaded_data = await promo_data.get_exclusions_data_from_file(
        file, product_exclusion_upload_configuration
    )
    validated_data = await promo_data.validate_exclusion_upload_data(uploaded_data, product_exclusion_upload_configuration)
    return validated_data


async def is_valid_to_finalize(request_data: promo_models.ToFinalizeModel):
    return await promo_data.is_valid_to_finalize(request_data)


async def get_exmd_template_id_service():
    return await promo_data.get_exmd_template_id_data()


async def get_exmd_price_filter_service(**kwargs):
    return await promo_data.get_exmd_price_filter_data(**kwargs)


async def get_exmd_target_folder_service(**kwargs):
    return await promo_data.get_exmd_target_folder_data(**kwargs)


async def get_exmd_sfcc_ats_check_service():
    return await promo_data.get_exmd_sfcc_ats_check_data()


async def get_exmd_sfcc_dropship_options_service():
    return await promo_data.get_exmd_sfcc_dropship_options_data()


async def save_exmd_service(**kwargs):
    await promo_data.save_exmd_data(**kwargs)


async def get_exmd_promo_service(**kwargs):
    return await promo_data.get_exmd_promo_data(**kwargs)


async def get_promo_id_by_scenario_id_service(scenario_id: int):
    return await promo_data.get_promo_id_by_scenario_id_data(scenario_id)


async def get_promo_name_service(promo_id: int):
    return await promo_data.get_promo_name_data(promo_id)


async def get_previously_synced_promo_id_service(promo_id_list: list):
    return await promo_data.get_previously_synced_promo_id_data(promo_id_list)

async def get_affected_promos_for_override(override_request: promo_models.OverrideForecast)->list[int]:
    affected_promos = []
    if (await promo_utils.is_finalized_scenario(override_request.promo_id,override_request.scenario_id)):
        affected_promos = await get_stacked_offers_of_promo(override_request.promo_id)
    print(affected_promos,"affected_promos")
    return affected_promos


async def override_forecast_for_a_promo_service_caller(
    override_request: promo_models.OverrideForecast,
    user_id: int
):
    override_action_log_id,offer_refresh_log_action_id = None,None
    try:
        override_request_dict = override_request.model_dump()
        override_action_log_id = await promo_utils.log_action(
            override_request_dict,
            [override_request.promo_id],
            promo_constants.ACTION_OVERRIDE,
            override_request.screen_type,
            user_id
        )
        affected_promos = await get_affected_promos_for_override(override_request)
        if affected_promos:
            offer_refresh_log_action_id = await promo_utils.log_action(
                override_request_dict,
                affected_promos,
                promo_constants.ACTION_REFRESH_METRICS,
                override_request.screen_type,
                user_id
            )

        promo_name = await get_promo_name_service(override_request.promo_id)
        await override_forecast_for_a_promo_service(override_request, promo_name, user_id)

    except ConflictException as err:
        failure_message = f"{err}"
        print(repr(err))
    except Exception as err:
        failure_message = f"Something went wrong when overriding {client_configuration_constants.PROMO_IDENTIFIER_PRIMARY} {promo_name}"
        print(repr(err))
    else:
        return
    finally:
        await update_is_processing_for_override_action(
            override_action_log_id,
            offer_refresh_log_action_id,
            override_request,
            affected_promos,
            user_id
        )
        

    await insert_notification(
        request={"message": failure_message,"user_id": user_id},
        action=promo_constants.ACTION_OVERRIDE,
        identifier=promo_name,
        module=NotificationModules.PROMOTIONS.value,
        user_id=user_id,
        promo_id=override_request.promo_id,
        status=False,
        message=failure_message,
        header_text= NotificationHeaderText.OVERRIDE.value,
    ) 

async def update_is_processing_for_override_action(
    override_action_log_id: int | None,
    offer_refresh_action_log_id: int | None,
    override_request: promo_models.OverrideForecast,
    affected_promos: list[int],
    user_id: int
):
    await promo_utils.update_promo_log(
            override_action_log_id,
            [override_request.promo_id],
            0,
            user_id,
        ) if override_action_log_id else None
    
    if affected_promos:
        await promo_utils.update_promo_log(
                offer_refresh_action_log_id,
                affected_promos,
                0,
                user_id,
            ) if offer_refresh_action_log_id else None

async def override_forecast_for_a_promo_service(
    override_request: promo_models.OverrideForecast, promo_name: str, user_id: int
):
    affected_stacked_promos = await promo_data.override_forecast_for_a_promo_data(override_request, user_id)
    print(affected_stacked_promos,"affected promos from sql function")
    await notify_user_regarding_override_and_affected_stacked_promos(
        override_request,
        promo_name,
        affected_stacked_promos,
        user_id,
    )

async def notify_user_regarding_override_and_affected_stacked_promos(
    override_request: promo_models.OverrideForecast,
    promo_name: str,
    affected_stacked_promos: list[int],
    user_id: int
):

    override_successful_message = f"{promo_name} has been successfully updated with overridden forecast."
    await insert_notification(
        request={"message": override_successful_message,"user_id": user_id},
        action=promo_constants.ACTION_OVERRIDE,
        identifier = promo_name,
        module = NotificationModules.PROMOTIONS.value,
        user_id = user_id,
        promo_id = override_request.promo_id,
        status = True,
        message = override_successful_message,
        header_text = NotificationHeaderText.OVERRIDE.value,
    )

    print(affected_stacked_promos)
    if not affected_stacked_promos:
        return

    identifier = (
        f"{len(affected_stacked_promos)} affected "
        f"{f'{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}' if len(affected_stacked_promos)>1 else f'{client_configuration_constants.PROMO_IDENTIFIER_ALIAS}'}"
    )

    affected_promos_message = (
        f"The forecast has been updated for {identifier}."
    )
    await insert_notification(
        request= {"message": affected_promos_message,"user_id": user_id},
        action=promo_constants.ACTION_REFRESH_METRICS,
        identifier = identifier,
        module= NotificationModules.PROMOTIONS.value,
        user_id = user_id,
        promo_id = None,
        status = True,
        message = affected_promos_message,
        promo_ids = affected_stacked_promos,
        header_text=NotificationHeaderText.OFFER_REFRESH.value,
    )

async def check_if_optimisation_is_running(override_request: promo_models.OverrideForecast):
    if override_request.scenario_id != 0:
        return

    is_processing_info = (
        await check_promo_background_process_status_service(
            promo_ids=[override_request.promo_id],
            processing_action="override_forecast",
            screen_type="step3",
        )
    )
    print(is_processing_info)

    if is_processing_info["is_processing"]:
        raise ConflictException(f"{is_processing_info['message']}")


async def get_override_forecast_for_a_promo_service(
    override_request: promo_models.OverrideForecast,
):
    data = await promo_data.get_override_forecast_for_a_promo_data(override_request)
    data["payload"] = override_request.model_dump()
    return data


async def get_override_reason_service():
    return await promo_data.get_override_reason_data()

async def set_default_for_scenario(set_defeault_request: promo_models.SetDefaultForScenario, user_id: int):
    return await promo_data.set_default_for_scenario(set_defeault_request, user_id)

async def get_promo_stacked_offers(promo_id: int):
    empty_response = {"stackable": [],"non_stackable": [],"no_of_stacked_promos": 0}
    data =  await promo_data.get_promo_stacked_offers(promo_id)
    return empty_response | data 


async def invoke_opt_endpoint(
    action, endpoint, req_data: promo_models.InvokeOptModel, message_set: promo_models.MessageSet
) -> tuple[bool, str]:
    print(f"Action - {action}")
    try:
        print(f"Request Data - {req_data}")
        print('Endpoint - ', endpoint)
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=endpoint, json=req_data
            )
            print(response)
    except httpx.HTTPStatusError as e:
        logger.info(
            f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
        )
        response = None
    except httpx.RequestError as e:
        logger.info(f"Request error occurred: {str(e)}")
        response = None
    except Exception as e:
        logger.info(f"An unexpected error occurred: {str(e)}")
        response = None

    if response is None or response.status_code != status.HTTP_200_OK:
        print("Opt Callback Failed")
        request_object = {
            "guid": req_data.get("guid"),
            "user_id": req_data.get("user_id"),
            "action_log_id": req_data.get("action_log_id"),
            "action": action,
            "promo_id": req_data.get("promo_ids"),
            "scenario_id": [],
            "status": 400,
            "message": "{}. Reason - {} {}".format(
                message_set.get('failure'),
                response.status_code if response else NO_RESPONSE,
                response.reason_phrase if response else "No Reason Phrase",
            ),
        }
        logger.info(f"BE Server Callback Request Payload:: {request_object}")
        print(f"BE Server Callback Request Payload:: {request_object}")
        await promo_utils.call_server_callback(request_object)
        return False, message_set.get('failure')

    # logger.info("Success response from opt")
    return True, message_set.get('success')


async def is_finalised_or_execution_approved_promos_present(promo_ids: list[int]) -> bool: 
    return await promo_data.is_finalised_or_execution_approved_promos_present(promo_ids)


async def send_notification_for_promo_delete(user_id, promo_ids, notification_message, status=True):
    await notify.create_notification(
                message = notification_message,
                module = "promotions",
                action = promo_constants.ACTION_DELETE,
                user_id=str(user_id),
                promo_ids=promo_ids,
                status=status,
                header_text=f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} delete"
            )

async def check_if_step3_is_disabled(promo_id: int, action: str):
    is_step3_disabled = await promo_data.check_if_step3_is_disabled(promo_id)
    if is_step3_disabled:
        raise ConflictException(
            f"Edit in progress for this {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}. {action} is temporarily disabled."
        )
    

async def get_stacked_offers_of_promo(promo_id: int)->list[int]:
    return await promo_data.get_stacked_offers_of_promo(promo_id)


async def create_placeholder_promotion_v3(
    request_payload: promo_models.CreatePlaceholderPromoRequest,
    user_id: int
):
    if (
        await promo_data.check_promo_existence(
            promo_name = request_payload.promo_name,
            start_date = request_payload.start_date,
            end_date = request_payload.end_date
        )
    ):
        raise PromoAlreadyExistsException(
            f"{client_configuration_constants.PROMO_IDENTIFIER_STANDARD.capitalize()} with name {request_payload.promo_name} already exists." 
        )
    data = await promo_data.create_placeholder_promo_v3(
        request_payload,
        user_id
    )
    return data

@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_EDIT,
    screen_name=promo_constants.SCREEN_STEP0
)
async def update_placeholder_promotion_v3(
    request_payload: promo_models.UpdatePlaceholderPromoRequest,
    user_id: int
):
    data = await promo_data.update_placeholder_promo_v3(
        request_payload,
        user_id
    )

    return data



@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name = promo_constants.ACTION_CREATE,
    screen_name= promo_constants.SCREEN_STEP1
)
async def insert_promo_step1_data(
    request_payload: promo_models.InsertPromoStep1Request,
    user_id: int
) -> None:
    await promo_data.save_step1_details_data_v3(
        request_payload,
        user_id
    )

@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name = promo_constants.ACTION_EDIT,
    screen_name= promo_constants.SCREEN_STEP1
)
async def update_promo_step1_data(
    request_payload: promo_models.UpdatePromoStep1Request,
    user_id: int
):

    await promo_data.update_promo_step1_data(
        request_payload,
        user_id
    )

    await handle_refresh_metrics_for_other_affected_promos(
        request_payload.promo_id,
        user_id 
    )

    
@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_EDIT,
    screen_name=promo_constants.SCREEN_STEP0
)
async def update_promo_step0_data(
    request_payload: promo_models.UpdatePromoStep0Request,
    user_id: int
)-> None:
    await promo_data.update_promo_step0_data(
        request_payload,
        user_id
    )
    await handle_refresh_metrics_for_other_affected_promos(
        request_payload.promo_id,
        user_id 
    )


@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_EDIT,
    screen_name=promo_constants.SCREEN_STEP2
)
async def update_promo_step2_data(
    reqeust_payload: promo_models.UpdatePromoStep2Request,
    user_id: int
):

    await promo_data.update_promo_step2_data(
        reqeust_payload,
        user_id
    )

    await handle_refresh_metrics_for_other_affected_promos(
        reqeust_payload.promo_id,
        user_id
    )
    
@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_SIMULATE,
    screen_name=promo_constants.SCREEN_STEP3,
    update_task_completion=False
)
async def simulate_promo_caller(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int,
    action_log_id: int
):
    await check_if_step3_is_disabled(
        request_payload.promo_id,
        promo_constants.ACTION_SIMULATE.capitalize()
    )
    return await simulate_promo_v3(request_payload, user_id, action_log_id)

def process_discounts_data(reqeust_payload: promo_models.SimulatePromoRequest,user_id: int):
    for i, discount_data in enumerate(reqeust_payload.discounts_data):
        reqeust_payload.discounts_data[i]._scenario_data = get_formatted_scenario_data(discount_data,user_id)

    reqeust_payload._updated_scenario_ids = identify_updated_scenario_ids(reqeust_payload)

def get_formatted_scenario_data(discount_data: promo_models.DiscountsData,user_id: int):
    formatted_scenario_data = {}
    for scenario_data in discount_data.scenario_data:
        if scenario_data.scenario_order_id == 0:
            continue
        
        scenario_data.offer_value = promo_utils.get_offer_description(scenario_data)
        formatted_scenario_data[scenario_data.scenario_order_id] = scenario_data
        if scenario_data.updated:
            scenario_data.updated_at = datetime.datetime.now(datetime.UTC)
            scenario_data.updated_by = user_id
        if scenario_data.created_at is None: 
            scenario_data.created_at = datetime.datetime.now(datetime.UTC)

    return formatted_scenario_data

def identify_updated_scenario_ids(request_payload: promo_models.SimulatePromoRequest):
    updated_scenario_ids = set()
    for discount_data in request_payload.discounts_data:
        for scenario_data in discount_data.scenario_data:
            if scenario_data.updated:
                updated_scenario_ids.add(scenario_data.scenario_id)
    return list(updated_scenario_ids)


async def simulate_promo_v3(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int,
    action_log_id: int
):
    process_discounts_data(request_payload,user_id)

    if not await validate_upto_discount_values(request_payload,user_id):
        raise UptoDiscountMismatchError()

    await validate_missing_discount_values(
        request_payload,
        user_id
    )

    await update_discount_values(
        request_payload,
        user_id
    )

    await call_simulation_service(request_payload,action_log_id,user_id)
    await promo_data.update_promo_step3_data(request_payload,user_id)

async def call_simulation_service(
    request_payload: promo_models.SimulatePromoRequest,
    action_log_id: int,
    user_id: int
):
    clear_finalised_data = await promo_data.check_edit_of_last_approved_scenario_v3(
        request_payload
    )
    promotions = [
        {
            "promo_id": request_payload.promo_id,
            "scenario_id": await promo_data.get_promo_scenario_ids(request_payload.promo_id,only_updated=True)
        }
    ]
    resimulate_opt_payload = {
        "action_log_id": action_log_id,
        "user_id": user_id,
        "source": promo_constants.SCREEN_STEP3,
        "action": promo_constants.SIMULATE,
        "promotions": promotions,
        "approved_scenario_sim_opt": clear_finalised_data,
        "parameters": {
            "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
        }
   
    }

    await call_resimulation_service_v3(resimulate_opt_payload)

async def update_discount_values(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int
):
    new_scenarios_insertion_query = ""
    if request_payload.new_scenario_details:
        new_scenarios_insertion_query = get_scenarios_insertion_query(
            request_payload.promo_id,
            request_payload.new_scenario_details,
            user_id
        )
    await promo_data.update_discount_values(request_payload,user_id,new_scenarios_insertion_query)

async def validate_missing_discount_values(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int
):
    await promo_data.validate_missing_discount_values(
        request_payload,
        user_id,
    )

async def validate_upto_discount_values(
    request_payload: promo_models.SimulatePromoRequest,
    user_id : int
):  
    scenario_data = promo_data.get_processed_data_for_upto_discount(request_payload)
    row_ids_to_ignore = [row.row_id for row in request_payload.discounts_data]
    scenario_db_data = await promo_data.get_db_discount_data_for_scenarios(request_payload,row_ids_to_ignore, user_id)
    scenario_db_data = scenario_db_data[0]["result"]
    if scenario_db_data is None:
        scenario_db_data={}
    return await promo_data.validate_upto_discount_values(scenario_data,scenario_db_data) 

async def approve_scenario_v3(
    request_payload: promo_models.ApproveScenarioRequest,
    user_id: int,
    is_finalised_promo: bool,
    action_log_id: int
):

    await promo_data.approve_scenario_v3(
        request_payload,
        user_id
    )
    if not is_finalised_promo:
        return
    try:
        # invoke opt end point to delete finalised data and refresh metrics of mapped promos
        refresh_finalised_data_opt_call_req: promo_models.InvokeOptModel = {
            "action_log_id": action_log_id,
            "promo_id": request_payload.promo_id,
            "scenario_id": request_payload.scenario_id,
            "user_id": user_id,
            "action": promo_constants.ACTION_APPROVE_SCENARIO,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }

        message_set: promo_models.MessageSet = {
            "success": "Scenario approved successfully",
            "failure": "Scenario approval failed. Please try again later"
        }
        res_status, response_text = await invoke_opt_endpoint(
            action=promo_constants.ACTION_APPROVE_SCENARIO,
            endpoint=environment.REFRESH_API_URL,
            req_data=refresh_finalised_data_opt_call_req,
            message_set=message_set
            )
        print(res_status, response_text)
        if not res_status:
            raise BusinessLogicException(response_text)
    except Exception as e:
        raise BusinessLogicException("Scenario approval failed. Please try again later") from e
    
@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_OPTIMISE,
    screen_name=promo_constants.SCREEN_STEP3
)
async def optimise_promo_caller(
    request_payload: promo_models.OptimisePromoRequest,
    user_id: int,
    action_log_id: int
):
    await check_if_step3_is_disabled(
        request_payload.promo_id,
        "Optimization"
    )
    return await optimise_promo_v3(request_payload, user_id, action_log_id)

async def optimise_promo_v3(
    request_payload: promo_models.OptimisePromoRequest,
    user_id: int,
    action_log_id: int
):
    await promo_data.optimisation_flow_data_v3(
        request_payload,
        user_id,
    )

    last_finalised_scenario_id = await promo_data.get_last_finalised_scenario_id(
        request_payload.promo_id
    )

    opt_payload = {
        "user_id": user_id,
        "action_log_id": action_log_id,
        "action": promo_constants.ACTION_OPTIMISE,
        "promo_id": request_payload.promo_id,
        "scenario_id": 0,
        "approved_scenario_sim_opt": last_finalised_scenario_id == 0,
        "parameters": {
            "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
        }
    }
    return await call_optimisation_service(opt_payload)

@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_id,
    action_name=promo_constants.ACTION_OPTIMISE,
    screen_name=promo_constants.SCREEN_STEP3
)
async def reoptimise_promo(
    request_payload: promo_models.OptimisePromoRequest,
    user_id: int,
    action_log_id: int  
):
    await check_if_step3_is_disabled(
        request_payload.promo_id,
        "Optimization"
    )
    await promo_data.delete_data_on_reoptimisation(
        request_payload.promo_id
    )
    return await optimise_promo_v3(
        request_payload,
        user_id,
        action_log_id
    )

@log_action_decorator(
    get_promo_ids_func=lambda item: item.promo_ids,
    action_name=promo_constants.ACTION_FINALIZE,
    screen_name=promo_constants.SCREEN_WORKBENCH,
    update_task_completion=False
)
async def finalise_promo_v3(
    finalise_promo_request: promo_models.FinalisePromoRequest,  
    user_id: int,
    action_log_id: int
):
    await validate_event_lock(
        finalise_promo_request.promo_ids,
        ConfigModuleEnum.EVENT,
        ConfigKeyEnum.ALLOW_PROMO_FINALIZE_AFTER_EVENT_LOCK,
        f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} under a locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be finalized"
    )

    finalise_opt_call_req_obj: promo_models.InvokeOptModel = {
        "action_log_id": action_log_id,
        "promo_ids": [{"promo_id": promo_id} for promo_id in finalise_promo_request.promo_ids],
        "user_id": user_id,
        "action": promo_constants.ACTION_FINALIZE,
        "parameters": {
            "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
        }
    }

    endpoint = environment.REFRESH_API_URL
    message_set: promo_models.MessageSet = {
        "success": f"{client_configuration_constants.PROMO_IDENTIFIER_STANDARD.capitalize()} finalized successfully",
        "failure": "Finalise operation failed. Please try again later"
    }
    status, response_text = await invoke_opt_endpoint(action=promo_constants.ACTION_FINALIZE, endpoint=endpoint, req_data=finalise_opt_call_req_obj, message_set=message_set)

    if not status:
        raise OptCallFailedException(response_text)


@log_action_decorator(
    get_promo_ids_func=lambda i: i.promo_ids,
    action_name=promo_constants.ACTION_DELETE,
    screen_name=promo_constants.SCREEN_WORKBENCH,
    update_task_completion=False
)
async def delete_promos_v3(
    request_payload: promo_models.DeletePromosRequest,
    user_id: int,
    action_log_id: int,
    background_tasks: BackgroundTasks 
):
    has_finalised_promos = await is_finalised_or_execution_approved_promos_present(
        promo_ids=request_payload.promo_ids
    )
    if has_finalised_promos:
        await delete_promos_through_opt_flow(
            request_payload, user_id, action_log_id
        )
        notification_message = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} delete is in progress. You will be notified shortly."
    else:
        await delete_promos_directly(request_payload, user_id, action_log_id, background_tasks)
        notification_message = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} deleted successfully."
        await send_notification_for_promo_delete(
            user_id=user_id,
            promo_ids=request_payload.promo_ids,
            notification_message=notification_message,
    )    
    return notification_message

async def delete_promos_directly(
    request_payload: promo_models.DeletePromosRequest,
    user_id: int,
    action_log_id: int,
    background_tasks: BackgroundTasks
):
    await delete_promotion_service_v3(
            request_payload,
            user_id
        )
    previously_synced_promo_ids = await get_previously_synced_promo_id_service(
        promo_id_list=request_payload.promo_ids
    )
    if previously_synced_promo_ids:
        background_tasks.add_task(
            promo_utils.downstream_cloud_function_invoke,
            promo_ids=previously_synced_promo_ids,
            action=promo_constants.ACTION_ARCHIVE,
        )
    await promo_utils.update_promo_log(action_log_id, request_payload.promo_ids, 0, user_id)

async def delete_promos_through_opt_flow(
    request_payload: promo_models.DeletePromosRequest,
    user_id: int,
    action_log_id: int  
):
    request_object:promo_models.InvokeOptModel = {
            "promo_ids": request_payload.promo_ids,
            "action": promo_constants.ACTION_DELETE,
            "user_id": user_id,
            "action_log_id": action_log_id,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }
    opt_status, _ = await invoke_opt_endpoint(
            action=promo_constants.ACTION_DELETE,
            endpoint=environment.REFRESH_API_URL,
            req_data=request_object,
            message_set={
                "failure": "",
                "success": ""
            }
        )
    if not opt_status:
        raise CommonException(f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} deletion failed. Please try again later.")

def validate_opt_server_callback_payload(
    request_payload: promo_models.OptServerCallback,
    required_fields: list[str]
):
    missing_fields = []
    for field in required_fields:
        if getattr(request_payload,field,None) is None:
            missing_fields.append(field)
        
    if not missing_fields:
        return
    
    message = f"""
        {','.join(missing_fields)} are not valid
    """
    raise RequestValidationError(message)



async def handle_optimise_action_callback(
    request_payload: promo_models.OptServerCallback
):
    validate_opt_server_callback_payload(
        request_payload,["promo_id","status"]
    )

    promo_name = await get_promo_name_service(request_payload.promo_id) # type: ignore

    if request_payload.status == 200:
        header_text = "IA Recommendation Generated"
        message = f"Optimized results are ready for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}"

    else:
        header_text = "IA Recommendation Failed"
        message = f"Optimized results generation for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name} failed. Retry later or contact support!"

    await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
        action_log_id=request_payload.action_log_id,
        promo_ids=[request_payload.promo_id],
        action_log_status=0 if request_payload.status == 200 else -1,
        is_processing_status=0,
        user_id=request_payload.user_id
    )

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=message,
        promo_id=request_payload.promo_id,
        status=True if request_payload.status == 200 else False,
        identifier=promo_name,
        header_text=header_text,
    )

async def handle_simulate_action_callback(
    request_payload: promo_models.OptServerCallback
):
    validate_opt_server_callback_payload(request_payload,["source","failure_promo_ids","success_promo_ids"])

    header_action = "Simulation"
    identifier_action = "Simulation"
    message_action = "Simulation"

    # Process successful promotions
    if request_payload.success_promo_ids:
        promo_count = len(request_payload.success_promo_ids)
        if promo_count > 1:
            header_text = f"{header_action} Generated"
            identifier = f"{identifier_action} Generated"
            message = (
                f"{message_action} results are ready for {promo_count} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            )

            status = True
            request_payload.status = 200
            await insert_notification(
                request=request_payload,
                user_id=request_payload.user_id,
                module=NotificationModules.PROMOTIONS.value,
                action=request_payload.action,
                message=message,
                promo_id=None,
                status=status,
                identifier=identifier,
                header_text=header_text,
            )
        else:
            promo_id = request_payload.success_promo_ids[0]
            promo_name = await get_promo_name_service(promo_id)

            header_text = f"{header_action} Generated"
            identifier = promo_name
            message = f"{message_action} results are ready for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}"

            status = True
            request_payload.promo_id = promo_id
            request_payload.status = 200
            await insert_notification(
                request=request_payload,
                user_id=request_payload.user_id,
                module=NotificationModules.PROMOTIONS.value,
                action=request_payload.action,
                message=message,
                promo_id=promo_id,
                status=status,
                identifier=identifier,
                header_text=header_text,
            )

        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=request_payload.action_log_id,
            promo_ids=request_payload.success_promo_ids,
            source=request_payload.source,
            action_log_status=0,
            is_processing_status=0,
            auto_resimulated_flag=1,
            user_id=request_payload.user_id
        )

    if request_payload.failure_promo_ids:
        # Process failed promotions
        for promo_id in request_payload.failure_promo_ids:
            promo_name = await get_promo_name_service(promo_id)

            header_text = f"{header_action} Failed"
            identifier = promo_name
            message = f"{message_action} results generation for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name} failed. Retry later or contact support!"

            await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
                action_log_id=request_payload.action_log_id,
                promo_ids=[promo_id],
                source=request_payload.source,
                action_log_status=-1,
                is_processing_status=0,
                auto_resimulated_flag=-1,
                user_id=request_payload.user_id
            )
            status = False
            request_payload.promo_id = promo_id
            request_payload.status = 400
            await insert_notification(
                request=request_payload,
                user_id=request_payload.user_id,
                module=NotificationModules.PROMOTIONS.value,
                action=request_payload.action,
                message=message,
                promo_id=promo_id,
                status=status,
                identifier=identifier,
                header_text=header_text,
            )
    
async def handle_execution_approved_action_callback(
    request_payload: promo_models.OptServerCallback
):
    exec_approval = "Execution Approval"
    header_action = exec_approval
    identifier_action = exec_approval

    total_promos = len(request_payload.success_promo_ids)+len(request_payload.failure_promo_ids)
    eligible_promos = len(request_payload.success_promo_ids)
    failed_promos = len(request_payload.failure_promo_ids)
    promo_id_list = request_payload.success_promo_ids + request_payload.failure_promo_ids
    header_text = f"{header_action} Completed"
    identifier = f"{identifier_action} Completed"
    status = True
    request_payload.status = 200
    message = ""
    # Case 1: All promo IDs are successful
    if promo_id_list == request_payload.success_promo_ids:
        if total_promos == 1:
            # Only 1 promo ID is present, fetch the promo name
            promo_name = await get_promo_name_service(promo_id_list[0])
            message = f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
        else:
            # Multiple promo IDs are successful
            message = f"Execution completed for {total_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."

    # Case 2: All promo IDs failed
    elif total_promos == failed_promos:
        if total_promos == 1:
            # Only 1 promo ID is present and it failed
            promo_name = await get_promo_name_service(promo_id_list[0])
            message = f"Please fill Execution Metadata for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}: {promo_name}."
        else:
            message = f"Please fill Execution Metadata for {total_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."

        header_text = f"{header_action} Failed"
        identifier = f"{identifier_action} Failed"
        status = False
        request_payload.status = 400
    # Case 3: Some promo IDs passed, some failed

    else:
        if eligible_promos == 1 and failed_promos == 1:
            eligible_promo_name = await get_promo_name_service(request_payload.success_promo_ids[0])
            failed_promo_name = await get_promo_name_service(request_payload.failure_promo_ids[0])
            message = f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {eligible_promo_name}. Please fill execution metadata for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {failed_promo_name}."
        elif eligible_promos == 1:
            promo_name = await get_promo_name_service(request_payload.success_promo_ids[0])
            message = f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}. Please fill execution metadata for {failed_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."
        elif failed_promos == 1:
            promo_name = await get_promo_name_service(request_payload.failure_promo_ids[0])
            message = f"Execution completed for {eligible_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s). Please fill execution metadata for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
        else:
            message = f"Execution completed for {eligible_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s). Please fill execution metadata for {failed_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."


    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=message,
        promo_id=None,
        status=status,
        identifier=identifier,
        header_text=header_text,
    )

    await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
        action_log_id=request_payload.action_log_id,
        promo_ids=request_payload.success_promo_ids + request_payload.failure_promo_ids,
        source=request_payload.source,
        action_log_status=0,
        is_processing_status=0,
        auto_resimulated_flag=0,
        user_id=request_payload.user_id
    )

async def handle_finalize_action_callback(
    request_payload: promo_models.OptServerCallback
):

    header_action = "Finalisation"
    message_action = "Finalized"
    actionable_promos = request_payload.success_promo_ids
    if request_payload.status == 200:
        await promo_data.update_promo_status_and_refresh_stacked_promos_mapping(
            promo_ids=request_payload.success_promo_ids
        )

        identifier = f"{header_action} Completed"
        status = True
        header_text = f"{header_action} Successful"
        if len(request_payload.success_promo_ids) > 1:
            message = f"Successfully {message_action} {len(request_payload.success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
            promo_id = None
        else:
            promo_id = request_payload.success_promo_ids[0]
            promo_name = await get_promo_name_service(promo_id)
            message = f"Successfully {message_action} {promo_name}"

        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=request_payload.action_log_id,
            promo_ids=request_payload.success_promo_ids,
            action_log_status=0,
            is_processing_status=0,
            user_id=request_payload.user_id
        )
    else:
        actionable_promos = request_payload.failure_promo_ids
        status = False
        identifier = f"{header_action} Failed"
        header_text = f"{header_action} Failed"

        if len(request_payload.failure_promo_ids) > 1:
            promo_id = None
            message = f"Finalisation failed for {len(request_payload.failure_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
        else:
            promo_id = request_payload.failure_promo_ids[0]
            promo_name = await get_promo_name_service(promo_id)
            message = f"Finalisation failed for {promo_name}"

        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=request_payload.action_log_id,
            promo_ids=request_payload.failure_promo_ids,
            action_log_status=-1,
            is_processing_status=0,
            user_id=request_payload.user_id
        )

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=message,
        promo_id=promo_id,
        status=status,
        identifier=identifier,
        header_text=header_text,
        promo_ids=actionable_promos
    )


async def handle_refresh_metrics_action_callback(request_payload: promo_models.OptServerCallback):
    identifier = header_text = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} Refresh"
    if request_payload.status == 200:
        await update_is_processing_service(
            promo_id_list = request_payload.success_promo_ids,
            processing_flag = 0
        )
        status = True
        if len(request_payload.success_promo_ids) > 1:
            message = f"Successfully refreshed metrics for {len(request_payload.success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
            promo_id = None
        else:
            promo_id = request_payload.success_promo_ids[0]
            promo_name = await get_promo_name_service(promo_id)
            message = f"Successfully refreshed metrics for {promo_name}"
    else:
        status = False

        if len(request_payload.failure_promo_ids) > 1:
            promo_id = None
            message = f"Refresh of metrics failed for {len(request_payload.failure_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
        else:
            promo_id = request_payload.failure_promo_ids[0]
            promo_name = await get_promo_name_service(promo_id)
            message = f"Refresh of metrics failed for {promo_name}"

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module="promotions",
        action=request_payload.action,
        message=message,
        promo_id=promo_id,
        status=status,
        identifier=identifier,
        header_text=header_text,
        promo_ids=request_payload.success_promo_ids if request_payload.status == 200 else request_payload.failure_promo_ids
    )

async def handle_delete_action_callback(
    request_payload: promo_models.OptServerCallback,
    background_tasks: BackgroundTasks
):
    validate_opt_server_callback_payload(
        request_payload,
        ["promo_ids","status"]
    )

    notification_message = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} delete failed."
    if request_payload.status == 200:
        await delete_promotion_service_v3(
            promo_models.DeletePromosRequest(promo_ids=request_payload.promo_ids), # type: ignore
            user_id=request_payload.user_id
        )
        previously_synced_promo_ids = await get_previously_synced_promo_id_service(
            promo_id_list=request_payload.promo_ids # type: ignore
        )
        if previously_synced_promo_ids:
            background_tasks.add_task(
                promo_utils.downstream_cloud_function_invoke,
                promo_ids=previously_synced_promo_ids,
                action=promo_constants.ACTION_ARCHIVE,
            )
        await promo_utils.update_promo_log(
            request_payload.action_log_id, 
            request_payload.promo_ids, 
            0, 
            request_payload.user_id
        )
        notification_message = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} successfully deleted."
    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=notification_message,
        promo_id=None,
        status=request_payload.status == 200,
        identifier=f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} delete",
        header_text=f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} delete",
        promo_ids=request_payload.promo_ids
    )

async def handle_edit_action_callback(
    request_payload: promo_models.OptServerCallback
):
    await promo_data.update_strategy_disable_simulation_flag(
        promo_id=request_payload.promo_id, # type: ignore
        is_simulation_disabled=False
    )
    promo_name = await get_promo_name_service(request_payload.promo_id) # type: ignore
    if request_payload.status == 200:
        message = f"Edit for {promo_name} is complete, and the data has been successfully updated."
        status = True
    else:
        message = f"Edit for {promo_name} failed. Please retry or contact support."
        status = False

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=message,
        promo_id=request_payload.promo_id,
        status=status,
        identifier=promo_name,
        header_text=f"Edit {client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()}",
    )

async def handle_approve_scenario_action_callback(
    request_payload: promo_models.OptServerCallback
):
    promo_name = await get_promo_name_service(request_payload.promo_id) # type: ignore
    if request_payload.status == 200:
        message = f"Scenario for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name} has been successfully approved"
        status = True
    else:
        message = f"Scenario approval for {promo_name} failed. Please retry or contact support"
        status = False

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=request_payload.action,
        message=message,
        promo_id=request_payload.promo_id,
        status=status,
        identifier=promo_name,
        header_text="Scenario Approval",
    )

    await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
        action_log_id=request_payload.action_log_id,
        promo_ids=[request_payload.promo_id],
        action_log_status=0,
        is_processing_status=0,
        user_id=request_payload.user_id
    )


async def handle_event_edit_action_callback(request_payload: promo_models.OptServerCallback):
    await handle_event_resimulate_action_callback(
        request_payload,
        request_payload.event_id,
        request_payload.refreshed_promo_ids
    )
    

async def handle_event_resimulate_action_callback(
    request_payload: promo_models.OptServerCallback,
    event_id: int,
    promo_ids: list[int]
):
    event_details  = await event_data_module.get_event_details(event_id,select="name")
    if request_payload.status == 200:
        if promo_ids: 
            identifier = f"{len(promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
            message = f"{identifier} under event {event_details['name']} have been successfully resimulated"
        else:
            identifier = event_details['name']
            message = f"Event {event_details['name']} edit successfull"
        status = True
    else:
        identifier = event_details['name']
        message = f"Event {event_details['name']} edit failed. Please retry later or contact support."
        status = False
    
    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module=NotificationModules.EVENT.value,
        action=request_payload.action,
        message=message,
        promo_id=None,
        promo_ids=promo_ids,
        status = status,
        identifier = identifier,
        header_text = f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} Resimulate"
    )
    await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
        action_log_id=request_payload.action_log_id,
        promo_ids=promo_ids,
        action_log_status=-1 if request_payload.status != 200 else 0,
        is_processing_status=0,
        user_id=request_payload.user_id
    )
    await event_data_module.unset_event_under_processing(event_id)




async def handle_event_edit_offer_refresh_action_callback(request_payload: promo_models.OptServerCallback):
    if not request_payload.promo_ids:
        return

    action = "edit" if request_payload.action == promo_constants.ACTION_EVENT_EDIT_OFFER_REFRESH else "resimulate"

    if request_payload.status == 200:
        identifier = f"{len(request_payload.promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"
        message = f"Successfully updated the metrics of {len(request_payload.promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} due to {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} {action}"
    else:
        message = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} refresh failed for {len(request_payload.promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} due to {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} {action}"
        identifier = f"{len(request_payload.promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}"

    await insert_notification(
        request=request_payload,
        user_id=request_payload.user_id,
        module="promotions",
        action=request_payload.action,
        message=message,
        promo_id=None,
        promo_ids = request_payload.promo_ids,
        status=request_payload.status == 200,
        identifier=identifier,
        header_text=f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} Refresh",
    )

async def handle_opt_server_callback(
    request_payload: promo_models.OptServerCallback,
    background_tasks: BackgroundTasks
):
    if request_payload.action == promo_constants.OPTIMISE:
        await handle_optimise_action_callback(request_payload)
        
    elif request_payload.action == promo_constants.SIMULATE:
        await handle_simulate_action_callback(request_payload)

    elif request_payload.action == promo_constants.ACTION_EXECUTION_APPROVED:
        await handle_execution_approved_action_callback(request_payload)

    elif request_payload.action == promo_constants.ACTION_FINALIZE:
        await handle_finalize_action_callback(request_payload)

    elif request_payload.action == promo_constants.ACTION_REFRESH_METRICS:
        await handle_refresh_metrics_action_callback(request_payload)

    elif request_payload.action == promo_constants.ACTION_DELETE:
        await handle_delete_action_callback(request_payload,background_tasks)

    elif request_payload.action == promo_constants.ACTION_EDIT:
        await handle_edit_action_callback(request_payload)
        
    elif request_payload.action == promo_constants.ACTION_APPROVE_SCENARIO:
        await handle_approve_scenario_action_callback(request_payload)
    
    elif request_payload.action == promo_constants.ACTION_EVENT_EDIT:
        await handle_event_edit_action_callback(request_payload)
    
    elif request_payload.action == promo_constants.ACTION_EVENT_RESIMULATE:
        for event_data in request_payload.event_details:
            await handle_event_resimulate_action_callback(request_payload,event_data.event_id,event_data.promo_ids)
    
    elif request_payload.action in (
        promo_constants.ACTION_EVENT_EDIT_OFFER_REFRESH,
        promo_constants.ACTION_EVENT_RESIMULATE_OFFER_REFRESH
    ):
        await handle_event_edit_offer_refresh_action_callback(request_payload)

    else:
        raise RequestValidationError("Invalid action")
    
async def get_promotions(
    request_payload: promo_models.GetPromos
):
    data = await promo_data.get_promotions(
        request_payload
    )
    return data
        

async def validate_copy_offers(
    request_payload: promo_models.ValidateCopyOffers
):
    data = await promo_data.validate_copy_offers(
        request_payload
    )
    return data

async def get_vendor_funding_types():
    data = await promo_data.get_vendor_funding_types()
    return data

async def validate_event_lock(
    promo_ids: list[int],
    module: ConfigModuleEnum,
    key: ConfigKeyEnum,
    error_message: str
):
    action_allowed_after_event_lock = await get_config_value(
        module,
        key,
    )
    if not action_allowed_after_event_lock:
        await check_for_locked_events(promo_ids,error_message)

async def check_for_locked_events(
    promo_ids: list[int],
    error_message: str
):
    event_ids = await event_service.get_event_ids_by_promo_ids(promo_ids)
    has_locked_events = await event_service.has_locked_events(event_ids)
    if has_locked_events:
        raise event_exceptions.EventLockedException(error_message)


async def get_special_offer_types():
    return await promo_data.get_special_offer_types()


async def get_special_offer_type_details(offer_identifier: str):
    return await promo_data.get_special_offer_type_details(offer_identifier)


async def get_expected_time(request_payload: promo_models.TimeEstimate, user_id: int):
    return await promo_data.get_expected_time(request_payload,user_id)
    
async def get_promo_discounts_table_metadata(
    promo_id: int
):
    data = await promo_data.get_promo_discounts_table_metadata(promo_id)
    await add_addtional_columns_if_sku_or_store_level(promo_id,data)
    return data

async def add_addtional_columns_if_sku_or_store_level(promo_id: int,data: dict):
    sku_additional_columns = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.DISCOUNT_LEVEL_SKU_ADDITIONAL_COLUMNS)
    store_additional_columns = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.DISCOUNT_LEVEL_STORE_ADDITIONAL_COLUMNS)

    if await is_promo_product_discount_sku_level(promo_id):
        data["product_level_columns"]["columns"].extend(
            [
                {
                    "column_name": i["display_name"],
                    "id_key": None,
                    "value_key": i["key"] 
                }
                for i in sku_additional_columns
            ]
        )

    if await is_promo_store_discount_store_level(promo_id):
        data["store_level_columns"]["columns"].extend(
            [
                {
                    "column_name": i["display_name"],
                    "id_column": None,
                    "value_column": i["key"] 
                }
                for i in store_additional_columns
            ]
        )

async def is_promo_product_discount_sku_level(promo_id: int) -> bool:
    is_sku_discount_level = await promo_data.is_promo_product_discount_sku_level(promo_id)
    return is_sku_discount_level

async def is_promo_store_discount_store_level(promo_id: int) -> bool:
    is_store_discount_level = await promo_data.is_promo_store_discount_store_level(promo_id)
    return is_store_discount_level

async def get_promo_discounts(
    request_payload: promo_models.GetPromoDiscounts,
    user_id: int
):
    discounts_data = await promo_data.get_promo_discounts(request_payload,user_id)
    tier_data = await promo_data.get_tier_data(request_payload)
    scenario_data = await promo_data.get_scenario_data(request_payload)
    merge_discounts_data(discounts_data,tier_data,scenario_data)
    await modify_discounts_data_response(request_payload,discounts_data)
    return discounts_data

def merge_discounts_data(discounts_data,tier_data,scenario_data_mapping):
    for discount in discounts_data:
        for scenario_data in discount["scenario_data"].values():
            if scenario_data.get("tier_id") is not None:
                scenario_data["tier_name"] = tier_data[str(scenario_data["tier_id"])]
            scenario_data["scenario_name"] = scenario_data_mapping.get(str(scenario_data.get("scenario_id")))

async def modify_discounts_data_response(request_payload: promo_models.GetPromoDiscounts,discounts_data: list[dict]):
    for i,discount_record in enumerate(discounts_data):
        discounts_data[i] = modify_scenario_data_record(discount_record)
    
    update_discounts_data_with_unsaved_changes(request_payload,discounts_data)
    await add_default_scenarios_data_if_there_are_new_scenarios(request_payload,discounts_data)

def update_discounts_data_with_unsaved_changes(request_payload: promo_models.GetPromoDiscounts,discounts_data: list[dict]):
    if not request_payload.unsaved_scenario_data:
        return

    for updated_row in request_payload.unsaved_scenario_data:
        for discount_record in discounts_data:
            if discount_record["row_id"] == updated_row.row_id:
                discount_record["scenario_data"] = (
                    discount_record["scenario_data"][:1] + 
                    [i.model_dump() for i in updated_row.scenario_data]
                )


async def add_default_scenarios_data_if_there_are_new_scenarios(request_payload: promo_models.GetPromoDiscounts,discounts_data: list[dict]):
    if not request_payload.new_scenario_details:
        return
    offer_type_details = await get_promo_offer_type_details(request_payload.promo_id)
    for discount_record in discounts_data:
        for new_scenario in request_payload.new_scenario_details:
            if new_scenario.scenario_id in [i["scenario_id"] for i in discount_record["scenario_data"]]:
                continue
                
            discount_record["scenario_data"].append(
                promo_models.ScenarioDetails(
                    scenario_id=new_scenario.scenario_id,
                    scenario_name=new_scenario.scenario_name,
                    scenario_order_id=new_scenario.scenario_order_id,
                    scenario_type="resimulation",
                    offer_type_id=offer_type_details["offer_type_id"],
                    offer_type=offer_type_details["offer_type_value"]
                ).model_dump()
            )

async def get_promo_offer_type_details(promo_id: int):
    data = await promo_data.get_promo_offer_type_details(promo_id)
    return data

def modify_scenario_data_record(discount_record: dict):
    discount_record["scenario_data"] = [discount_record.pop("ia_recommended_data")] + list(discount_record["scenario_data"].values())
    discount_record = {
        **discount_record.pop("product_level_value"),
        **discount_record.pop("store_level_value"),
        **discount_record.pop("customer_level_value"),
        **discount_record,
    }
    return discount_record

async def get_new_scenario_id():
    data = await promo_data.get_new_scenario_id()
    return data

def get_scenarios_insertion_query(
    promo_id: int,
    new_scenario_data: list[promo_models.NewScenarioDetails],
    user_id: int
):
    return promo_data.get_scenarios_insertion_query(
        promo_id,
        new_scenario_data,
        user_id
    )

async def identify_new_scenarios(
    promo_id: int,
    discounts_data: list[promo_models.DiscountsData]
) -> list[promo_models.NewScenarioDetails]:
    current_scenario_ids = await promo_data.get_promo_scenario_ids(promo_id)

    new_scenario_data = []
    for discount_data in discounts_data:
        for scenario_data in discount_data.scenario_data:
            if scenario_data.scenario_name is None or scenario_data.scenario_order_id == 0:
                continue
            if scenario_data.scenario_id not in current_scenario_ids:
                new_scenario_data.append(
                    promo_models.NewScenarioDetails(
                        scenario_id=scenario_data.scenario_id,
                        scenario_name=scenario_data.scenario_name,
                        scenario_order_id=scenario_data.scenario_order_id
                    )
                )
                current_scenario_ids.append(scenario_data.scenario_id)
    return new_scenario_data

async def bulk_edit_discounts(
    request_payload: promo_models.BulkEditDiscountsRequest,
    user_id: int
):
    for i, discount_data in enumerate(request_payload.discounts_data or []):
        request_payload.discounts_data[i]._scenario_data = get_formatted_scenario_data(discount_data,user_id) # type: ignore
    
    for bulk_edit_data in request_payload.bulk_edit_data:
        bulk_edit_data.offer_value = promo_utils.get_offer_description(bulk_edit_data)

    await promo_data.bulk_edit_discounts(
        request_payload,
        user_id
    )

async def get_product_details_of_promo(promo_id:int):
    data = await promo_data.get_product_details_of_promo(promo_id)
    return data

async def get_store_details_of_promo(promo_id:int):
    data = await promo_data.get_store_details_of_promo(promo_id)
    return data

async def approve_ia_scenario(request_payload: promo_models.ApproveIaScenario, user_id: int, is_finalised_promo: bool, action_log_id: int):
    await promo_data.approve_ia_scenario(request_payload, user_id)
    if not is_finalised_promo:
        return
    try:
        # invoke opt end point to delete finalised data and refresh metrics of mapped promos
        refresh_finalised_data_opt_call_req: promo_models.InvokeOptModel = {
            "action_log_id": action_log_id,
            "promo_id": request_payload.promo_id,
            "scenario_id": request_payload.scenario_id,
            "user_id": user_id,
            "action": promo_constants.ACTION_APPROVE_SCENARIO,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }

        message_set: promo_models.MessageSet = {
            "success": "Scenario approved successfully",
            "failure": "Scenario approval failed. Please try again later"
        }
        res_status, response_text = await invoke_opt_endpoint(
            action=promo_constants.ACTION_APPROVE_SCENARIO,
            endpoint=environment.REFRESH_API_URL,
            req_data=refresh_finalised_data_opt_call_req,
            message_set=message_set
            )
        print(res_status, response_text)
        if not res_status:
            raise BusinessLogicException(response_text)
    except Exception as e:
        raise BusinessLogicException("Scenario approval failed. Please try again later") from e
    
    
async def download_promo_sku_price_data(promo_id: int, user_id: int):
    query = promo_queries.FETCH_PROMO_PRICE_FILE.format(
        promo_id = promo_id
    )
    await promo_utils.cloud_function_report_handler(
        fetch_query = query,
        report_file_name = promo_constants.PROMO_SKU_PRICE_DATA,
        report_type = "excel",
        user_id = user_id,
        promo_name = None,
        report_file_name_str = "SKU Price Data "
    )
    
async def copy_discounts(request_payload: promo_models.CopyDiscounts,user_id: int):
    await promo_data.copy_discounts(request_payload,user_id) 

async def get_scenario_name(scenario_id: int):
    data = await promo_data.get_scenario_name(scenario_id)
    return data