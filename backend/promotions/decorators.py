import inspect
from pydantic import BaseModel
from promotions import utils as promo_utils
from typing import Callable, Required, TypedDict
from typing_extensions import Unpack
from logger.logger import logger
import functools
import sys


class LogActionKwargsType(TypedDict):
    user_id: Required[int]


def log_action_decorator(
    get_promo_ids_func: Callable,
    action_name: str,
    screen_name: str,
    update_task_completion: bool = True
):
    '''
    Description:
        Decorator to log action in price_promo.tb_action_log_master table
        and handle is_under_processing flag
    Parameters:
        get_promo_ids_func: function to get promo_ids from payload
        action_name: what action is being performed
        screen_name: where the action is being performed
        update_task_completion: whether to update task completion or not( 
            when we are hitting opt api for any action then we don't want to update task completion
            as it is updated in the callback api
        )  
    '''

    def inner_function(decorated_function: Callable):

        @functools.wraps(decorated_function)
        async def decorator(payload: BaseModel,user_id:int,*args,**kwargs):
            action_log_id = None
            try:
                promo_ids = get_promo_ids_func(payload)
                promo_ids = [promo_ids] if not isinstance(promo_ids,list) else promo_ids

                action_log_id = await promo_utils.log_action_v2(
                    payload,
                    promo_ids,
                    action_name,
                    screen_name,
                    user_id
                )
                if "action_log_id" in inspect.signature(decorated_function).parameters:
                    kwargs["action_log_id"] = action_log_id
                return (await decorated_function(payload,user_id,*args,**kwargs))
            finally:
                _,exception_value,_ = sys.exc_info()
                if exception_value is not None:
                    logger.error(f"Exception in {decorated_function.__name__}: {exception_value}",exc_info=True)
                if action_log_id and (update_task_completion or exception_value is not None):
                    await promo_utils.update_promo_log(
                        action_log_id,
                        promo_ids,
                        0 if exception_value is None else -1,
                        user_id
                    )
        return decorator

    return inner_function