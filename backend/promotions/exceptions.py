
from exceptions.custom_base_exception import CustomBaseException
from fastapi import status
from client_configuration import constants as client_configuration_constants

class PromoAlreadyExistsException(CustomBaseException):
    def __init__(self, message: str) -> None:
        super().__init__(message, status.HTTP_409_CONFLICT)

class OptRefreshApiException(CustomBaseException):
    def __init__(self, message: str = f"Failed to refresh the affected {client_configuration_constants.PROMO_IDENTIFIER_PLURAL}") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)
    

class SimulationApiCallFailedException(CustomBaseException):
    def __init__(self, message: str = "Simulation Failed") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)


class OptCallFailedException(CustomBaseException):
    def __init__(self, message: str = "Optimisation failed") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)
    

    
class MissingDiscountValuesException(CustomBaseException):
    def __init__(self, message: str = "Please enter discounts for all levels to proceed with the simulation") -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)

class UptoDiscountMismatchError(CustomBaseException):
    def __init__(self, message: str =  "Upto X%' offer type which cannot be combined with other offer types. Please review and adjust the discounts") -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)
