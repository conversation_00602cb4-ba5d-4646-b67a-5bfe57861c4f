from enum import Enum


class DiscountLevelEnum(Enum):
    OVERALL = "Overall"
    DIVISION = "Division"
    GROUP = "Group"
    DEPARTMENT = "Department"
    CLASS = "Class"
    SUB_CLASS = "Sub-class"
    PRODUCT_GROUP = "Product-Group"
    BRAND = "Brand"
    SKU = "SKU"

class OfferTypeEnum(Enum):
    BXGY_PERCENT_OFF = (2, "BXGY % Off")
    EXTRA_AMOUNT_OFF = (13, "$ Off")
    UPTO_X_PERCENT_OFF = (14, "UPTO X % Off")
    PERCENT_OFF = (15, "% Off")
    FIXED_PRICE = (17, "PP")
    BXGY = (27, "BXGY")
    BMSM = (32, "BMSM")
    TIERED_OFFER = (65, "Tier")

    @property
    def id_value(self):
        return self._value_[0]

    @property
    def display_name(self):
        return self._value_[1]
    
class ScreenTypeEnum(Enum):
    DECISION_DASHBOARD = "decision_dashboard"
    CALENDAR_VIEW = "calendar_view" 
    WORKBENCH = "workbench"
