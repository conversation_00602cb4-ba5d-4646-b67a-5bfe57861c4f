GET_STORE_GROUPS = """
    with filtered_store_groups_cte as (
        select 
            store_group_id
        from price_promo.fn_filter_store_groups(
            {store_hierarchies}::jsonb,
            {store_ids}::int[],
            {event_id}
        )
    ),
    sg_data_cte as(
        select
            tsg.sg_id as store_group_id,
            tsg.sg_name as store_group_name,
            tsg.description as store_group_description,
            um.name as created_by_user,
            tsg.created_at,
            uum.name as modified_by_user,
            case
                when tsg.created_at <> tsg.updated_at then tsg.updated_at
                else null
            end modified_at,
            tsg.stores_count,
            array_agg(distinct sm.s0_name) as  s0_name,
            array_agg(distinct sm.s1_name) as  s1_name,
            array_agg(distinct sm.s2_name) as  s2_name,
            tsg.is_under_processing as group_under_process
        from
            {global_schema}.tb_store_group tsg
        inner join 
            {global_schema}.tb_sg_store tss on tss.sg_id = tsg.sg_id
        inner join 
            {global_schema}.tb_store_master sm on sm.store_id = tss.store_id
        left join {global_schema}.user_master um on
            um.user_code = tsg.created_by
        left join {global_schema}.user_master uum on
            uum.user_code = tsg.updated_by
        where
            tsg.sg_id in (select store_group_id from filtered_store_groups_cte)
            and
            tsg.is_deleted <> 1
        group by
            store_group_id,
            store_group_name,
            store_group_description,
            created_by_user,
            tsg.created_at,
            modified_by_user,
            modified_at
    ),
    sg_strategy_map_cte as(
        select 
            sdc.store_group_id,
            count(tssg.strategy_id) as strategies
        from 
            sg_data_cte sdc
        left join 
            {markdown_schema}.tb_strategy_store_groups tssg on sdc.store_group_id = tssg.store_group_id 
        group by 
            sdc.store_group_id
    ),
    sg_promo_map_cte as(
        select 
            sdc.store_group_id,
            count(tpsg.promo_id) as promos
        from 
            sg_data_cte sdc
        left join 
            {promo_schema}.tb_promo_store_groups tpsg on sdc.store_group_id = tpsg.store_group_id 
        group by 
            sdc.store_group_id
    )
    select 
        sdc.*,
        ssmc.strategies as strategies_count,
        spmc.promos as promos_count
    from 
        sg_data_cte sdc
    inner join 
        sg_strategy_map_cte ssmc using(store_group_id)
    inner join 
        sg_promo_map_cte spmc using(store_group_id)
"""

UNGROUPED_STORES_QUERY = """
    select
        tsm.*
    from
        {global_schema}.tb_store_master tsm
    where
        {hierarchies_filter}
        and store_id not in (
            select
                distinct tss.store_id
            from
                {global_schema}.tb_sg_store tss
            join {global_schema}.tb_store_group tsg on
                tsg.sg_id = tss.sg_id
            where
                tsg.is_deleted = 0
        )
        and is_active = 1
"""


GET_UNGROUPED_STORES_COUNT_AND_PERCENTAGE = """
    select
        (case 
            when round(cast(ungrouped_stores.ungrouped_stores_count / nullif(cast(total_stores.total_store_count as FLOAT),0)* 100 as DECIMAL),2) is not null 
                 then round(cast(ungrouped_stores.ungrouped_stores_count / nullif(cast(total_stores.total_store_count as FLOAT),0)* 100 as DECIMAL),2)
                 else 0 end 
        ) as ungrouped_percentage,
        ungrouped_stores.ungrouped_stores_count as ungrouped_count
    from
        (
        select
            COUNT(*) total_store_count
        from
            {global_schema}.tb_store_master
        where
            {hierarchies_filter} 
            and is_active = 1
        ) total_stores,
        (	select
                COUNT(*) as ungrouped_stores_count
            from
                {global_schema}.tb_store_master
            where
                {hierarchies_filter}
                and store_id not in (
                    select
                        distinct tb_sg_store.store_id
                    from
                        {global_schema}.tb_sg_store
                    join {global_schema}.tb_store_group on
                        tb_store_group.sg_id = tb_sg_store.sg_id
                    where
                        tb_store_group.is_deleted = 0
                )
                and is_active = 1
        ) ungrouped_stores
"""

GET_STORE_GROUP_IF_NOT_DELETED = """
    select 
        sg_name,
        is_deleted
    from 
        {global_schema}.tb_store_group
    where 
        sg_name ilike({store_group_name})
        and is_deleted = 0
"""

GET_STORES_HIERARCHIES = """
    with store_hierarchy_data as (
        select 
            {select_list}
        from 
            {global_schema}.tb_store_master tsm 
        where 
            tsm.store_id = any(array{store_ids})
    )
    select 
        t1.hierarchy_level,
        t1.hierarchy_value
    from
        (
            {union_list}
        )t1
    where 
	    t1.hierarchy_value is not null
"""

INSERT_STORE_GROUP = """
    DO $$
    DECLARE
        _sg_id integer;
        partition_query text; 
    BEGIN

        -- Insert basic Store Group Info.
        insert into {global_schema}.tb_store_group (sg_name, description, created_by, stores_count)
        values ({sg_name}, {description}, {created_by}, {stores_count}) returning sg_id into _sg_id;

        -- Create Partition if not exists.
        call global.pc_create_partition_for_pgs_or_sgs('tb_sg_store', _sg_id);

        -- Grant Partition Table Permissions.
        -- execute 'GRANT ALL ON table {global_schema}.tb_sg_store_' || _sg_id::text || ' TO "mtp-dev"';

        -- Insert SG Stores.
        with hierarchy_insert_cte as(
            insert 
                into {global_schema}.tb_sg_hierarchy (sg_id, hierarchy_level, hierarchy_value)
            select
                _sg_id as sg_id,
                dd.hierarchy_level,
                dd.hierarchy_value
            from
                (
                    {sub_query}
                )dd
        )
        insert
            into {global_schema}.tb_sg_store (sg_id, store_id)
        select
            _sg_id as sg_id,
            cast(dd.store_id as INTEGER)
        from
            (
                select json_array_elements_text('{store_ids}') store_id
            )dd;
        perform global.fn_refresh_materialized_view('global', 'mvw_sg_hierarchy_agg_data');
    END;
    $$;
"""

GET_SG_PROCESS = """
    select tsg.sg_id from {global_schema}.tb_store_group tsg
    where tsg.sg_id = {sg_id} and tsg.is_under_processing = 1
"""

GET_STORE_GROUP = """
    with store_group_details as (
        SELECT 
            sg_name as store_group_name,
            description as store_group_description,
            store_id
        FROM 
            {global_schema}.tb_store_group
        inner join 
            {global_schema}.tb_sg_store using (sg_id)
        where 
            sg_id = {store_group_id}
    )
    select
        store_group_name,
        store_group_description,
        jsonb_agg(
            jsonb_build_object(
                's0_name', sm.s0_name,
                's1_name', sm.s1_name,
                's2_name', sm.s2_name,
                's3_name', sm.s3_name,
                's4_name', sm.s4_name,
                's5_name', sm.s5_name,
                'store_id', sm.store_id,
                'store_name',sm.store_name,
                'active', case when sm.is_active = 1 then true else false end
            )
        ) as store_details
    from
        store_group_details sgd
    inner join
        {global_schema}.tb_store_master sm on sgd.store_id = sm.store_id
    group by 
        store_group_name,
        store_group_description
"""

GET_EFFECTED_PROMOS_FOR_STORE_GROUP = """
    select 
        'Offer' as plan,
        pm.name as plan_name,
        pm.promo_id as plan_id,
        psc.status_name::text as plan_status,
        pm.start_date,
        pm.end_date,
        pr.discount_level as plan_discount_level,
        case when pm.is_under_processing = 0 then 0 else 1 end as under_process,
        um.user_name
    from price_promo.promo_master as pm
    left join price_promo.ps_rules as pr on pr.promo_id = pm.promo_id
    join price_promo.tb_promo_store_groups as tppg on tppg.promo_id = pm.promo_id
    join global.user_master um on um.user_code = pm.created_by
    inner join price_promo.promo_status_config psc on pm.status = psc.status_id
    where tppg.store_group_id = {store_group_id}
        and pm.start_date > date(timezone({client_timezone}, now()))
        and pm.status not in (-1, 6) 
        and pm.step_count > 0
"""

SG_NAME_UNIQUE_QUERY = """
    select 
        sg_name,
        is_deleted
    from 
        {global_schema}.tb_store_group
    where 
        sg_name ilike({store_group_name})
        and is_deleted = 0
        and sg_id != {store_group_id}
"""

UPDATE_SG_PROCESS = """
    update {global_schema}.tb_store_group set is_under_processing = {set_to}
    where sg_id = {sg_id}
"""

UPDATE_STORE_GROUP_QUERY = """
    select * 
    from 
        {promo_schema}.fn_update_store_group(
            {edit_sg_id}::integer,
            {store_group_name}::text,
            {store_group_description}::text,
            {user_id}::integer,
            {sg_sub_query}::text,
            array{store_ids}::integer[], 
            array{selected_strategies}::integer[], 
            array{selected_promos}::integer[],
            {client_timezone}::text
        )
"""

DELETE_STORE_GROUPS = """
    update {global_schema}.tb_store_group 
        set is_deleted = 1, updated_by = {user_id}, updated_at = now()
    where sg_id in ({store_group_id_str})
"""

GET_STORE_GROUP_PROMOS = """
    select 
        pm.name as promo_name,
        pm.start_date,
        pm.end_date,
        pm.promo_id,
        psc.status_name as promo_status,
        um.name as created_by
    from
        {promo_schema}.tb_promo_store_groups tpsg
    inner join
        {promo_schema}.promo_master pm using(promo_id)
    inner join 
        {promo_schema}.promo_status_config psc on pm.status = psc.status_id
    left join 
        {global_schema}.user_master um on pm.created_by = um.user_code
    where 
        tpsg.store_group_id = {store_group_id}
"""


STORE_GROUP_DOWNLOAD_QUERY = """
    with filtered_store_groups_cte as (
        select 
            store_group_id
        from price_promo.fn_filter_store_groups(
            {store_hierarchies}::jsonb,
            {store_ids}::int[],
            {event_id}
        )
    ),
    sg_data_cte as(
        select
            tsg.sg_id as store_group_id,
            tsg.sg_name as store_group_name,
            tsg.description as store_group_description,
            usr.name as created_by_user,
            tsg.created_at,
            user_updated_by_table.name as modified_by_user,
            case
                when tsg.created_at <> tsg.updated_at then tsg.updated_at
                else null
            end modified_at,
            COUNT(tss.store_id) stores_count,
            array_agg(distinct sm.s0_name) as  s0_name,
            array_agg(distinct sm.s1_name) as  s1_name,
            array_agg(distinct sm.s2_name) as  s2_name
        from
            global.tb_store_group tsg
        inner join 
            global.tb_sg_store tss on tss.sg_id = tsg.sg_id
        inner join 
            global.tb_store_master sm on sm.store_id = tss.store_id
        left join global.user_master usr on
            usr.user_code = tsg.created_by
        left join global.user_master user_updated_by_table on
            user_updated_by_table.user_code = tsg.updated_by
        where
            tsg.sg_id in (select store_group_id from filtered_store_groups_cte)
            and       
            tsg.is_deleted <> 1 
        group by
            store_group_id,
            store_group_name,
            store_group_description,
            created_by_user,
            tsg.created_at,
            modified_by_user,
            modified_at
    ),
    sg_strategy_map_cte as(
        select 
            sdc.store_group_id,
            count(tssg.strategy_id) as strategies
        from 
            sg_data_cte sdc
        left join 
            price_markdown.tb_strategy_store_groups tssg on sdc.store_group_id = tssg.store_group_id 
        group by 
            sdc.store_group_id
    ),
    sg_promo_map_cte as(
        select 
            sdc.store_group_id,
            count(tpsg.promo_id) as promos
        from 
            sg_data_cte sdc
        left join 
            price_promo.tb_promo_store_groups tpsg on sdc.store_group_id = tpsg.store_group_id 
        group by 
            sdc.store_group_id
    )
    select 
        sdc.store_group_name as "STORE GROUP NAME",
        sdc.store_group_description as "DESCRIPTION",
        sdc.stores_count as "STORES",
        array_to_string(sdc.s0_name,', ') as "{s0_name}",
        array_to_string(sdc.s1_name,', ') as "{s1_name}",
        array_to_string(sdc.s2_name,', ') as "{s2_name}",
        sdc.created_by_user as "CREATED BY",
        date(timezone({client_timezone}, sdc.created_at)) as "CREATED ON",
        sdc.modified_by_user as "MODIFIED BY",
        date(timezone({client_timezone}, sdc.modified_at)) as "MODIFIED ON",
        spmc.promos as "PROMOS",
        ssmc.strategies as "STRATEGIES"
    from 
        sg_data_cte sdc
    inner join 
        sg_strategy_map_cte ssmc using(store_group_id)
    inner join 
        sg_promo_map_cte spmc using(store_group_id)
"""

GET_STORE_DETAILS = """
    select
        tsm.*
    FROM
        (
            select
                store_id
            from
                {global_schema}.tb_sg_store
            where
                sg_id = {store_group_id}
        ) as tss
        left join {global_schema}.tb_store_master tsm on tss.store_id = tsm.store_id
    """