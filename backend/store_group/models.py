from typing import Optional
from pydantic import BaseModel,field_validator,Field
from exceptions.exceptions import CommonException


class StoreHierarchyFilter(BaseModel):
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_code: list[int] = []
    event_id: Optional[int]= None

class UnGroupedStoresRead(StoreHierarchyFilter):
    only_meta_data: bool = False


class StoreGroupWrite(BaseModel):
    store_group_name: str
    store_group_description: str
    store_id: list[int]

    @field_validator("store_group_name")
    @classmethod
    def is_sg_name_empty(cls, value):  # pylint: disable=E0213
        if not value.strip():
            raise CommonException("Store Group Name cannot be empty")
        return value

    @field_validator("store_group_description")
    @classmethod
    def is_sg_desc_empty(cls, value):  # pylint: disable=E0213
        if not value.strip():
            raise CommonException("Store Group Description cannot be empty")
        return value

class StoreGroupUpdate(StoreGroupWrite):
    store_group_id: int
    selected_plans: Optional[list] = []
    guid: str
    application: Optional[str] = 'promo'

class StoreGroupProcess(BaseModel):
    store_group_id: int

class StoreGroupsDelete(BaseModel):
    store_group_id: list[int]