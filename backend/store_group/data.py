from typing import Any
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from exceptions.exceptions import UniqueConstraintViolation
from pricesmart_common.utils import async_execute_query, get_array_format, get_key_value_str, get_str_repr
from store_group import queries as store_group_queries
from store_group import models as store_group_models
from store_group import constants as store_group_constants
from configuration.environment import environment
from logger.logger import logger
from psycopg2.errors import UniqueViolation
from exceptions.exceptions import ConflictException
from store_group import exceptions as store_group_exceptions
from pricesmart_common import constants as common_constants
from promotions import queries as promo_queries
from store_group import utils as store_group_utils
import client_configuration.constants as client_configuration_constants

async def get_store_groups(
    request_payload: store_group_models.StoreHierarchyFilter
):
    query = store_group_queries.GET_STORE_GROUPS.format(
        global_schema=environment.global_schema,
        um_schema=environment.um_schema,
        markdown_schema=environment.markdown_schema,
        promo_schema=environment.promo_schema,
        store_hierarchies = get_str_repr(request_payload.store_hierarchies),
        store_ids = get_array_format(request_payload.store_code),
        event_id=get_str_repr(request_payload.event_id)
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_ungrouped_stores_count_and_percentage(store_hierarchies: dict[str, Any]):
    hierarchies_list = (
        get_key_value_str(key, value)
        for key, value in store_hierarchies.items()
        if value
    )
    hierarchies_filter = " and ".join(hierarchies_list)
    query = store_group_queries.GET_UNGROUPED_STORES_COUNT_AND_PERCENTAGE.format(
        global_schema=environment.global_schema,
        hierarchies_filter=hierarchies_filter if hierarchies_filter else "true",
    )
    ungrouped_stores_count_and_percentage = await async_execute_query(query)
    return ungrouped_stores_count_and_percentage[0]

async def get_ungrouped_stores(store_hierarchies: dict[str, Any]):
    hierarchies_list = (
        get_key_value_str(key, value)
        for key, value in store_hierarchies.items()
        if value
    )
    hierarchies_filter = " and ".join(hierarchies_list)
    query = store_group_queries.UNGROUPED_STORES_QUERY.format(
        global_schema=environment.global_schema,
        hierarchies_filter=hierarchies_filter if hierarchies_filter else "true",
    )
    ungrouped_stores = await async_execute_query(query)
    return ungrouped_stores

async def insert_store_group(store_group: store_group_models.StoreGroupWrite, user_id: int):

    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    
    select_list = store_group_utils.get_store_hierarchies_select_list(store_hierarchy_config)
    union_list = store_group_utils.get_store_hierarchies_union_list(store_hierarchy_config)
    
    is_store_group_exists_query = store_group_queries.GET_STORE_GROUP_IF_NOT_DELETED.format(
        global_schema=environment.global_schema,
        store_group_name=get_str_repr(store_group.store_group_name),
    )
    is_store_group_exists_data = await async_execute_query(is_store_group_exists_query)
    if is_store_group_exists_data:
        raise UniqueConstraintViolation(store_group_constants.STORE_GROUP_NAME, store_group.store_group_name)

    store_hierarchies_sub_query = store_group_queries.GET_STORES_HIERARCHIES.format(
        global_schema=environment.global_schema, store_ids=store_group.store_id,
        select_list=select_list,
        union_list=union_list
    )
    query = store_group_queries.INSERT_STORE_GROUP.format(
        sg_name=get_str_repr(store_group.store_group_name),
        description=get_str_repr(store_group.store_group_description),
        created_by=user_id,
        store_ids=store_group.store_id,
        global_schema=environment.global_schema,
        stores_count=len(store_group.store_id),
        sub_query=store_hierarchies_sub_query
    )
    try:
        await async_execute_query(query)
    except UniqueViolation as err:
        raise UniqueConstraintViolation(
            store_group_constants.STORE_GROUP_NAME, store_group.store_group_name
        ) from err

async def check_sg_process(store_group_id: int):
    query = store_group_queries.GET_SG_PROCESS.format(
        global_schema=environment.global_schema,
        sg_id=store_group_id
    )
    sg_process = await async_execute_query(query)
    if sg_process:
        detail = "Store group is currently being edited by another user"
        raise ConflictException(detail)
    return True

async def get_store_group(store_group_id: int):
    query = store_group_queries.GET_STORE_GROUP.format(
        global_schema=environment.global_schema,
        store_group_id=store_group_id,
    )
    data = await async_execute_query(query)
    if not data:
        raise store_group_exceptions.StoreGroupIdNotFound
    return data[0]

async def get_effected_promos_for_store_group(store_group_id: int):
    query = store_group_queries.GET_EFFECTED_PROMOS_FOR_STORE_GROUP.format(
        global_schema=environment.global_schema,
        store_group_id=store_group_id,
        client_timezone=get_str_repr(common_constants.CLIENT_TIMEZONE),
    )
    data = await async_execute_query(query)
    return data

async def sg_name_unique_check(stores_info: store_group_models.StoreGroupUpdate):
    is_sg_name_unique_query = store_group_queries.SG_NAME_UNIQUE_QUERY.format(
        global_schema=environment.global_schema,
        store_group_id=stores_info.store_group_id,
        store_group_name=get_str_repr(stores_info.store_group_name),
    )
    logger.info(f"------is_sg_name_unique_query-----\n{is_sg_name_unique_query}")
    is_sg_name_exists_data = await async_execute_query(is_sg_name_unique_query)
    if is_sg_name_exists_data:
        raise UniqueConstraintViolation(store_group_constants.STORE_GROUP_NAME, stores_info.store_group_name)
    return True

async def update_sg_process(stores_info: store_group_models.StoreGroupUpdate, set_to: int):
    query = store_group_queries.UPDATE_SG_PROCESS.format(
        global_schema=environment.global_schema,
        set_to=set_to,
        sg_id=stores_info.store_group_id,
    )
    await async_execute_query(query)

async def strategies_promo_process_check(stores_info: store_group_models.StoreGroupUpdate):
    selected_promos = []
    if stores_info.selected_plans:
        selected_promos = [
            i["plan_id"] for i in stores_info.selected_plans if i["plan"] == "Offer"
        ]

    query = promo_queries.PROMOS_PROCESS_CHECK.format(
        selected_promos=selected_promos
    )
    process_check = await async_execute_query(query)
    if process_check:
        detail = f"Selected {client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.capitalize()} is currently being edited by another user"
        raise ConflictException(detail)
    return True

async def update_store_group(stores_info: store_group_models.StoreGroupUpdate, user_id: int):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    
    select_list = store_group_utils.get_store_hierarchies_select_list(store_hierarchy_config)
    union_list = store_group_utils.get_store_hierarchies_union_list(store_hierarchy_config)
    store_hierarchies_sub_query = store_group_queries.GET_STORES_HIERARCHIES.format(
        global_schema=environment.global_schema, store_ids=stores_info.store_id, 
        select_list=select_list,
        union_list=union_list
    )

    selected_strategies = []
    selected_promos = []
    if stores_info.selected_plans:
        selected_strategies = [
            i["plan_id"]
            for i in stores_info.selected_plans
            if i["plan"] == "Markdown strategy"
        ]
        selected_promos = [
            i["plan_id"] for i in stores_info.selected_plans if i["plan"] == "Offer"
        ]

    query = store_group_queries.UPDATE_STORE_GROUP_QUERY.format(
        promo_schema=environment.promo_schema,
        edit_sg_id=stores_info.store_group_id,
        store_group_name=get_str_repr(stores_info.store_group_name),
        store_group_description=get_str_repr(stores_info.store_group_description),
        user_id=user_id,
        sg_sub_query=get_str_repr(store_hierarchies_sub_query),
        store_ids=stores_info.store_id,
        selected_strategies=selected_strategies,
        selected_promos=selected_promos,
        client_timezone=get_str_repr(common_constants.CLIENT_TIMEZONE),
    )

    logger.info(f"-----query----{query}")
    updated_sg_res = await async_execute_query(query, timelimit=None)
    logger.info(f"-----update_sg_res----{updated_sg_res}")
    updated_sg_ig = updated_sg_res[0]["fn_update_store_group"]

    return updated_sg_ig


async def delete_store_groups(sgs_info: store_group_models.StoreGroupsDelete, user_id: int):
    store_group_id_str = ", ".join(str(sg_id) for sg_id in sgs_info.store_group_id)
    query = store_group_queries.DELETE_STORE_GROUPS.format(
        global_schema=environment.global_schema,
        store_group_id_str=store_group_id_str,
        user_id=user_id
    )
    await async_execute_query(query)

async def get_store_group_promos(store_group_id: int):
    query = store_group_queries.GET_STORE_GROUP_PROMOS.format(
        promo_schema=environment.promo_schema,
        store_group_id=store_group_id,
        global_schema=environment.global_schema,
    )
    data = await async_execute_query(query)
    return data

async def get_store_details_from_store_group(store_group_id: int):
    query = store_group_queries.GET_STORE_DETAILS.format(
        promo_schema=environment.promo_schema,
        store_group_id=store_group_id,
        global_schema=environment.global_schema,
    )
    data = await async_execute_query(query)
    return data
