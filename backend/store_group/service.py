import asyncio
from datetime import datetime
from enums.Enums import ConfigKey<PERSON><PERSON>, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value
from server_sent_events.utils import insert_notification
from store_group import models as store_group_models
from store_group import data as store_group_data_module
from store_group import queries as store_group_queries
from pricesmart_common import constants as global_constants
from pricesmart_common.utils import get_str_repr,get_array_format
from promotions import utils as promo_utils
from logger.logger import logger

async def get_store_groups(request_payload: store_group_models.StoreHierarchyFilter):

    return await store_group_data_module.get_store_groups(
        request_payload
    )

async def get_ungrouped_stores(
    ungrouped_stores_filter: store_group_models.UnGroupedStoresRead,
):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)

    store_hierarchies = {
        store_hierarchy_config[key]["id_column"]: value 
        for key, value in ungrouped_stores_filter.store_hierarchies.items()
    }
    if ungrouped_stores_filter.only_meta_data:
        data = await store_group_data_module.get_ungrouped_stores_count_and_percentage(
            store_hierarchies
        )
    else:
        data = await store_group_data_module.get_ungrouped_stores(store_hierarchies)
    return data

async def insert_store_group(store_group: store_group_models.StoreGroupWrite, user_id: int):
    await store_group_data_module.insert_store_group(store_group, user_id)

async def check_sg_process(store_group_id: int):
    await store_group_data_module.check_sg_process(store_group_id)

async def get_store_group(store_group_id: int):
    data = await store_group_data_module.get_store_group(store_group_id)
    return data

async def get_effected_promos_for_store_group(store_group_id: int):
    data = await store_group_data_module.get_effected_promos_for_store_group(store_group_id)
    return data

async def sg_name_unique_check(stores_info: store_group_models.StoreGroupUpdate):
    await store_group_data_module.sg_name_unique_check(stores_info)

async def update_sg_process(stores_info: store_group_models.StoreGroupUpdate, set_to: int):
    await store_group_data_module.update_sg_process(stores_info, set_to)

async def strategies_promo_process_check(stores_info: store_group_models.StoreGroupUpdate):
    await store_group_data_module.strategies_promo_process_check(stores_info)

async def update_store_group(
    stores_info: store_group_models.StoreGroupUpdate,
    user_id: int,
):
    start_time = datetime.now()
    try:
        updated_sg_id = await store_group_data_module.update_store_group(stores_info, user_id)
        message = f"Store Group {stores_info.store_group_name} modified successfully."
        status = True
    except Exception as err:
        message = f"Store Group {stores_info.store_group_name} edit failed."
        status = False
        logger.error(err,exc_info=True)
        updated_sg_id = stores_info.store_group_id
    time_taken = (datetime.now() - start_time).total_seconds()
    if time_taken < 2:
        """
        sleeping for 5 secs because the background process notification 
        is going first before the api response
        """
        await asyncio.sleep(5-time_taken)
    await store_group_data_module.update_sg_process(stores_info, 0)
    await insert_notification(
        request={},
        user_id=user_id,
        module="store_group",
        action="store group edit",
        message=message,
        promo_id=None,
        status=status,
        identifier="store_group",
        header_text="Store Group Edit",
        promo_ids=None,
        navigate_to=str(updated_sg_id)
    )

async def delete_store_groups(sgs_info: store_group_models.StoreGroupsDelete, user_id: int):
    await store_group_data_module.delete_store_groups(sgs_info, user_id)

async def get_store_group_promos(store_group_id: int):
    data = await store_group_data_module.get_store_group_promos(store_group_id)
    return data

async def download_store_group_report(request_payload: store_group_models.StoreHierarchyFilter, user_id: int):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    query = store_group_queries.STORE_GROUP_DOWNLOAD_QUERY.format(
        client_timezone = get_str_repr(global_constants.CLIENT_TIMEZONE),
        store_hierarchies = get_str_repr(request_payload.store_hierarchies),
        store_ids = get_array_format(request_payload.store_code),
        event_id = get_str_repr(request_payload.event_id),
        s0_name = store_hierarchy_config["s0_ids"]["label"].upper(),
        s1_name = store_hierarchy_config["s1_ids"]["label"].upper(),
        s2_name = store_hierarchy_config["s2_ids"]["label"].upper(),
    )

    await promo_utils.cloud_function_report_handler(
        fetch_query = query,
        report_file_name = "store_group_report",
        report_type = "excel",
        user_id = user_id,
        promo_name = None
    )

async def get_store_details_from_store_group(store_group_id: int):
    data = await store_group_data_module.get_store_details_from_store_group(store_group_id)
    return data