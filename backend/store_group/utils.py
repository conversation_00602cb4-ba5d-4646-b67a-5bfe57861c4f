from filters.types import HierarchyFiltersInfoType


def get_store_hierarchies_select_list(store_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = ""
    for hierarchy_key, cfg in store_hierarchy_config.items():
        if cfg.get('is_linked_to_store_group'):
            id_column = cfg['id_column']  # The id_column from the configuration
            select_list += f"array_agg(DISTINCT tsm.{id_column}) AS {hierarchy_key}, "

    select_list = select_list.rstrip(", ")    
    return select_list


def get_store_hierarchies_union_list(store_hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    union_list = ""
    for hierarchy_key, cfg in store_hierarchy_config.items():
            if cfg.get('is_linked_to_store_group'):
                level_id = cfg['id']
                union_list += (
                    f"SELECT {level_id} AS hierarchy_level, "
                    f"unnest({hierarchy_key}) AS hierarchy_value "
                    f"FROM store_hierarchy_data UNION ALL "
                )
    union_list = union_list.rstrip(" UNION ALL ")

    return union_list