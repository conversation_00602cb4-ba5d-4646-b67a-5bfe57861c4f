from fastapi import APIRouter, BackgroundTasks
from app.dependencies import UserDependency
from store_group import constants as store_group_constants
from store_group import models as store_group_models
from store_group import service as store_group_service
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as global_constants

router = APIRouter(tags=[store_group_constants.STORE_GROUP_API_TAG])


@router.post(
    "/get-store-groups",
)
async def get_store_groups(
    store_hierarchy_filter: store_group_models.StoreHierarchyFilter,
):
    data = await store_group_service.get_store_groups(store_hierarchy_filter)
    return common_utils.create_response(data=data)

@router.post(
    "/store-group-download"
)
async def download_store_group_report(
    request_payload: store_group_models.StoreHierarchyFilter,
    user_id: UserDependency
):
    await store_group_service.download_store_group_report(request_payload, user_id)
    return common_utils.create_response(message=global_constants.REPORT_DOWNLOAD_MESSAGE)

@router.post(
    "/get-ungrouped-stores-details",
)
async def get_ungrouped_stores_details(
    ungrouped_stores_filter: store_group_models.UnGroupedStoresRead,
):
    """
    This Method will return the un grouped stores info.

    Param: only_meta_data : bool
    if it is set to False, will return the ungrouped stores.
    Exp:    [
                {   "store_id": int,
                    "store_name": str,
                    "s0_name": str,
                    "s1_name": str,
                    "s2_name": str,
                    "s3_name": str,
                    "city": str
                },
                {....}
            ]

    if it is set to True, Will return the numbers.
    Exp :   {
                "ungrouped_count": int,
                "ungrouped_percentage": int
            }
    """
    data = await store_group_service.get_ungrouped_stores(ungrouped_stores_filter)
    return common_utils.create_response(data=data)


@router.post(
    "/store-group",
)
async def insert_store_group(
    store_group: store_group_models.StoreGroupWrite,
    user_id: UserDependency
):
    await store_group_service.insert_store_group(store_group, user_id)
    return common_utils.create_response(
        message=f"Store Group {store_group.store_group_name} created successfully."
    )

@router.post(
    path="/store-group-process",
)
async def store_group_process(
    stores_info: store_group_models.StoreGroupProcess,
):
    await store_group_service.check_sg_process(stores_info.store_group_id)

    return common_utils.create_response(message="")

@router.get(
    "/store-group",
)
async def get_store_group(store_group_id: int):
    data = await store_group_service.get_store_group(store_group_id)
    return common_utils.create_response(data=data)

@router.get(
    "/store-groups/{store_group_id}/effected-promos"
)
async def get_effected_promos_for_store_group(store_group_id: int):
    data = await store_group_service.get_effected_promos_for_store_group(store_group_id)
    return common_utils.create_response(data=data)

@router.get(
    "/store-groups/{store_group_id}/promos",
)
async def get_store_group_promos(store_group_id: int):
    data = await store_group_service.get_store_group_promos(store_group_id)
    return common_utils.create_response(data=data)


@router.put(
    path="/store-group",
)
async def update_store_group(
    stores_info: store_group_models.StoreGroupUpdate,
    user_id: UserDependency,
    background_tasks: BackgroundTasks,
):
    await store_group_service.check_sg_process(stores_info.store_group_id)
    await store_group_service.sg_name_unique_check(stores_info)
    await store_group_service.update_sg_process(stores_info, 1)
    await store_group_service.strategies_promo_process_check(stores_info)

    background_tasks.add_task(
        store_group_service.update_store_group,
        stores_info,
        user_id,
    )
    return common_utils.create_response(
        message="Store Group edit started, will notify once completed"
    )

@router.put("/store-groups-delete")
async def delete_store_groups(
    sgs_info: store_group_models.StoreGroupsDelete,
    user_id: UserDependency
):
    await store_group_service.delete_store_groups(sgs_info, user_id)
    return common_utils.create_response(message="successfully deleted store groups")

@router.get(
    "/store-groups/{store_group_id}/stores",
)
async def get_store_details( store_group_id: int):
    data = await store_group_service.get_store_details_from_store_group(store_group_id)
    return common_utils.create_response(data=data)
