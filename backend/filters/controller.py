from fastapi import Request
from fastapi import status as http_status
from fastapi.routing import APIRouter
from filters import models as filter_models
from filters import service as filter_service
from pricesmart_common import constants as global_constants
from pricesmart_common import utils as common_utils

filters_router = APIRouter(tags=[global_constants.FILTERS_API_TAG])

@filters_router.post(
    path="/filters",
    response_model=filter_models.HierarchyFilterDataResponse,
)
async def get_hierarchies(items: filter_models.HierarchyFilters, request: Request):
    """
    Get hierarchy filter data.
    
    Args:
        items (HierarchyFilters): Filter criteria for hierarchies
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Hierarchy filter data
            - message (str): Success/error message
            - status (int): HTTP status code
            - user_id (str): User identifier
    """
    user_id = request.state.user_id

    result = await filter_service.get_hierarchy_service(items)
    return filter_models.HierarchyFilterDataResponse(
        message="Success", status=http_status.HTTP_200_OK, user_id=user_id, data=result
    )


@filters_router.post(
    path="/lifecycle",
    response_model=filter_models.HierarchyFilterDataResponse,
)
async def lifecycle_indicator(items: filter_models.HierarchyFilters, request: Request):
    """
    Get lifecycle indicator data.
    
    Args:
        items (HierarchyFilters): Filter criteria for lifecycle indicators
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Lifecycle indicator data
            - message (str): Success/error message
            - status (int): HTTP status code
            - user_id (str): User identifier
    """
    user_id = request.state.user_id
    result = await filter_service.get_lifecycle_indicators(items)
    return filter_models.HierarchyFilterDataResponse(
        message="Success", status=http_status.HTTP_200_OK, user_id=user_id, data=result
    )


@filters_router.post(
    path="/product-filters"
)
async def get_product_filters(
    request_payload: filter_models.ProductFiltersRequest
):
    """
        Get distinct values from product master table based on specified column and filters.
    """

    data = await filter_service.get_product_filters(request_payload)
    return common_utils.create_response(
        data=data
    )

@filters_router.post(
    path="/store-filters"
)
async def get_store_filters(
    request_payload: filter_models.StoreFiltersRequest
):
    data = await filter_service.get_store_filters(request_payload)
    return common_utils.create_response(
        data=data
    )


@filters_router.post(
    path="/lifecycle-indicators"
)
async def get_lifecycle_indicators(
    request_payload: filter_models.LifeCycleIndicatorsRequest
):
    data = await filter_service.get_lifecycle_indicators_v2(request_payload)
    return common_utils.create_response(
        data=data
    )

@filters_router.post("/currency-filter")
async def get_currency_filter(
    request_payload: filter_models.CurrencyFilter
):
    data = await filter_service.get_currency_filter(request_payload)
    return common_utils.create_response(
        data=data
    )
