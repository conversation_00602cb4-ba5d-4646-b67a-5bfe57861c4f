from filters import models, data as filters_data_module
from filters import models
from filters import constants as filter_constants

async def get_hierarchy_service(request_payload: models.HierarchyFilters):
    if request_payload.hierarchy_type in filter_constants.PRODUCT_HIERARCHIES:
        return await filters_data_module.get_product_hierarchy_data(request_payload)
    else:
        return await filters_data_module.get_store_hierarchy_data(request_payload)


async def get_lifecycle_indicators(request: models.HierarchyFilters):
    return await filters_data_module.get_lifecycle_indicators(request)


async def get_product_filters(request_payload: models.ProductFiltersRequest):
    return await filters_data_module.get_product_filters(request_payload)

async def get_lifecycle_indicators_v2(request_payload: models.LifeCycleIndicatorsRequest):
    return await filters_data_module.get_lifecycle_indicators_v2(request_payload)

async def get_store_filters(request_payload: models.StoreFiltersRequest):
    return await filters_data_module.get_store_filters(request_payload)

async def get_currency_filter(request_payload: models.CurrencyFilter):
    data = await filters_data_module.get_currency_filter(request_payload)
    return data