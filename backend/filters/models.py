import ast
from typing import Any, List, Optional

from fastapi import status as http_status
from pydantic import BaseModel, validator,Field


class BaseResponseBody(BaseModel):
    message: str
    status: int
    user_id: Optional[int]
    data: Optional[Any]

    def __init__(
        self,
        message: str = "",
        status: int = http_status.HTTP_200_OK,
        user_id: Optional[int] = None,
        data: Optional[Any] = None,
    ) -> None:
        super().__init__(message=message, status=status, user_id=user_id, data=data)


# Request Models
class ProductHierarchy(BaseModel):
    brand: Optional[List] = None
    color_id: Optional[List] = None
    style_color: Optional[List] = None
    l0_ids: Optional[List] = None
    l1_ids: Optional[List] = None
    l2_ids: Optional[List] = None
    l3_ids: Optional[List] = None
    l4_ids: Optional[List] = None
    l5_ids: Optional[List] = None


class StoreHierarchy(BaseModel):
    s0_ids: Optional[List] = None
    s1_ids: Optional[List] = None
    s2_ids: Optional[List] = None
    s3_ids: Optional[List] = None


class Hierarchy(BaseModel):
    product_hierarchy: Optional[ProductHierarchy] = None
    store_hierarchy: Optional[StoreHierarchy] = None


class HierarchyFilters(BaseModel):
    filters: Hierarchy
    hierarchy_type: object
    allow_only_active_products: bool = False
    channel_type_id: Optional[int] = None
    is_specific : bool = False
    event_id : Optional[int] = None

class LifeCycleIndicatorsRequest(BaseModel):
    hierarchy_filters: dict[str,list[int]] = Field(default_factory=dict)
    event_id: Optional[int] = None

class HierarchyFilter(BaseModel):
    product_hierarchy: Optional[ProductHierarchy]


# Response Models
class HierarchyFilterData(BaseModel):
    label: str
    value: object


class HierarchyFilterDataResponse(BaseResponseBody):
    data: List[HierarchyFilterData]


class ValidOffers(BaseModel):
    type: Optional[str]
    promo_id: Optional[int]
    promo_list: Optional[List[int]]
    overall: bool = False
    filters: object
    level: Optional[str]

    @validator("filters")
    def discount_type_values_check(cls, value, values):
        value_dict = ast.literal_eval(str(value))
        if values["overall"] is False and not value_dict["product_hierarchy"]:
            raise ValueError("Values missing for filters")
        return value


class ProductListRequest(BaseModel):
    product_hierarchy: Optional[ProductHierarchy]
    store_hierarchy: Optional[StoreHierarchy]
    event_id: Optional[int]


class BaseHierarchyFiltersRequest(BaseModel):
    hierarchy_filters: dict[str,list[int]] = Field(default_factory=dict)
    query_column: str
    event_id : Optional[int] = None


class ProductFiltersRequest(BaseHierarchyFiltersRequest):
    pass

class StoreFiltersRequest(BaseHierarchyFiltersRequest):
    pass

class CurrencyFilter(BaseModel):
    promo_id: Optional[int] = None
    country_ids: Optional[list[int]] = None