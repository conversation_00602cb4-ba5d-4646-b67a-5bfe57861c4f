from pricesmart_common.utils import async_execute_query, get_array_format
from configuration.environment import environment
from filters import constants as filter_constants
from filters import models as filter_models, queries as filter_queries
from filters.helpers import FilterModel
from events import data as event_data_module
from logger.logger import logger
from events.enums import EventProductSelectionType,EventStoreSelectionType
from enums.Enums import ConfigModuleEnum, ConfigKeyEnum
from pricesmart_common.data import get_config_value 
from filters.types import HierarchyFiltersInfoType
from pricesmart_common import constants as common_constants
from product import utils as product_utils


async def get_product_hierarchy_data(items: filter_models.HierarchyFilters):
    
    filters = items.filters.model_dump()
    filter_obj = FilterModel(filters)
    table_name = filter_constants.PRODUCT_TABLE
    table_alias = filter_constants.PRODUCT_TABLE_ALIAS
    build_config = {"table_name": table_name, "table_alias": table_alias}

    filter_obj.build_where_arr(build_config)
    filter_obj.build_where_str()

    select_name = filter_constants.HIERARCHY_NAME_MAPPING[items.hierarchy_type]
    select_id = filter_constants.HIERARCHY_ID_MAPPING[items.hierarchy_type]
    select_str = (
        f"{table_alias}.{select_id} as value, {table_alias}.{select_name} as label"
    )
    group_by_str = f"{table_alias}.{select_id}, {table_alias}.{select_name}"
    order_by_str = f"{table_alias}.{select_name}"
    not_null_condition = f"{table_alias}.{select_id} is not null"

    if items.allow_only_active_products:
        filter_obj.extend_where_str(f"{table_alias}.is_active = 1")
    
        
    filter_obj.extend_where_str(not_null_condition)
    where_str = filter_obj.where_str

    if items.event_id:
        event_data = await event_data_module.get_event_details(items.event_id,"product_inclusion_type")
    else:
        event_data = {}

    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        query = filter_queries.GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT_TYPE.format(
            select_str = select_str,
            table_name = table_name,
            table_alias = table_alias,
            where_str = where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
            event_id = items.event_id
        )
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        filter_obj.extend_where_str(
            f"""
                pm.product_id in (
                    select product_id 
                    from price_promo.included_event_products
                    where event_id = {items.event_id}
                )
            """
        )
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
            select_str=select_str,
            table_name=table_name,
            table_alias=table_alias,
            where_str=filter_obj.where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    else:
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
            select_str=select_str,
            table_name=table_name,
            table_alias=table_alias,
            where_str=where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    logger.info(query)
    result = await async_execute_query(query)
    return result

async def get_store_hierarchy_data(request_payload: filter_models.HierarchyFilters):
    
    filters = request_payload.filters.model_dump()
    filter_obj = FilterModel(filters)
    table_name = filter_constants.STORE_TABLE
    table_alias = filter_constants.STORE_TABLE_ALIAS

    filter_obj.build_where_arr(
        {"table_name": table_name, "table_alias": table_alias}
    )
    filter_obj.build_where_str()

    select_name = filter_constants.HIERARCHY_NAME_MAPPING[request_payload.hierarchy_type]
    select_id = filter_constants.HIERARCHY_ID_MAPPING[request_payload.hierarchy_type]
    not_null_condition = f"{table_alias}.{select_id} is not null"

    filter_obj.extend_where_str(not_null_condition)

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"store_selection_type")
    else:
        event_data = {}

    if (event_data.get("store_selection_type") or EventStoreSelectionType.ALL_STORES.value) != EventStoreSelectionType.ALL_STORES.value:
        filter_obj.extend_where_str(
            f"""
                {table_alias}.store_id
                in (
                    select store_id
                    from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                )
            """
        )
    
    query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
        select_str=(
            f"{table_alias}.{select_id} as value, {table_alias}.{select_name} as label"
        ),
        table_name=table_name,
        table_alias=table_alias,
        where_str=filter_obj.where_str,
        group_by_str=f"{table_alias}.{select_id}, {table_alias}.{select_name}",
        order_by_str=f"{table_alias}.{select_name}",
        client_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query)
    return result


async def get_lifecycle_indicators(request: filter_models.HierarchyFilters):
    filters = request.filters.model_dump()
    is_specific = request.is_specific
    allow_only_active_products = request.allow_only_active_products

    table_name = filter_constants.PRODUCT_TABLE
    table_alias = filter_constants.PRODUCT_TABLE_ALIAS
    select_id = "lifecycle_indicator"

    filter_obj = FilterModel(filters)
    build_config = {"table_name": table_name, "table_alias": table_alias}
    filter_obj.build_where_arr(build_config)
    filter_obj.build_where_str()

    not_null_condition = f"{table_alias}.{select_id} is not null"

    if allow_only_active_products:
        filter_obj.extend_where_str(
            f"{filter_constants.PRODUCT_TABLE_ALIAS}.is_active = 1"
        )

    filter_obj.extend_where_str(not_null_condition)

    if request.event_id:
        event_data = await event_data_module.get_event_details(request.event_id,"product_inclusion_type")
    else:
        event_data = {}
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        filter_obj.extend_where_str(
            f"""
            (
                not exists (
                    select 1 from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id} and hierarchy_level_id = -2
                ) or 
                gb.id in (
                    select 
                        hierarchy_value_id
                    from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id}
                    and hierarchy_level_id = -2
                )
            )
            """
        )
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        filter_obj.extend_where_str(
            f"""
                pm.product_id in (
                    select product_id
                    from price_promo.included_event_products
                    where event_id = {request.event_id} 
                )
            """
        )

    where_str = filter_obj.where_str
    curr_cte = None
    if is_specific:
        curr_cte = "specific_cte"
    else:
        curr_cte = "non_specific_cte"

    if request.event_id and event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        query = filter_queries.GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT.format(
            where_str=where_str,
            curr_cte=curr_cte,
            event_id=request.event_id
        )
    else:
        query = filter_queries.GET_LIFECYCLE_INDIACTORS.format(
            where_str=where_str, curr_cte=curr_cte
        )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_lifecycle_indicators_v2(request: filter_models.LifeCycleIndicatorsRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    where_condition = [
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request.hierarchy_filters,
            filter_constants.PRODUCT_TABLE_ALIAS
        )
    ] if request.hierarchy_filters else []

    event_data = (
        await event_data_module.get_event_details(request.event_id,"product_inclusion_type")
        if request.event_id
        else {}
    )

    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        lifecycle_indicator_level_id = await product_utils.get_lifecycle_indicator_level_id()
        where_condition.append(
            f"""
            (
                not exists (
                    select 1 from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id} and hierarchy_level_id = {lifecycle_indicator_level_id}
                ) or 
                gb.id in (
                    select 
                        hierarchy_value_id
                    from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id}
                    and hierarchy_level_id = {lifecycle_indicator_level_id}
                )
            )
            """
        )
    
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        where_condition.append(
            f"""
                pm.product_id in (
                    select product_id
                    from price_promo.included_event_products
                    where event_id = {request.event_id} 
                )
            """
        )


    if request.event_id and event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        return await get_lifecycle_indicators_based_on_whole_category_event(
            request.event_id,
            where_condition,
            hierarchy_filters_info
        )
    else:
        where_str = common_constants.AND.join(where_condition) if where_condition else "true"
        query = filter_queries.GET_LIFECYCLE_INDIACTORS_V2.format(
            where_str=where_str
        )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_lifecycle_indicators_based_on_whole_category_event(
    event_id: int,
    where_condition: list[str],
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType]
):
    where_condition.append(get_event_where_condition(hierarchy_filters_info,filter_constants.PRODUCT_TABLE_ALIAS))

    where_str = common_constants.AND.join(where_condition)
    query = filter_queries.GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT_V2.format(
        where_str=where_str,
        event_id=event_id,
        event_select_str=get_event_select_str(hierarchy_filters_info)
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_product_filters(request_payload: filter_models.ProductFiltersRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    where_condition = []
    where_condition.append(
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request_payload.hierarchy_filters,
            filter_constants.PRODUCT_TABLE_ALIAS
        )
    ) if request_payload.hierarchy_filters else []

    select_str = f"""
        {filter_constants.PRODUCT_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["value_column"]} as label,
        {filter_constants.PRODUCT_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["id_column"]} as value
    """

    order_by_str = f"""{hierarchy_filters_info[request_payload.query_column]["value_column"]}"""

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"product_inclusion_type")
    else:
        event_data = {}

    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        return (
            await get_product_filters_based_on_whole_category_event(
                request_payload,
                where_condition,
                select_str,
                order_by_str
            )
        )
        
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        where_condition.append(
            f"""
                {filter_constants.PRODUCT_TABLE_ALIAS}.product_id in (
                    select product_id 
                    from price_promo.included_event_products
                    where event_id = {request_payload.event_id}
                )
            """
        )
        where_str = common_constants.AND.join(where_condition)
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
            select_str=select_str,
            table_name=filter_constants.PRODUCT_TABLE,
            table_alias=filter_constants.PRODUCT_TABLE_ALIAS,
            value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
            where_str=where_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    else:
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
            select_str=select_str,
            table_name=filter_constants.PRODUCT_TABLE,
            table_alias=filter_constants.PRODUCT_TABLE_ALIAS,
            value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
            where_str=where_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    logger.info(query)
    result = await async_execute_query(query)
    return result

def generate_hierarchy_where_condition(
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType],
    hierarchy_filters: dict[str,list[int]],
    table_alias: str
):
    where_condition = []
    for hierarchy_filter_key, hierarchy_filter_values in hierarchy_filters.items():
        if not hierarchy_filter_values:
            continue

        id_column = hierarchy_filters_info[hierarchy_filter_key]["id_column"]
        where_condition.append(
            f""" 
                {table_alias}.{id_column} = any(
                    {get_array_format(hierarchy_filter_values)}::int[]
                )
            """
        )
    return common_constants.AND.join(where_condition)

async def get_product_filters_based_on_whole_category_event(
    request_payload: filter_models.ProductFiltersRequest,
    where_condition: list[str],
    select_str: str,
    order_by_str: str
):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )

    where_condition.append(
        get_event_where_condition(hierarchy_filters_info,filter_constants.PRODUCT_TABLE_ALIAS)
    )

    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    query = filter_queries.GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT.format(
        select_str = select_str,
        event_select_str = get_event_select_str(hierarchy_filters_info),
        table_name = filter_constants.PRODUCT_TABLE,
        table_alias = filter_constants.PRODUCT_TABLE_ALIAS,
        value_column = hierarchy_filters_info[request_payload.query_column]["value_column"],
        where_str = where_str,
        order_by_str=order_by_str,
        client_schema=environment.promo_schema,
        event_id = request_payload.event_id
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

def get_event_select_str(hierarchy_filters_info: dict[str,HierarchyFiltersInfoType]):
    return ",".join([
        f"array_agg(hierarchy_value_id) filter (where hierarchy_level_id = {hierarchy_filter['id']}) as {hierarchy_filter['id_column']}"
        for hierarchy_filter in hierarchy_filters_info.values()
        if hierarchy_filter["is_linked_to_event"]
    ])

def get_event_where_condition(hierarchy_filters_info: dict[str,HierarchyFiltersInfoType],table_alias: str):
    return " and ".join([
        f"(ehc.{hierarchy_filter['id_column']} is null or {table_alias}.{hierarchy_filter['id_column']} = any(ehc.{hierarchy_filter['id_column']}))"
        for hierarchy_filter in hierarchy_filters_info.values()
        if hierarchy_filter["is_linked_to_event"]
    ])

async def get_store_filters(request_payload: filter_models.StoreFiltersRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )

    where_condition = [
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request_payload.hierarchy_filters,
            filter_constants.STORE_TABLE_ALIAS
        )
    ] if request_payload.hierarchy_filters else []

    select_str = f"""
        {filter_constants.STORE_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["value_column"]} as label,
        {filter_constants.STORE_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["id_column"]} as value
    """

    order_by_str = f"""{hierarchy_filters_info[request_payload.query_column]["value_column"]}"""

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"store_selection_type")
    else:
        event_data = {}

    if (event_data.get("store_selection_type") or EventStoreSelectionType.ALL_STORES.value) != EventStoreSelectionType.ALL_STORES.value:
        where_condition.append(
            f"""
                {filter_constants.STORE_TABLE_ALIAS}.store_id
                in (
                    select store_id
                    from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                )
            """
        )
    
    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
        select_str=select_str,
        table_name=filter_constants.STORE_TABLE,
        table_alias=filter_constants.STORE_TABLE_ALIAS,
        value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
        where_str=where_str,
        order_by_str=order_by_str,
        client_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query)
    return result
    
async def get_currency_filter(request_payload: filter_models.CurrencyFilter):
    if request_payload.promo_id:
        query = filter_queries.GET_CURENCY_IDS_USING_PROMO_ID.format(
            promo_id = request_payload.promo_id
        )
    else:
        query = filter_queries.GET_CURRENCY_FILTER.format(
            country_ids = get_array_format(request_payload.country_ids)
        )
    data = await async_execute_query(query)
    return data