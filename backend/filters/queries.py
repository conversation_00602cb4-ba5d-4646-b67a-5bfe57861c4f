GET_HIERARCHY_FILTERS_QUERY = """ 
    select  
        {select_str}
    from 
        {client_schema}.{table_name} {table_alias}
    {where_str}
    group by 
    {group_by_str}
    order by 
        {order_by_str}
"""

GET_HIERARCHY_FILTERS_QUERY_V2 = """ 
    select  
        distinct {select_str}
    from 
        {client_schema}.{table_name} {table_alias}
    where 
        {value_column} is not null
        and
        {table_alias}.is_active = 1
        and
        {where_str}
    order by 
        {order_by_str}
"""

GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT_TYPE = """
    with event_hierarchy_cte as (
        select
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 0) as l0_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 1) as l1_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 2) as l2_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 3) as l3_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 4) as l4_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = -1) as brand_ids
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    select
        {select_str}
    from {client_schema}.{table_name} {table_alias},
    event_hierarchy_cte ehc
    {where_str}
    and (ehc.l0_ids is null or l0_cid = any(ehc.l0_ids))
    and (ehc.l1_ids is null or l1_cid = any(ehc.l1_ids))
    and (ehc.l2_ids is null or l2_cid = any(ehc.l2_ids))
    and (ehc.l3_ids is null or l3_cid = any(ehc.l3_ids))
    and (ehc.l4_ids is null or l4_cid = any(ehc.l4_ids))
    and (ehc.brand_ids is null or brand_cid = any(ehc.brand_ids))
    group by 
        {group_by_str}
    order by 
        {order_by_str}
"""


GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT = """
    with event_hierarchy_cte as (
        select
            {event_select_str}
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    select
        distinct {select_str}
    from {client_schema}.{table_name} {table_alias},
    event_hierarchy_cte ehc
    where {where_str}
    and {table_alias}.is_active = 1
    and {table_alias}.{value_column} is not null
    order by 
        {order_by_str}
"""

GET_LIFECYCLE_INDIACTORS = """
    with specific_cte as (
        SELECT 
            gb.id AS value,
            gb.lifecycle_indicator as label
        FROM
            global.tb_lifecycle_indicator_config gb
        JOIN
            price_promo.product_master pm ON gb.lifecycle_indicator = pm.lifecycle_indicator
        {where_str}
        group by 
            gb.id,
            gb.lifecycle_indicator
    ),
    non_specific_cte as (
            SELECT
                id as value,
                lifecycle_indicator as label
            FROM 
                global.tb_lifecycle_indicator_config
    )
    SELECT * FROM 
        {curr_cte}
    order by 
        value
"""

GET_LIFECYCLE_INDIACTORS_V2 = """
    SELECT 
        distinct
        gb.id AS value,
        gb.lifecycle_indicator as label
    FROM
        global.tb_lifecycle_indicator_config gb
    JOIN
        price_promo.product_master pm ON gb.lifecycle_indicator = pm.lifecycle_indicator
    where {where_str}
    order by value
"""


GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT = """
    with event_hierarchy_cte as (
        select
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 0) as l0_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 1) as l1_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 2) as l2_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 3) as l3_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 4) as l4_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = -1) as brand_ids
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    ),
    specific_cte as (
        SELECT 
            gb.id AS value,
            gb.lifecycle_indicator as label
        FROM
            global.tb_lifecycle_indicator_config gb
        JOIN
            price_markdown.product_master pm 
        ON gb.lifecycle_indicator = pm.lifecycle_indicator,
        event_hierarchy_cte ehc
        {where_str}
        and (ehc.l0_ids is null or l0_cid = any(ehc.l0_ids))
        and (ehc.l1_ids is null or l1_cid = any(ehc.l1_ids))
        and (ehc.l2_ids is null or l2_cid = any(ehc.l2_ids))
        and (ehc.l3_ids is null or l3_cid = any(ehc.l3_ids))
        and (ehc.l4_ids is null or l4_cid = any(ehc.l4_ids))
        and (ehc.brand_ids is null or brand_cid = any(ehc.brand_ids))
        group by 
            gb.id,
            gb.lifecycle_indicator
    ),
    non_specific_cte as (
        SELECT
            id as value,
            lifecycle_indicator as label
        FROM 
            global.tb_lifecycle_indicator_config
    )
    SELECT * FROM 
        {curr_cte}
    order by 
        value
"""

GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT_V2 = """
    with event_hierarchy_cte as (
        select
            {event_select_str}
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    SELECT 
        distinct
        gb.id AS value,
        gb.lifecycle_indicator as label
    FROM
        global.tb_lifecycle_indicator_config gb
    JOIN
        price_markdown.product_master pm 
    ON gb.lifecycle_indicator = pm.lifecycle_indicator,
    event_hierarchy_cte ehc
    where {where_str}
    order by value
"""

GET_CURRENCY_FILTER = """
    with country_currencies as (
        select 
            array_agg(currency_id) as currency_ids
        from 
            global.tb_country_currency_mapping 
        where 
            country_id = ANY({country_ids})
    )
    select 
        cm.currency_name as label,
        cm.currency_id as value
    from 
        price_promo.fn_get_target_currency_ids((select currency_ids from country_currencies)) tci
    inner join
        global.tb_currency_master cm on tci.target_currency_id = cm.currency_id
"""

GET_CURENCY_IDS_USING_PROMO_ID = """
    select 
        cm.currency_name as label,
        cm.currency_id as value
    from
        price_promo.fn_get_target_currency_ids(
            (select 
                array_agg(distinct currency_id) 
            from 
                price_promo.promo_master 
            where 
                promo_id = {promo_id}) 
        ) tci
    inner join
        global.tb_currency_master cm on tci.target_currency_id = cm.currency_id
"""