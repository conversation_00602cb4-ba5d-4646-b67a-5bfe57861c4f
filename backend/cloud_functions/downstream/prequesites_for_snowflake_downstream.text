-- All queries to be run on snowflake for each env once
-- <ENV>   ---> DEV/TEST/UAT/PROD
-- <BUCKET> ---> saks-dev/saks-test/saks-uat/saks-prod


-- 1. create a new storage integration in snowflake
-- DROP STORAGE INTEGRATION IF EXISTS SAKS_DOWNSTREAM_GCS_INTEGRATION_<ENV>
CREATE STORAGE INTEGRATION IF NOT EXISTS SAKS_DOWNSTREAM_GCS_INTEGRATION_<ENV>
TYPE = EXTERNAL_STAGE
STORAGE_PROVIDER = GCS
ENABLED = TRUE
STORAGE_ALLOWED_LOCATIONS = ('gcs://<BUCKET>/');


-- 2. Create a new Stage
-- DROP STAGE IF EXISTS SAKS_DOWNSTREAM_GCS_STAGE_<ENV>
CREATE STAGE IF NOT EXISTS SAKS_DOWNSTREAM_GCS_STAGE_<ENV>
  URL='gcs://<BUCKET>/'
  STORAGE_INTEGRATION = SAKS_DOWNSTREAM_GCS_INTEGRATION_<ENV>;


-- 3. Create a csv format which will skip headers, if any
CREATE OR REPLACE FILE FORMAT SAKS_CSV_FORMAT
  TYPE = 'CSV'
  FIELD_OPTIONALLY_ENCLOSED_BY = '"'
  SKIP_HEADER = 1
  ERROR_ON_COLUMN_COUNT_MISMATCH=FALSE
  NULL_IF = ('');
