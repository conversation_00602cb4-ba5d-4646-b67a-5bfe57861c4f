from google.cloud import secretmanager
import asyncpg
from json import loads
from settings import settings
import snowflake.connector
from snowflake.connector import Dict<PERSON><PERSON><PERSON>
from typing import Dict
from constants import SPECIAL_CHARACTER_MAPPING


def get_secret_data():
    client_secret_manager = secretmanager.SecretManagerServiceClient()
    SECRET_NAME = f"projects/{settings.SECRET_PROJECT}/secrets/{settings.SECRET_ID}/versions/latest"
    response = client_secret_manager.access_secret_version(name=SECRET_NAME)
    return loads(response.payload.data.decode("UTF-8"))  # type: ignore


SECRET_DATA = get_secret_data()


class DbConnection:
    async def __aenter__(self):
        self.conn = await asyncpg.connect(
            user=SECRET_DATA["db_user"],
            password=SECRET_DATA["db_password"],
            host=SECRET_DATA["db_host"],
            port=SECRET_DATA["db_port"],
            database=SECRET_DATA["db_name"]
        )
        return self.conn

    async def __aexit__(self, exc_type, exc_value, exc_tb):
        await self.conn.close()


class SnowflakeConnectivity:

    def __init__(self, connection_params = None):
        self.conn = self.connect(connection_params)

    def connect(self, connection_params=None):
        if connection_params:
            print('from connection params')
            user_name = connection_params.get('user')
            password = connection_params.get('password')
            account_id = connection_params.get('account')
            warehouse = connection_params.get('warehouse')
            database = connection_params.get('database')
            schema = connection_params.get('schema')
        else:
            print('secret present')
            user_name = SECRET_DATA["snowflake_user"]
            password = SECRET_DATA["snowflake_password"]
            account_id = SECRET_DATA["snowflake_account"]
            warehouse = SECRET_DATA["snowflake_warehouse"]
            database = SECRET_DATA["snowflake_database"]
            schema = SECRET_DATA["snowflake_schema"]

        self.conn = snowflake.connector.connect(
            user=user_name,
            password=password,
            account=account_id,
            warehouse=warehouse,
            database=database,
            schema=schema
        )
        return self.conn

    def execute_query(self, query):
        cur = self.conn.cursor(DictCursor)
        cur.execute(query)
        return cur.fetchall()

    def import_from_gcs(self, stage_name, file_name, table_name, file_format):
        query = f"""
            COPY INTO {table_name} 
            FROM @{stage_name}/{file_name}
            FILE_FORMAT = {file_format}; 
        """
        print(query)
        return self.execute_query(query=query)


def replace_imputed_characters(value: str):
    for special_char, replacement in SPECIAL_CHARACTER_MAPPING.items():
        value = value.replace(special_char, replacement)
    return value


# Special Character Imputation function
def impute_special_characters(data: Dict) -> Dict:
    new_data = {}
    for key, value in data.items():
        # Apply the function to both keys and values
        new_key = replace_imputed_characters(key) if isinstance(key, str) else key
        new_value = replace_imputed_characters(value) if isinstance(value, str) else value
        new_data[new_key] = new_value
    return new_data