import functions_framework
from data import process_promo_downstream_data, markdown_downstream_integration
import asyncio


@functions_framework.http
def downstream_function(request):
    request_json = request.get_json(silent=True)
    print('got hit with request....', request_json)

    if request_json['source'] == 'markdown_integration':
        response_data = asyncio.run(markdown_downstream_integration())
        response_data = [dict(item) for item in response_data]
        print(response_data)
        return response_data
    else:
        promo_id_list = request_json["promo_ids"]
        action_type = request_json["action"]
        print(promo_id_list, action_type)

        try:
            data = asyncio.run(process_promo_downstream_data(promo_id_list,action_type))
        except Exception as e:
            print('Exception -- ', e)
            return {"status": "failed"}
    return {"status": "success"}

