[local]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = saks-dev
SECRET_PROJECT = saksfifthavenue-********
SECRET_ID = saksfifthavenue_dev
SNOWFLAKE_STAGE = SAKS_DOWNSTREAM_GCS_STAGE_DEV
PRODUCT_GROUP_DETAILS_TABLE = PRODUCT_GROUP_DETAILS_DEV
OFFER_EXECUTION_DETAILS_TABLE = OFFER_EXECUTION_DETAILS_DEV
PCW_INTEGRATION_DETAILS_TABLE = PCW_INTEGRATION_DETAILS_DEV

[dev]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = saks-dev
API_ENDPOINT = https://pricesmart-biglots-dev.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = saksfifthavenue-********
SECRET_ID = saksfifthavenue_dev
SNOWFLAKE_STAGE = SAKS_DOWNSTREAM_GCS_STAGE_DEV
PRODUCT_GROUP_DETAILS_TABLE = PRODUCT_GROUP_DETAILS_DEV
OFFER_EXECUTION_DETAILS_TABLE = OFFER_EXECUTION_DETAILS_DEV
PCW_INTEGRATION_DETAILS_TABLE = PCW_INTEGRATION_DETAILS_DEV

[test]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = saks-test
SECRET_PROJECT = saksfifthavenue-********
SECRET_ID = saksfifthavenue_test
SNOWFLAKE_STAGE = SAKS_DOWNSTREAM_GCS_STAGE_TEST
PRODUCT_GROUP_DETAILS_TABLE = PRODUCT_GROUP_DETAILS_TEST
OFFER_EXECUTION_DETAILS_TABLE = OFFER_EXECUTION_DETAILS_TEST
PCW_INTEGRATION_DETAILS_TABLE = PCW_INTEGRATION_DETAILS_TEST

[uat]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = saks-uat
SECRET_PROJECT = saksfifthavenue-********
SECRET_ID = saksoff5th_uat
SNOWFLAKE_STAGE = SAKS_DOWNSTREAM_GCS_STAGE_UAT
PRODUCT_GROUP_DETAILS_TABLE = PRODUCT_GROUP_DETAILS_UAT
OFFER_EXECUTION_DETAILS_TABLE = OFFER_EXECUTION_DETAILS_UAT
PCW_INTEGRATION_DETAILS_TABLE = PCW_INTEGRATION_DETAILS_UAT

[prod]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = saks-prod
SECRET_PROJECT = saksfifthavenue-********
SECRET_ID = saksoff5th_prod
SNOWFLAKE_STAGE = SAKS_DOWNSTREAM_GCS_STAGE_PROD
PRODUCT_GROUP_DETAILS_TABLE = PRODUCT_GROUP_DETAILS
OFFER_EXECUTION_DETAILS_TABLE = OFFER_EXECUTION_DETAILS
PCW_INTEGRATION_DETAILS_TABLE = PCW_INTEGRATION_DETAILS

