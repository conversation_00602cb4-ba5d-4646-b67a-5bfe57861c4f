import datetime
import io
from io import StringIO

import pandas as pd
import pytz
import queries as dws_queries
from google.cloud import storage
from jinjasql import JinjaSql
from settings import environment, settings
from utils import DbConnection, SnowflakeConnectivity, impute_special_characters


async def process_promo_downstream_data(promo_ids, action):
    is_pg_ids_empty = False
    jinja_sql = JinjaSql(param_style="asyncpg")
    print(promo_ids, action)

    # Product Group data save to postgres
    save_product_group_data_query, pg_bind_params = jinja_sql.prepare_query(
        dws_queries.SAVE_DOWNSTREAM_PRODUCT_GROUP_DATA_POSTGRES_QUERY,
        {"promo_ids": promo_ids},
    )
    print("pg data query -- ", save_product_group_data_query)

    # Offer data save to postgres
    save_offer_data_query, offer_bind_params = jinja_sql.prepare_query(
        dws_queries.SAVE_DOWNSTREAM_OFFER_DATA_POSTGRES_QUERY,
        {"promo_ids": promo_ids, "action": action},
    )
    print("offer data query -- ", save_offer_data_query)

    async with DbConnection() as conn:
        pg_data = await conn.fetch(save_product_group_data_query, *pg_bind_params)
        record_pg_data = pg_data
        pg_ins_ids = dict(pg_data[0])["pg_ids"] if pg_data else None
        print("inserted row ids for pg_ids ============ ", pg_ins_ids)
        offer_data = await conn.fetch(save_offer_data_query, *offer_bind_params)
        offer_ins_ids = dict(offer_data[0])["offer_ids"] if offer_data else None
        print("inserted row ids for offers ============ ", offer_ins_ids)

    if pg_ins_ids:
        # Get product group data from postgres
        get_pg_data_query, pg_data_bind_params = jinja_sql.prepare_query(
            dws_queries.GET_PRODUCT_GROUP_DATA_QUERY, {"pg_ins_ids": pg_ins_ids}
        )
        print("get pg data query -- ", get_pg_data_query)
        async with DbConnection() as conn:
            pg_data = await conn.fetch(get_pg_data_query, *pg_data_bind_params)
            # print("pg data -- ", pg_data)

    if offer_ins_ids:
        # Get offer data from postgres
        get_offer_data_query, offer_data_bind_params = jinja_sql.prepare_query(
            dws_queries.GET_OFFER_DATA_QUERY, {"offer_ins_ids": offer_ins_ids}
        )
        print("get offer data query -- ", get_offer_data_query)
        async with DbConnection() as conn:
            offer_data = await conn.fetch(get_offer_data_query, *offer_data_bind_params)
            # print("offer data -- ", offer_data)

    if record_pg_data is None or len(record_pg_data) == 0:
        is_pg_ids_empty = True
    else:
        # Check if all 'pg_ids' in each Record is None
        is_pg_ids_empty = all(row["pg_ids"] is None for row in record_pg_data)

    # Convert results to CSV
    pg_csv_data = convert_to_csv(pg_data)
    offer_csv_data = convert_to_csv(offer_data)

    # Upload CSV data to Google Cloud Storage
    bucket_name = settings.BUCKET_NAME
    pg_blob_name = f"promo_downstream_data/pg_data{datetime.datetime.now(datetime.UTC).strftime('%Y%m%d_%H%M%S')}.csv"
    offer_blob_name = f"promo_downstream_data/offer_data{datetime.datetime.now(datetime.UTC).strftime('%Y%m%d_%H%M%S')}.csv"
    upload_to_gcs(bucket_name, pg_blob_name, pg_csv_data)
    upload_to_gcs(bucket_name, offer_blob_name, offer_csv_data)

    print(environment, " env")
    print(settings.PRODUCT_GROUP_DETAILS_TABLE)
    # Push data to Snowflake
    snowflake_connectivity = SnowflakeConnectivity()
    pg_q = dws_queries.PUSH_PG_DATA_TO_SNOWFLAKE_TABLE_QUERY.format(
        pg_blob_name=pg_blob_name,
        stage=settings.SNOWFLAKE_STAGE,
        table_name=settings.PRODUCT_GROUP_DETAILS_TABLE,
    )
    off_q = dws_queries.PUSH_OFFER_DATA_TO_SNOWFLAKE_TABLE_QUERY.format(
        offer_blob_name=offer_blob_name,
        stage=settings.SNOWFLAKE_STAGE,
        table_name=settings.OFFER_EXECUTION_DETAILS_TABLE,
    )
    print("env", environment)
    print(pg_q)
    print(off_q)
    # snowflake_connectivity.execute_query(query=pg_q)
    if not is_pg_ids_empty:
        snowflake_connectivity.execute_query(query=pg_q)
    snowflake_connectivity.execute_query(query=off_q)
    print("data pushed successfully")

    return pg_blob_name, offer_blob_name


def convert_to_csv(data):
    print("converting to df")

    # Convert data to a Pandas DataFrame
    print(data)
    # df = pd.DataFrame(data)
    df = pd.DataFrame([impute_special_characters(dict(record)) for record in data])

    # Convert DataFrame to CSV format in memory
    csv_buffer = StringIO()
    df.to_csv(csv_buffer, index=False, header=True)
    return csv_buffer.getvalue()


def upload_to_gcs(bucket_name, blob_name, csv_data):
    # Initialize a GCS client
    client = storage.Client()
    bucket = client.bucket(bucket_name)

    # Create a new blob and upload the CSV data
    blob = bucket.blob(blob_name)
    blob.upload_from_string(csv_data, content_type="text/csv")

    print(f"File uploaded to {blob_name} in bucket {bucket_name}.")


async def markdown_downstream_integration():
    today = datetime.datetime.now(pytz.timezone("Asia/Kolkata"))
    today_str = today.strftime("%m%d%Y")
    print(today_str)
    current_partition = f"price_markdown.tb_snowflake_data_{today_str}"

    # Create partition for the table
    partition_query = dws_queries.MARKOWN_TABLE_PARTITION_QUERY

    # Populate the data with the data to be updated today
    data_populating_query = dws_queries.MARKDOWN_TABLE_DATA_POPULATION_QUERY

    # Retrieve the data
    data_retrieval_query = dws_queries.MARKDOWN_DATA_FETCHING_QUERY.format(
        current_partition=current_partition
    )
    print("data_retrieval_query=", data_retrieval_query)

    async with DbConnection() as conn:
        await conn.fetch(partition_query)
        print("Partition created successfully")
        response = await conn.fetch(data_populating_query)
        print("Data Populated successfully")
        data = await conn.fetch(data_retrieval_query)
        print("Data fetched from the table")

    csv_data = convert_to_csv(data)
    print("Data written in CSV file")

    bucket_name = settings.BUCKET_NAME
    blob_name = f"markdown_downstream_data/snowflake_data{today_str}.csv"
    upload_to_gcs(bucket_name, blob_name, csv_data)
    print("File Uploaded")

    snowflake_connectivity = SnowflakeConnectivity()
    print("Connected to snowflake")
    md_q = dws_queries.PUSH_MARKDOWN_DATA_TO_SNOWFLAKE_TABLE.format(
        blob_name=blob_name,
        stage=settings.SNOWFLAKE_STAGE,
        table_name=settings.PCW_INTEGRATION_DETAILS_TABLE,
    )

    print(md_q)
    snowflake_connectivity.execute_query(query=md_q)
    print("data pushed for markdown successfully")
    return response
