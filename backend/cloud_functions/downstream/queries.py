SAVE_DOWNSTREAM_OFFER_DATA_POSTGRES_QUERY = """
    select price_promo.fn_generate_execution_metadata_offer_level_data(array{{ promo_ids | sqlsafe}}::integer[], '{{ action | sqlsafe}}') as offer_ids;
"""


SAVE_DOWNSTREAM_PRODUCT_GROUP_DATA_POSTGRES_QUERY = """ 
    select price_promo.fn_generate_execution_metadata_product_group_data(array{{ promo_ids | sqlsafe}}::integer[]) as pg_ids;
"""


GET_OFFER_DATA_QUERY = """ 
    select 
        ia_offer_id, offer_name, start_date, end_date, inclusion_product_group_id, exclusion_product_group_id, template_id, price_filter, target_folder, location_list,
        eligibility_condition, eligibility_condition_value, limits, discount_type, discount_value, no_of_tiers, tier, promo_code, receipt_text_eng, receipt_text_fr, sfcc_pip_text, 
        sfcc_drop_ship, sfcc_tender_type_promo_msg, sfcc_pip_customer_group, sfcc_customer_group, sfcc_pip_rank, sfcc_rank, sfcc_ats_check, promo_action, status, deploy  
    from 
        price_promo.so5_integration_offer_execution_details 
    where   
        id = any({{ offer_ins_ids}})
"""


GET_PRODUCT_GROUP_DATA_QUERY = """
    select 
        product_group_id, name, department, class, mfg, svs, action 
    from
        price_promo.so5_integration_product_group_details 
    where 
        id = any({{ pg_ins_ids}})
"""


PUSH_PG_DATA_TO_SNOWFLAKE_TABLE_QUERY = """
    COPY INTO {table_name} (PRODUCT_GROUP_ID, NAME, DEPARTMENT, CLASS, MFG, SVS, ACTION)
    FROM @{stage}/{pg_blob_name}
    FILE_FORMAT = SAKS_CSV_FORMAT;
"""



PUSH_OFFER_DATA_TO_SNOWFLAKE_TABLE_QUERY = """
    COPY INTO
    {table_name} (
        IA_OFFER_ID, OFFER_NAME, START_DATE, END_DATE, INCLUSION_PRODUCT_GROUP_ID, EXCLUSION_PRODUCT_GROUP_ID, 
        TEMPLATE_ID, PRICE_FILTER, TARGET_FOLDER, LOCATION_LIST, ELIGIBILITY_CONDITION, ELIGIBILITY_CONDITION_VALUE, LIMITS, DISCOUNT_TYPE, DISCOUNT_VALUE, NO_OF_TIERS,
        TIER, PROMO_CODE, RECIEPT_TEXT_ENG, RECIEPT_TEXT_FR, SFCC_PIP_TEXT, SFCC_DROP_SHIP, SFCC_TENDER_TYPE_PROMO_MSG, SFCC_PIP_CUSTOMER_GROUP, SFCC_CUSTOMER_GROUP, 
        SFCC_PIP_RANK, SFCC_RANK, SFCC_ATS_CHECK, PROMO_ACTION, STATUS, DEPLOY)
    FROM @{stage}/{offer_blob_name}
    FILE_FORMAT = SAKS_CSV_FORMAT;
"""

MARKOWN_TABLE_PARTITION_QUERY = """
        select * from price_markdown.fn_create_snowflake_data_partition()
"""

MARKDOWN_TABLE_DATA_POPULATION_QUERY = """
        select * from price_markdown.fn_send_data_to_snowflake()
"""

MARKDOWN_DATA_FETCHING_QUERY = """
        select
            sku_id as "SKU",
            store_id as "STORE",
            effective_date as "EFFECTIVE_DATE",
            price as "PRICE",
            price_status as "PRICE_STATUS",
            action as "ACTION",
            updated_by as "UPDATED_BY",
            updated_at as "UPDATED_AT",
            strategy_id as "STRATEGY_ID",
            pcd_id as "PCD_ID",
            style_id as "STYLE_ID"
        from 
            {current_partition}
"""

PUSH_MARKDOWN_DATA_TO_SNOWFLAKE_TABLE = """
        COPY INTO
            {table_name} (SKU, STORE, EFFECTIVE_DATE, PRICE, PRICE_STATUS, ACTION, UPDATED_BY, UPDATED_AT, STRATEGY_ID, PCD_ID, STYLE_ID)
        FROM @{stage}/{blob_name}
        FILE_FORMAT = SAKS_CSV_FORMAT;
"""