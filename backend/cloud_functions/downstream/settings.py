from pydantic import BaseModel
import configparser
import os


class Settings(BaseModel):
    SERVICE_ACCOUNT_EMAIL: str
    BUCKET_NAME: str
    SECRET_PROJECT: str
    SECRET_ID: str
    SNOWFLAKE_STAGE: str
    PRODUCT_GROUP_DETAILS_TABLE : str
    OFFER_EXECUTION_DETAILS_TABLE : str
    PCW_INTEGRATION_DETAILS_TABLE : str


config = configparser.ConfigParser()
config.optionxform = str # type: ignore

config.read("environment.ini")


environment = os.getenv("ENV") or "local"
print(f"Environment: {environment}")
config = {**config[environment]}
settings = Settings(**config)