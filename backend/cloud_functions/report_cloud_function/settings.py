from pydantic import BaseModel
import configparser
import os

class Settings(BaseModel):
    SERVICE_ACCOUNT_EMAIL: str
    BUCKET_NAME : str
    API_ENDPOINT : str
    SECRET_PROJECT : str
    SECRET_ID : str


config = configparser.ConfigParser()
config.optionxform = str # type: ignore

config.read("environment.ini")


environment = os.getenv("ENV") or "local"
config = {**config[environment]}
settings = Settings(**config)