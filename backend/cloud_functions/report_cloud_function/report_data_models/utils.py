from asyncio import create_task
from datetime import datetime,timedelta
import logging

import xlsxwriter
from report_data_models.data_models_store import DATA_MODELS
from jinjasql import JinjaSql
from typing import Dict, List
from google.cloud import secretmanager
from google.cloud import storage
from io import BytesIO
import asyncpg
import os
from json import loads,dumps
from google.auth import compute_engine
from google.auth.transport.requests import Request
import requests
from settings import settings
import gc



def get_secret_data():
    client_secret_manager = secretmanager.SecretManagerServiceClient()
    SECRET_NAME = f"projects/{settings.SECRET_PROJECT}/secrets/{settings.SECRET_ID}/versions/latest"
    response = client_secret_manager.access_secret_version(name=SECRET_NAME)
    return loads(response.payload.data.decode("UTF-8")) # type: ignore

SECRET_DATA = get_secret_data()


class DbConnection:

    async def __aenter__(self):
        self.conn = await asyncpg.connect(
            user = SECRET_DATA["db_user"],
            password = SECRET_DATA["db_password"],
            host = SECRET_DATA["db_host"],
            port = SECRET_DATA["db_port"],
            database = SECRET_DATA["db_name"]
        )
        return self.conn

    async def __aexit__(self, exc_type, exc_value, exc_tb):
        await self.conn.close()





async def process_model_id(id,parameters: Dict):
    jinja_sql = JinjaSql(param_style="asyncpg")

    
    

    model_data = DATA_MODELS[id]
    parameters_data = parameters.copy()
    if parameters_data.get("start_date") and parameters_data.get("end_date"):
        parameters_data["start_date"] = datetime.strptime(parameters_data["start_date"],"%m/%d/%Y").date() # type: ignore
        parameters_data["end_date"] = datetime.strptime(parameters_data["end_date"],"%m/%d/%Y").date() # type: ignore
    query,bind_params = jinja_sql.prepare_query(model_data["query"],parameters_data)

    async with DbConnection() as conn:
        data = await conn.fetch(query,*bind_params)
    return data


async def process_model_ids(id_list: List[str], parameters: Dict, user_id:int):
    try:
        report_name = parameters["report_name"]
        model_data_result = {}
        for id in id_list:
            model_data_result[id] =  create_task(process_model_id(id,parameters))

        for id in id_list:
            model_data_result[id] =  await model_data_result[id]

        sheet_data = {
            DATA_MODELS[id]["sheet-name"] : model_data_result[id]
            for id in id_list
        }

        output = BytesIO()

        workbook = xlsxwriter.Workbook(output,{"default_date_format":"yyyy-mm-dd"})

        create_excel(workbook,sheet_data)

        workbook.close()
        xlsx_data = output.getvalue()

        blob = get_blob(user_id,report_name)

        write_blob(blob,xlsx_data)

        signed_url = get_signed_url(blob)
        message = f"{report_name} is ready for download"
        notification_data = await save_notification(signed_url,message,user_id)
        notification_data["status"] = 200
        print(requests.post(settings.API_ENDPOINT,json = {"notification_data":notification_data,"user_id":user_id}))

        # deleting data for garbage collection to collect
        del sheet_data
        del blob
        del output
        del workbook
        gc.collect()

        return "success"
    except Exception:
        logging.exception("error")
        report_name = parameters["report_name"]
        message = f"Something went wrong while processing the {report_name}"
        notification_data = await save_notification(None,message,user_id)
        notification_data["status"] = 500
        print(requests.post(settings.API_ENDPOINT,json = {"notification_data":notification_data,"user_id":user_id}))
        return "failure"


async def save_notification(url,message,user_id):
    async with DbConnection() as conn:
        res = dict((await conn.fetch(
            '''
            with report_url_record as (
                insert into public.report_urls (url) values ($1)
                returning report_id
            )
            insert into public.tb_notifications (module,message,action,navigate_to,user_id)
            select $2,$3,$4,report_id,$5 from report_url_record
            returning action,created_at,is_active,life_span,message,module,navigate_to,priority,read_at,read_by,severity,uuid
            ''',
            url,"REPORT",message,"download",user_id
        ))[0])
    print("saved_notification")
    res["url"] = url
    return loads(dumps(res,default=str))



def create_excel(workbook: xlsxwriter.Workbook,data_dict):
    for sheet_name, data in data_dict.items():
        if not data:
            continue
        sheet = workbook.add_worksheet(sheet_name)

        # Write the headers for the query
        sheet.write_row(row=0,col=0,data=list(data[0].keys()))

        # Write the data
        for index,r in enumerate(data,1):
            sheet.write_row(row = index,col=0,data = list(r.values()))





def write_blob(blob,xlsx_data):
    with blob.open("wb") as f:
        f.write(xlsx_data)


def get_blob(user_id,report_name):
    report_name = get_formatted_report_name(report_name) 
    blob_name = f"user_reports/{user_id}/{report_name}"
    storage_client = storage.Client()
    bucket = storage_client.bucket(settings.BUCKET_NAME)
    return bucket.blob(blob_name)


def get_signed_url(blob):
    if os.getenv("ENV","local") == "local":
        return "no_url" 
    http_request = Request()
    signing_credentials = compute_engine.IDTokenCredentials(
        http_request,
        "",
        service_account_email = settings.SERVICE_ACCOUNT_EMAIL
    )
    signed_url = blob.generate_signed_url(
        version="v4",
        expiration=timedelta(hours=72),
        credentials=signing_credentials,
    )

    return signed_url



def get_formatted_report_name(report_name):
    return f'{report_name}_{datetime.now().strftime("%m-%d-%Y-%H-%M-%S")}.xlsx'