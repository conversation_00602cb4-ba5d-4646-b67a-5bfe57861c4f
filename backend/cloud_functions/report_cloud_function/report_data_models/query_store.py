

SAP_EXTRACT = """select start_date as "Start Date",end_date as "End Date",Ad_Zone_Code as "Ad Zone Code", SAP_Subclass as "SAP Subclass", Ad_Zone as "Ad Zone (Chain or specific DC)",
                offer_name as "Preliminary Offer Name", Offer_Type as "Offer Type",div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", 
                class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",AD_Feature_Item as "AD Feature Item",
                Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",
                cost as "Cost",current_retail as "Current Retail","IMU" as "IMU %",
                "disc_from_Current_Retail" as "% Discount from Current Retail",
                event_retail as "Event Retail",
                case when "Discount_from_Event_Retail"<0 then 0 else  "Discount_from_Event_Retail" end as "% Discount from Event Retail",
                case when aur>event_retail then event_retail else aur end  as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
                baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
                forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
                unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
                AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
                Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, start_date,end_date, Offer_Type, sap_promo_level,
                div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,
                AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,
                round((Cost::Decimal),2) as Cost,
                round((current_retail::Decimal),2) as current_retail,
                round((IMU::Decimal),2) as "IMU",
                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                round((event_retail::Decimal),2) as event_retail,
                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                coalesce (round((aur::Decimal),2),0) as aur,
                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                round((baseline_sales::Decimal),2) as baseline_sales,
                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                round(( forecast_sales::Decimal),2) as forecast_sales,
                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
                round(( sales_diff::Decimal),2) as sales_diff,
                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                round((margin_diff::Decimal),2) as margin_diff,
                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                Comments_m, Marketing_Specific_Comments
        from (
        select Ad_Zone_Code, SAP_Subclass,
                'CHAIN' as Ad_Zone,
                offer_name, sap_promo_level,
                start_date,end_date,
                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
                        else 'BOGO (APR Offer)' end as Offer_Type,
                div_cd, dept_cd , dept_nm ,
                class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,
                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
                "Status",GRM_Flow,
                cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when  Offer_Type = 'fixed_price' then offer_value 
                when  Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else  current_retail end as event_retail,
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select case when sap_promo_level in (1,2,3) then null else SAP_Subclass end as SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name,start_date,end_date,Offer_Type ,
        div_cd, 
        case when sap_promo_level=1 then null else dept_cd end as dept_cd, 
        case when sap_promo_level=1 then null else dept_nm end as dept_nm,
        case when sap_promo_level in (1,2) then null else class_cd end as  class_cd, 
        case when sap_promo_level in (1,2) then null else class_nm end as class_nm,
        case when sap_promo_level in (1,2,3) then null else subcls_cd end as subcls_cd,
        case when sap_promo_level in(1,2,3) then null else subcls_nm end as subcls_nm,
        case when sap_promo_level=5 then Sku else null end as Sku,
        case when sap_promo_level=5 then descr else null end as descr,promo_id, sap_promo_level,
        case when sap_promo_level=5 then AD_Feature_Item else null end as AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
                max(Ad_Zone_Code) as Ad_Zone_Code,
                max(IMU) as IMU ,
                max(disc_from_CR) as disc_from_Current_Retail,
                max(event_retail) as event_retail,
                max(disc_from_ER) as disc_from_ER,
                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                sum(baseline_sales_units) as baseline_sales_units,
                avg(baseline_sales_all_store) as baseline_sales_all_store,
                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                sum(baseline_sales) as baseline_sales ,
                sum(baseline_GM_$) as baseline_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                sum(forecast_sales_units) as forecast_sales_units,
                avg(forecast_sales_all_store) as forecast_sales_all_store,
                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                sum(forecast_sales) as forecast_sales,
                sum(forecast_event_MD) as forecast_event_MD,
                sum(forecast_GM_$) as forecast_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
                sum(sales_diff) as sales_diff,
                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                sum(margin_diff) as margin_diff,
                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                max(Sell_Through) as Sell_Through,
                max(Comments_m) as Comments_m,
                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
                pm.name as offer_name,
                pm.start_date,pm.end_date,
                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id, case when pm.sap_promo_level is null then 5 when pm.sap_promo_level<=3 then 3 else pm.sap_promo_level end as sap_promo_level,case when tasm.name  in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                im.product_h5_id as Sku, im.item_nm as descr,
                null as AD_Feature_Item,
                null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
                t.recommendation_date as dt,
                im.cost as cost,
                im.price as current_retail,
                '1 - cost/CR' as IMU,
                'use case when on %off' as disc_from_CR,
                'CR * (1-%offfromCR)' as event_retail,
                '1 - AUR/ER' as disc_from_ER,
                t.aur as aur, t.baseline_sales_units,
                1435 as baseline_sales_all_store,
                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                t.baseline_revenue as baseline_sales,
                t.baseline_margin as baseline_GM_$,
                0 as UPAs_build,
                t.sales_units as forecast_sales_units,
                1435 as forecast_sales_all_store,
                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
                'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t
        inner join (
                select event_id , start_date , end_date from public.event_master 
                {% if event_id %}
                where event_id in {{event_id | inclause}}
                {% endif %}
                group by 1,2,3
                ) as em
        on t.event_id = em.event_id
        inner join (
                select * from public.promo_master where status=4 
                {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
                {% endif %}
        ) as pm
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                                product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price
                        From public.product_master
                        where product_h2_id in {{product_h2 | inclause}}
                ) as im
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm 
        on t.offer_type_id = tasm.id 
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21
        ) as b
        left join  
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01"""



SKU_EXTRACT = """
            with base as
                (select Ad_Zone_Code,event_name,cast(start_date as date) as start_date,cast(end_date as date) as end_date, SAP_Subclass, Ad_Zone, offer_name, Offer_Type,tool_offer_type,
                div_cd,div_nm, dept_cd ,dept_nm, class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
                AD_Feature_Item,event_objective,ad_type,channel,
                round((Cost::Decimal),2) as Cost,
                round((current_retail::Decimal),2) as current_retail,
                round((IMU::Decimal),2) as "IMU",
                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                round((event_retail::Decimal),2) as event_retail,
                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                coalesce (round((aur::Decimal),2),0) as aur,
                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                round((baseline_sales::Decimal),2) as baseline_sales,
                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                round(( forecast_sales::Decimal),2) as forecast_sales,
                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                round(( forecast_sales_units_diff::Decimal),2) as forecast_sales_units_diff,
                round(( sales_diff::Decimal),2) as sales_diff,
                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                round((margin_diff::Decimal),2) as margin_diff,
                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                Comments_m, Marketing_Specific_Comments,
                vendor_funding_amount, 
                vendor_funding_per_unit, 
                vendor_funding_type 
                from (
                select Ad_Zone_Code, SAP_Subclass, start_date,end_date,
                'CHAIN' as Ad_Zone,event_objective,
                event_name,offer_name,
                case when (tool_offer_type = 'percent_off') then 'percent_off'
                when (tool_offer_type = 'fixed_price') then 'fixed_price'
                when (tool_offer_type = 'extra_amount_off') then 'extra_amount_off'
                when (tool_offer_type = 'reg_price_cv') then 'reg_price_cv'
                when (tool_offer_type = 'reg_price') then 'reg_price'
                when (tool_offer_type = 'bxgy') then 'bxgy'
                when (tool_offer_type = 'bundle_offer') then 'bundle_offer'
                when (tool_offer_type = 'bxgy_$') then 'bxgy_$'
                when (tool_offer_type = 'bmsm_$') then 'bmsm_$'
                else 'BOGO (APR Offer)' end as tool_offer_type,
                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'reg_price'
                else 'BOGO (APR Offer)' end as Offer_Type,
                case when (ad_type = '1') then 'Advertised'
                    when (ad_type = '2') then 'Unadvertised' end as ad_type,
                case when (channel_type = '22') then 'Ecomm Only'
                    when (channel_type = '23') then 'Omni Channel' end as channel,
                div_cd,div_nm,dept_cd , dept_nm ,
                class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item,
                cost, current_retail,
                (1 - (cost/nullif(current_retail,0))) as IMU,
                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                        when Offer_Type = 'fixed_price' then offer_value
                        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                        else current_retail end as event_retail,
                (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                aur,baseline_sales_units, baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units, forecast_sales_all_store ,
                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$,forecast_sales_units_diff, sales_diff, sales_lift, margin_diff,
                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments,
                vendor_funding_amount, 
                vendor_funding_per_unit, 
                vendor_funding_type 
                from (
                select SAP_Subclass, 'CHAIN' as "Ad_Zone",event_name,offer_name, Offer_Type,tool_offer_type , start_date,end_date,
                div_cd,div_nm, dept_cd, dept_nm, class_cd, class_nm, subcls_cd, subcls_nm, brand,
                Sku, descr,promo_id,channel_type,
                AD_Feature_Item,event_objective,ad_type,
                vendor_funding_amount, 
                vendor_funding_per_unit, 
                vendor_funding_type,
                avg(effective_discount) as effective_discount ,
                case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(cost) end as cost,
                case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(current_retail) end as current_retail, avg(offer_value) as offer_value,
                max(Ad_Zone_Code) as Ad_Zone_Code,
                max(IMU) as IMU ,
                max(disc_from_CR) as disc_from_Current_Retail,
                max(event_retail) as event_retail,
                max(disc_from_ER) as disc_from_ER,
                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                sum(baseline_sales_units) as baseline_sales_units,
                avg(baseline_sales_all_store) as baseline_sales_all_store,
                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                sum(baseline_sales) as baseline_sales ,
                sum(baseline_GM_$) as baseline_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                sum(forecast_sales_units) as forecast_sales_units,
                avg(forecast_sales_all_store) as forecast_sales_all_store,
                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                sum(forecast_sales) as forecast_sales,
                sum(forecast_event_MD) as forecast_event_MD,
                sum(forecast_GM_$) as forecast_GM_$,
                sum(forecast_sales_units_diff) as forecast_sales_units_diff,
                sum(sales_diff) as sales_diff,
                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                sum(margin_diff) as margin_diff,
                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                max(Sell_Through) as Sell_Through,
                max(Comments_m) as Comments_m,
                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                from (
                select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,pm.start_date as start_date, pm.end_date as end_date,
                pm.name as offer_name, em.channel_type as channel_type,em.ad_type as ad_type,em.name as event_name,
                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                t.effective_discount ,t.promo_id as promo_id,em.event_objective as event_objective,
                tasm.name as Offer_Type, tasm.name as tool_offer_type, case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                im.division_id as div_cd,im.division_nm as div_nm, im.department_id as dept_cd, im.department_nm as dept_nm,im.brand as brand,
                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                im.product_h5_id as Sku, im.item_nm as descr,
                null as AD_Feature_Item,
                t.recommendation_date as dt,
                im.cost as cost,
                im.price as current_retail,
                '1 - cost/CR' as IMU,
                'use case when on %off' as disc_from_CR,
                'CR * (1-%offfromCR)' as event_retail,
                '1 - AUR/ER' as disc_from_ER,
                t.aur as aur, t.baseline_sales_units as baseline_sales_units,
                1435 as baseline_sales_all_store,
                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                t.baseline_revenue as baseline_sales,
                t.baseline_margin as baseline_GM_$,
                0 as UPAs_build,
                t.sales_units as forecast_sales_units,
                1435 as forecast_sales_all_store,
                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                (t.sales_units-t.baseline_sales_units) as forecast_sales_units_diff,
                (t.revenue - t.baseline_revenue) as sales_diff,
                'forecast sales / bs sales' as sales_lift,
                (t.margin - t.baseline_margin ) as margin_diff,
                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                null as Comments_m, null as Marketing_Specific_Comments,
                psr.vf_fixed_amount as vendor_funding_amount,
                psr.vf_per_unit as vendor_funding_per_unit,
                case when psr.vf_type = 1 then 'Fixed Amount'
                when psr.vf_type = 2 then 'Per Unit $'
                when psr.vf_type = 3 then 'Ads. Amount & Per Unit $'
                else null
                end as vendor_funding_type
                from simulation.ps_recommended_finalized as t
                inner join ( select event_id ,name, start_date , end_date,event_objective,channel_type,ad_type from public.event_master 
                {% if event_id %}
                where event_id in {{event_id | inclause}}
                {% endif %}
                group by 1,2,3,4,5,6) as em
                on t.event_id = em.event_id
                inner join (
                    select * from public.promo_master 
                    where status=4 
                    {% if promo_id %}
                        and promo_id in {{promo_id | inclause}}
                    {% endif %}
                ) as pm
                on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                left join public.ps_rules psr
                on psr.promo_id = pm.promo_id
                and t.recommendation_date between pm.start_date and pm.end_date
                inner join (select product_h1_id as division_id , product_h1_name as division_nm, product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,brand as brand,cost,price
                From public.product_master
                ) as im
                on cast(im.product_h5_id as integer) = t.item_id
                left join metaschema.tb_app_sub_master as tasm
                on t.offer_type_id = tasm.id
                ) as a
                group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27
                ) as b
                inner join
                promo_products as pp
                on b.promo_id=pp.promo_id
                and cast(b.Sku as integer)=pp.item_id
                )as c
                where current_retail >0.01
                ),
            base_2 as  (
                select
                    u.*,
                    case
                        when u.Performance <-0.05 then 'toxic'
                        when u.Performance between -0.05 and 0.05 then 'average'
                        else 'good'
                    end as Performance_cal
                from (
                    select
                        y.*,
                        y.incremental_per / nullif(y.baseline_per,0) as performance
                    from (
                        select
                            em."name" as event_name,
                            pm.start_date,
                            pm.end_date,
                            pm."name" as offer_name,
                            item_id as item_id,
                            round( (avg(sum(t.incremental_margin)) over (partition by pm.promo_id, pm.event_id) )::Decimal , 2) as incremental_per,
                            round( (avg(sum(t.baseline_margin)) over (partition by pm.promo_id, pm.event_id) )::Decimal , 2) as baseline_per,
                            round( (sum(t.sales_units) )::Decimal , 2) as Actual_Sales_Units,
                            round( (sum(t.revenue) )::Decimal , 2) as Actual_Sales_$,
                            round( (sum(t.baseline_sales_units) )::Decimal , 2) as Actual_Baseline_Sales_Units,
                            round( (sum(t.incremental_sales_units) )::Decimal , 2) as Incremental_Sales_Units,
                            round( (sum(t.baseline_revenue) )::Decimal , 2) as Actual_Baseline_Revenue,
                            round( (sum(t.incremental_revenue) )::Decimal , 2) as Actual_Incremental_Revenue,
                            round( (sum(t.margin) )::Decimal , 2) as Actual_Margin,
                            round( (sum(t.baseline_margin) )::Decimal , 2) as Actual_Baseline_Margin,
                            round( (sum(t.incremental_margin) )::Decimal , 2) as Actual_Incremental_Margin,
                            round( (sum(promo_spend) )::Decimal , 2) as Actual_MD,
                            round( (sum(t.sales_units-t.baseline_sales_units) )::Decimal , 2) as actual_sales_unit_diff,
                            round( (sum(t.sales_units)/ 1435 )::Decimal , 2) as Actual_UPAs,
                            round( (sum(t.ecom_actual_units) )::Decimal , 2) as ecom_actual_units,
                            round( (sum(t.ecom_actual_revenue) )::Decimal , 2) as ecom_actual_revenue,
                            round( (sum(t.ecom_actual_margin) )::Decimal , 2) as ecom_actual_margin
                        from
                            simulation.ps_recommended_actuals as t
                        inner join 
                            public.promo_master as pm on pm.promo_id = t.promo_id and pm.event_id = t.event_id and t.recommendation_date >= pm.start_date and t.recommendation_date <= pm.end_date
                        inner join 
                            event_master em on em.event_id = pm.event_id and pm.status = 4
                        where em.event_id in {{event_id | inclause}}
                        {% if promo_id %}
                         and pm.promo_id in {{promo_id | inclause}}
                        {% endif %}
                        group by
                            1,
                            2,
                            3,
                            4,
                            5,
                            pm.promo_id,
                            pm.event_id
                    ) as y
                ) as u
            )
            select a.Ad_Zone_Code as "Ad Zone Code",a.event_name as "Event Name", a.start_date as "Start Date",a.end_date as "End Date",a.SAP_Subclass as "SAP Subclass",
                a.Ad_Zone as "Ad Zone (Chain or specific DC)", a.offer_name as "Preliminary Offer Name",
                a.Offer_Type as "Offer Type", a.tool_offer_type as "PriceSmart Offer Type",a.div_cd as "Div. #", a.div_nm as "Div Name",a.dept_cd as "Dept. #", a.dept_nm as "Dept Name",
                a.class_cd as "Class #", a.class_nm as "Class Desc",a.subcls_cd as "Subclass #", a.subcls_nm as "Subclass Desc", a.brand as "Brand",
                Sku as "Sku", descr as "Description",
                a.AD_Feature_Item as "AD Feature Item",a.event_objective as "Intent of Promo" ,a.cost as "Cost",
                a.current_retail as "Current Retail",a.ad_type as "Ad_Type",a.channel as "Channel",b.Performance_cal as "Performance",
                a."IMU"*100 as "IMU %",
                a."disc_from_Current_Retail"*100 as "% Discount from Current Retail",
                a.event_retail as "Event Retail",
                case when a."Discount_from_Event_Retail"<0 then 0 else a."Discount_from_Event_Retail"*100 end as "% Discount from Event Retail",
                a.aur as "Selling AUR",
                a.AD_Prep_Units as "AD Prep Units", a.as_Units as "A/S Units", a.Ad_Prep_$ as "Ad Prep $", a.Sell_Through as "Sell Through",
                a.Comments_m as "Comments (Basis for Projection)", a.Marketing_Specific_Comments as "Marketing Specific Comments",
                a.baseline_sales_all_store as "Baseline # of Stores",
                a.baseline_UPAs as "Baseline UPAS",
                a.forecast_sales_all_store as "Forecast # of Stores",
                a.forecast_UPAs as "Forecast UPAS",
                a.UPAs_build as "UPAS Build",
                a.forecast_event_MD as "Forecast Event MD $",
                a.forecast_sales_units as "Forecast Sales Units",
                a.baseline_sales_units as "Forecast Baseline Sales Units",
                a.forecast_sales_units_diff as "Forecast Incremental Units",
                a.forecast_sales as "Forecast Sales $",
                a.baseline_sales as " Forecast Baseline Sales $",
                a.sales_diff as "Forecast Incremental Sales $",
                a.baseline_GM_$ as "Forecast Baseline GM $",
                a.forecast_GM_$ as "Forecast GM $",
                a.margin_diff as "Forecast Incremental Margin $",
                a.vendor_funding_amount as "Vendor Funding Amount", 
                a.vendor_funding_per_unit as "Vendor Funding Per Unit $", 
                a.vendor_funding_type as "Vendor Funding Type", 
                b.Actual_UPAs as "Actual UPAS",
                b.Actual_MD as "Actual MD $",
                b.Actual_Sales_Units as "Actual Sales Units",
                b.Actual_Baseline_Sales_Units as "Actual Baseline Sales Units",
                b.actual_sales_unit_diff as "Actual Incremental Units",
                b.Actual_Sales_$ as "Actual Sales $",b.actual_baseline_revenue as "Actual Baseline Sales $",
                b.Actual_Incremental_Revenue as "Actual Incremental Sales $",
                b.Actual_Baseline_Margin as "Actual Baseline GM $",
                b.Actual_Baseline_Margin + b.Actual_Incremental_Margin  as "Actual GM $",
                b.Actual_Incremental_Margin as "Actual Incremental Margin $",
                b.ecom_actual_units AS "Actual Ecom Units",
                b.ecom_actual_revenue AS "Actual Ecom Revenue",
                b.ecom_actual_margin AS "Actual Ecom Margin",
                CASE WHEN b.Actual_Sales_Units = 0 then 0 
                else a.forecast_sales_units*b.ecom_actual_units/nullif(b.Actual_Sales_Units,0) end as "Ecom Forecast Units",
                CASE WHEN b.Actual_Sales_$ = 0 then 0 
                else a.forecast_sales*b.ecom_actual_revenue/nullif(b.Actual_Sales_$,0) end as "Ecom Forecast Sale",
                CASE WHEN (b.Actual_Baseline_Margin + b.Actual_Incremental_Margin) = 0  then 0 
                else a.forecast_GM_$*b.ecom_actual_margin/nullif((b.Actual_Baseline_Margin + b.Actual_Incremental_Margin),0) end as "Ecom Forecast Margin"

                from base as a
                left join base_2 as b
                on a.Start_Date=b.start_date
                and a.End_Date=b.end_date
                and a.Sku=b.item_id
                and upper(trim(a.offer_name))=upper(trim(b.offer_name))
                order by "Event Name","Preliminary Offer Name" 
"""

FILTERED_SKU_EXTRACT = """
        with base as
        (select Ad_Zone_Code,event_name,cast(start_date as date) as start_date,cast(end_date as date) as end_date, SAP_Subclass, Ad_Zone, offer_name, Offer_Type,tool_offer_type,
        div_cd,div_nm, dept_cd ,dept_nm, class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
        AD_Feature_Item,event_objective,ad_type,channel,
        round((Cost::Decimal),2) as Cost,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        round(( forecast_sales_units_diff::Decimal),2) as forecast_sales_units_diff,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments,
        vendor_funding_amount, 
        vendor_funding_per_unit, 
        vendor_funding_type 
        from (
        select Ad_Zone_Code, SAP_Subclass, start_date,end_date,
        'CHAIN' as Ad_Zone,event_objective,
        event_name,offer_name,
        case when (tool_offer_type = 'percent_off') then 'percent_off'
        when (tool_offer_type = 'fixed_price') then 'fixed_price'
        when (tool_offer_type = 'extra_amount_off') then 'extra_amount_off'
        when (tool_offer_type = 'reg_price_cv') then 'reg_price_cv'
        when (tool_offer_type = 'reg_price') then 'reg_price'
        when (tool_offer_type = 'bxgy') then 'bxgy'
        when (tool_offer_type = 'bundle_offer') then 'bundle_offer'
        when (tool_offer_type = 'bxgy_$') then 'bxgy_$'
        when (tool_offer_type = 'bmsm_$') then 'bmsm_$'
        else 'BOGO (APR Offer)' end as tool_offer_type,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'reg_price'
        else 'BOGO (APR Offer)' end as Offer_Type,
        case when (ad_type = '1') then 'Advertised'
                when (ad_type = '2') then 'Unadvertised' end as ad_type,
        case when (channel_type = '22') then 'Ecomm Only'
                when (channel_type = '23') then 'Omni Channel' end as channel,
        div_cd,div_nm,dept_cd , dept_nm ,
        class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when Offer_Type = 'fixed_price' then offer_value
                when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                else current_retail end as event_retail,
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units, baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units, forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$,forecast_sales_units_diff, sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments,
        vendor_funding_amount, 
        vendor_funding_per_unit, 
        vendor_funding_type 
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",event_name,offer_name, Offer_Type,tool_offer_type , start_date,end_date,
        div_cd,div_nm, dept_cd, dept_nm, class_cd, class_nm, subcls_cd, subcls_nm, brand,
        Sku, descr,promo_id,channel_type,
        AD_Feature_Item,event_objective,ad_type,
        vendor_funding_amount, 
        vendor_funding_per_unit, 
        vendor_funding_type,
        avg(effective_discount) as effective_discount ,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail, avg(offer_value) as offer_value,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units_diff) as forecast_sales_units_diff,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,pm.start_date as start_date, pm.end_date as end_date,
        pm.name as offer_name, em.channel_type as channel_type,em.ad_type as ad_type,em.name as event_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        t.effective_discount ,t.promo_id as promo_id,em.event_objective as event_objective,
        tasm.name as Offer_Type, tasm.name as tool_offer_type, case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd,im.division_nm as div_nm, im.department_id as dept_cd, im.department_nm as dept_nm,im.brand as brand,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,
        null as AD_Feature_Item,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units as baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        (t.sales_units-t.baseline_sales_units) as forecast_sales_units_diff,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments,
        psr.vf_fixed_amount as vendor_funding_amount,
        psr.vf_per_unit as vendor_funding_per_unit,
        case when psr.vf_type = 1 then 'Fixed Amount'
        when psr.vf_type = 2 then 'Per Unit $'
        when psr.vf_type = 3 then 'Ads. Amount & Per Unit $'
        else null
        end as vendor_funding_type
        from simulation.ps_recommended_finalized as t
        inner join ( select event_id ,name, start_date , end_date,event_objective,channel_type,ad_type from public.event_master 
        {% if event_id %}
        where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4,5,6) as em
        on t.event_id = em.event_id
        inner join (
                select * from public.promo_master 
                where status=4 
                {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
                {% endif %}
        ) as pm
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        left join public.ps_rules psr
        on psr.promo_id = pm.promo_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h1_name as division_nm, product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_id,product_h3_name as class_code_nm , client_subclassid as subclass_id ,product_h4_id, product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,brand as brand,cost,price
        From public.product_master
        ) as im
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        where
        {% if product_h2 %}
            im.department_id in {{product_h2 | inclause}}
        {% endif %} 
        {% if product_h3 %}
            im.product_h3_id in {{product_h3 | inclause}}
        {% endif %}
        {% if product_h4 %}
            im.product_h4_id in {{product_h4 | inclause}}
        {% endif %}
        {% if start_date %}
            and recommendation_date between {{start_date}} and {{end_date}}
        {% endif %}

        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27
        ) as b
        inner join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        where current_retail >0.01
        ),
        base_2 as  (
        select
                u.*,
                case
                when u.Performance <-0.05 then 'toxic'
                when u.Performance between -0.05 and 0.05 then 'average'
                else 'good'
                end as Performance_cal
        from (
                select
                y.*,
                y.incremental_per / nullif(y.baseline_per,0) as performance
                from (
                select
                        em."name" as event_name,
                        pm.start_date,
                        pm.end_date,
                        pm."name" as offer_name,
                        item_id as item_id,
                        round( (avg(sum(t.incremental_margin)) over (partition by pm.promo_id, pm.event_id) )::Decimal , 2) as incremental_per,
                        round( (avg(sum(t.baseline_margin)) over (partition by pm.promo_id, pm.event_id) )::Decimal , 2) as baseline_per,
                        round( (sum(t.sales_units) )::Decimal , 2) as Actual_Sales_Units,
                        round( (sum(t.revenue) )::Decimal , 2) as Actual_Sales_$,
                        round( (sum(t.baseline_sales_units) )::Decimal , 2) as Actual_Baseline_Sales_Units,
                        round( (sum(t.incremental_sales_units) )::Decimal , 2) as Incremental_Sales_Units,
                        round( (sum(t.baseline_revenue) )::Decimal , 2) as Actual_Baseline_Revenue,
                        round( (sum(t.incremental_revenue) )::Decimal , 2) as Actual_Incremental_Revenue,
                        round( (sum(t.margin) )::Decimal , 2) as Actual_Margin,
                        round( (sum(t.baseline_margin) )::Decimal , 2) as Actual_Baseline_Margin,
                        round( (sum(t.incremental_margin) )::Decimal , 2) as Actual_Incremental_Margin,
                        round( (sum(promo_spend) )::Decimal , 2) as Actual_MD,
                        round( (sum(t.sales_units-t.baseline_sales_units) )::Decimal , 2) as actual_sales_unit_diff,
                        round( (sum(t.sales_units)/ 1435 )::Decimal , 2) as Actual_UPAs,
                        round( (sum(t.ecom_actual_units) )::Decimal , 2) as ecom_actual_units,
                        round( (sum(t.ecom_actual_revenue) )::Decimal , 2) as ecom_actual_revenue,
                        round( (sum(t.ecom_actual_margin) )::Decimal , 2) as ecom_actual_margin
                from
                        simulation.ps_recommended_actuals as t
                inner join 
                        public.promo_master as pm on pm.promo_id = t.promo_id and pm.event_id = t.event_id and t.recommendation_date >= pm.start_date and t.recommendation_date <= pm.end_date
                inner join 
                        event_master em on em.event_id = pm.event_id and pm.status = 4
                inner join
                        product_master prodm on prodm.product_h5_id = t.item_id
                where 
                    em.event_id in {{event_id | inclause}}
                    {% if promo_id %}
                        and pm.promo_id in {{promo_id | inclause}}
                    {% endif %}
                    {% if product_h2 %}
                        and prodm.product_h2_id in {{product_h2 | inclause}}
                    {% endif %}
                    {% if product_h3 %}
                        and prodm.product_h3_id in {{product_h3 | inclause}}
                    {% endif %}
                    {% if product_h4 %}
                        and prodm.product_h4_id in {{product_h4 | inclause}}
                    {% endif %}
                    {% if start_date %}
                            and recommendation_date between {{start_date}} and {{end_date}}
                    {% endif %}
                group by
                        1,
                        2,
                        3,
                        4,
                        5,
                        pm.promo_id,
                        pm.event_id
                ) as y
        ) as u
        )
        select a.Ad_Zone_Code as "Ad Zone Code",a.event_name as "Event Name", a.start_date as "Start Date",a.end_date as "End Date",a.SAP_Subclass as "SAP Subclass",
        a.Ad_Zone as "Ad Zone (Chain or specific DC)", a.offer_name as "Preliminary Offer Name",
        a.Offer_Type as "Offer Type", a.tool_offer_type as "PriceSmart Offer Type",a.div_cd as "Div. #", a.div_nm as "Div Name",a.dept_cd as "Dept. #", a.dept_nm as "Dept Name",
        a.class_cd as "Class #", a.class_nm as "Class Desc",a.subcls_cd as "Subclass #", a.subcls_nm as "Subclass Desc", a.brand as "Brand",
        Sku as "Sku", descr as "Description",
        a.AD_Feature_Item as "AD Feature Item",a.event_objective as "Intent of Promo" ,a.cost as "Cost",
        a.current_retail as "Current Retail",a.ad_type as "Ad_Type",a.channel as "Channel",b.Performance_cal as "Performance",
        a."IMU"*100 as "IMU %",
        a."disc_from_Current_Retail"*100 as "% Discount from Current Retail",
        a.event_retail as "Event Retail",
        case when a."Discount_from_Event_Retail"<0 then 0 else a."Discount_from_Event_Retail"*100 end as "% Discount from Event Retail",
        a.aur as "Selling AUR",
        a.AD_Prep_Units as "AD Prep Units", a.as_Units as "A/S Units", a.Ad_Prep_$ as "Ad Prep $", a.Sell_Through as "Sell Through",
        a.Comments_m as "Comments (Basis for Projection)", a.Marketing_Specific_Comments as "Marketing Specific Comments",
        a.baseline_sales_all_store as "Baseline # of Stores",
        a.baseline_UPAs as "Baseline UPAS",
        a.forecast_sales_all_store as "Forecast # of Stores",
        a.forecast_UPAs as "Forecast UPAS",
        a.UPAs_build as "UPAS Build",
        a.forecast_event_MD as "Forecast Event MD $",
        a.forecast_sales_units as "Forecast Sales Units",
        a.baseline_sales_units as "Forecast Baseline Sales Units",
        a.forecast_sales_units_diff as "Forecast Incremental Units",
        a.forecast_sales as "Forecast Sales $",
        a.baseline_sales as " Forecast Baseline Sales $",
        a.sales_diff as "Forecast Incremental Sales $",
        a.baseline_GM_$ as "Forecast Baseline GM $",
        a.forecast_GM_$ as "Forecast GM $",
        a.margin_diff as "Forecast Incremental Margin $",
        a.vendor_funding_amount as "Vendor Funding Amount", 
        a.vendor_funding_per_unit as "Vendor Funding Per Unit $", 
        a.vendor_funding_type as "Vendor Funding Type", 
        b.Actual_UPAs as "Actual UPAS",
        b.Actual_MD as "Actual MD $",
        b.Actual_Sales_Units as "Actual Sales Units",
        b.Actual_Baseline_Sales_Units as "Actual Baseline Sales Units",
        b.actual_sales_unit_diff as "Actual Incremental Units",
        b.Actual_Sales_$ as "Actual Sales $",b.actual_baseline_revenue as "Actual Baseline Sales $",
        b.Actual_Incremental_Revenue as "Actual Incremental Sales $",
        b.Actual_Baseline_Margin as "Actual Baseline GM $",
        b.Actual_Baseline_Margin + b.Actual_Incremental_Margin  as "Actual GM $",
        b.Actual_Incremental_Margin as "Actual Incremental Margin $",
        b.ecom_actual_units AS "Actual Ecom Units",
        b.ecom_actual_revenue AS "Actual Ecom Revenue",
        b.ecom_actual_margin AS "Actual Ecom Margin",
        CASE WHEN b.Actual_Sales_Units = 0 then 0 
        else a.forecast_sales_units*b.ecom_actual_units/nullif(b.Actual_Sales_Units,0) end as "Ecom Forecast Units",
        CASE WHEN b.Actual_Sales_$ = 0 then 0 
        else a.forecast_sales*b.ecom_actual_revenue/nullif(b.Actual_Sales_$,0) end as "Ecom Forecast Sale",
        CASE WHEN (b.Actual_Baseline_Margin + b.Actual_Incremental_Margin) = 0  then 0 
        else a.forecast_GM_$*b.ecom_actual_margin/nullif((b.Actual_Baseline_Margin + b.Actual_Incremental_Margin),0) end as "Ecom Forecast Margin"

        from base as a
        left join base_2 as b
        on a.Start_Date=b.start_date
        and a.End_Date=b.end_date
        and a.Sku=b.item_id
        and upper(trim(a.offer_name))=upper(trim(b.offer_name))
        order by "Event Name","Preliminary Offer Name" 
"""


MARKETING_SKU_LEVEL = """
        with base as
        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
        current_retail as "Current Retail",effective_discount,
        "IMU" as "IMU %",
        "disc_from_Current_Retail" as "% Discount from Current Retail",
        event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
        round((percent_Message:: Decimal),2) as "% Message",
        round(abs(dollar_Message :: Decimal),2) as "$ Message",
        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
        (1-("event_retail_with_100%_redemption"/nullif(current_retail,0))) as "% Off From Current Retail (With 100% Redemption)",
        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
        when "Offer Type Promosmart"='reg_price' then 0
        else ("event_retail_with_100%_redemption" - current_retail)
        end as dollar_Message,
        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/nullif(comp_value,0))
        when "Offer Type Promosmart"='reg_price' then 0
        else (1-"event_retail_with_100%_redemption"/nullif(current_retail,0))
        end as percent_Message,
        round((Cost::Decimal),2) as Cost,
        effective_discount,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments
        from (
        select Ad_Zone_Code, SAP_Subclass,
        'CHAIN' as Ad_Zone,
        offer_name,b.promo_id,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
        div_cd, dept_cd , dept_nm ,
        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
        "Status",GRM_Flow,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when Offer_Type = 'fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail end as event_retail,
        case when Offer_Type='fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail*(1-effective_discount/100)
        end as "event_retail_with_100%_redemption",
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
        pm.name as offer_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
        null as AD_Feature_Item,
        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t 
        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master 
        {% if event_id %}
        where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4) as em
        on t.event_id = em.event_id
        inner join (select * from public.promo_master 
        where status=4 
        {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as pm 
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD=1 then 'web enabled' else null end as "web designation",
        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
        from public.product_master 
        where product_h2_id in {{product_h2 | inclause}}
        ) as im 
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
        ) as b
        left join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01)),
        base_2 as
        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
        from base a
        left join public.mkt_store_inventory msi
        on a."Sku"=msi.item_id
        left join
        (
        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
        from public.mkt_dam group by 1
        ) as md
        on a."Sku"=md."Sku"),
        offer_x_y_value as
        (WITH promo_product_store as (
        SELECT
        cast(sm.scenario_id as int4) as scenario_id ,
        sm.promo_id,
        pp.item_id,
        pm2."name",
        CASE
        WHEN discount_level = -200 THEN -200
        WHEN discount_level = 1 THEN pm.product_h1_id
        WHEN discount_level = 2 THEN pm.product_h2_id
        WHEN discount_level = 3 THEN pm.product_h3_id
        WHEN discount_level = 4 THEN pm.product_h4_id
        WHEN discount_level = 5 THEN pm.product_h5_id
        END AS discount_level_value
        from
        (select * from public.promo_master
        where status= 4 and last_approved_scenario_id is not null
        )pm2
        inner join
        public.scenario_master sm
        on pm2.promo_id=sm.promo_id
        and pm2.last_approved_scenario_id=sm.scenario_id
        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
        )
        SELECT
        pps.*,
        psd.offer_type,
        psd.offer_value,
        psd.offer_type_id,
        offer_x_value,
        offer_y_value
        FROM
        promo_product_store pps
        INNER JOIN (
        SELECT
        discount_level_value,
        scenario_id,
        offer_value,
        offer_x_value,
        offer_y_value,
        offer_type_id,
        tasm."name" as offer_type
        FROM public.ps_scenario_discounts pd
        INNER JOIN
        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
        ) psd USING (discount_level_value, scenario_id) where offer_type in
        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
        final as 
        (select a.*,
        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
        when "offer_type" = 'bxgy' then b.offer_value
        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
        else null end as "BOGO_Bundle_BMSM_Message"
        from base_2 a
        left join offer_x_y_value b
        on a.promo_id=b.promo_id
        and a."Sku"=b.item_id),
        combo as 
        (With Product_Combo as
        (Select a.id as "Sku", a.combo,
        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
        b. Total_SKUS_in_Combo
        from (select * from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        ) a
        inner join
        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
        public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ) b
        using (combo)
        ),
        Mkt_Extract as (Select * from final
        where "Sku" is not null),
        Combo_Flag as (
        Select a.*,
        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
        case when b. combo is not null then  combo else null end as Combo_Name,
        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
        b. extracted_number
        from Mkt_Extract a
        left join
        Product_Combo b
        using ("Sku")
        ),
        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
        from Combo_Flag
        group by 1,2,3
        ),
        Combo_Indicator as (Select a.*,
        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
        from Combo_Flag a
        left join
        Count_Flag b
        using (Combo_Name, event_name,"Preliminary Offer Name")
        ),
        Final_Combo_Table as (
        select "Preliminary Offer Name",event_name, "Sku",
        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
        from
        (Select *,
        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
        ) as a
        ),
        Output as (
        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
        from
        (Select a.*, b. min_number from Final_Combo_Table a
        inner join
        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
        group by 1,2,3
        ) b
        using ("Preliminary Offer Name",event_name, "Sku")
        ) a
        where min_number= extracted_number or min_number is null
        ))
        Select * from Output)
        select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
        ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
        "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
        "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount" from                                                                                                                                                                                                        
        (select a.*,case when combo_url is not null then combo_url
        when "web designation" = 'web enabled' then prod_url
        else null end as url
        from 
        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
        from final a
        left join combo b 
        on a."Sku"=b."Sku"
        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
        and a.event_name=b.event_name) as a ) as a
        order by "Event Name","Preliminary Offer Name"
"""

MARKETING_OFFER_LEVEL = """
        with base as
        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
        current_retail as "Current Retail",effective_discount,percent_Message,dollar_Message,
        "IMU" as "IMU %",
        "disc_from_Current_Retail" as "% Discount from Current Retail",
        event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
        round((percent_Message:: Decimal),2) as "% Message",
        round(abs(dollar_Message :: Decimal),2) as "$ Message",
        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
        (1-("event_retail_with_100%_redemption"/nullif(current_retail,0))) as "% Off From Current Retail (With 100% Redemption)",
        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
        when "Offer Type Promosmart"='reg_price' then 0
        else ("event_retail_with_100%_redemption" - current_retail)
        end as dollar_Message,
        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/nullif(comp_value,0))
        when "Offer Type Promosmart"='reg_price' then 0
        else (1-"event_retail_with_100%_redemption"/nullif(current_retail,0))
        end as percent_Message,
        round((Cost::Decimal),2) as Cost,
        effective_discount,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments
        from (
        select Ad_Zone_Code, SAP_Subclass,
        'CHAIN' as Ad_Zone,
        offer_name,b.promo_id,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
        div_cd, dept_cd , dept_nm ,
        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
        "Status",GRM_Flow,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
        when Offer_Type = 'fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail end as event_retail,
        case when Offer_Type='fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail*(1-effective_discount/100)
        end as "event_retail_with_100%_redemption",
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
        pm.name as offer_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
        null as AD_Feature_Item,
        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t 
        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master 
        {% if event_id %}
            where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4) as em
        on t.event_id = em.event_id
        inner join (select * from public.promo_master 
        where status=4 
        {% if promo_id %}
         and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as pm
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
        From public.product_master
        where product_h2_id in {{product_h2 | inclause}}
        ) as im 
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
        ) as b
        left join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01)),
        base_2 as
        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
        from base a
        left join public.mkt_store_inventory msi
        on a."Sku"=msi.item_id
        left join
        (
        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
        from public.mkt_dam group by 1
        ) as md
        on a."Sku"=md."Sku"),
        offer_x_y_value as
        (WITH promo_product_store as (
        SELECT
        cast(sm.scenario_id as int4) as scenario_id ,
        sm.promo_id,
        pp.item_id,
        pm2."name",
        CASE
        WHEN discount_level = -200 THEN -200
        WHEN discount_level = 1 THEN pm.product_h1_id
        WHEN discount_level = 2 THEN pm.product_h2_id
        WHEN discount_level = 3 THEN pm.product_h3_id
        WHEN discount_level = 4 THEN pm.product_h4_id
        WHEN discount_level = 5 THEN pm.product_h5_id
        END AS discount_level_value
        from
        (select * from public.promo_master
        where status= 4 and last_approved_scenario_id is not null
        )pm2
        inner join
        public.scenario_master sm
        on pm2.promo_id=sm.promo_id
        and pm2.last_approved_scenario_id=sm.scenario_id
        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
        )
        SELECT
        pps.*,
        psd.offer_type,
        psd.offer_value,
        psd.offer_type_id,
        offer_x_value,
        offer_y_value
        FROM
        promo_product_store pps
        INNER JOIN (
        SELECT
        discount_level_value,
        scenario_id,
        offer_value,
        offer_x_value,
        offer_y_value,
        offer_type_id,
        tasm."name" as offer_type
        FROM public.ps_scenario_discounts pd
        INNER JOIN
        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
        ) psd USING (discount_level_value, scenario_id) where offer_type in
        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
        final as 
        (select a.*,
        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
        when "offer_type" = 'bxgy' then b.offer_value
        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
        else null end as "BOGO_Bundle_BMSM_Message"
        from base_2 a
        left join offer_x_y_value b
        on a.promo_id=b.promo_id
        and a."Sku"=b.item_id),  
        combo as 
        (With Product_Combo as
        (Select a.id as "Sku", a.combo,
        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
        b. Total_SKUS_in_Combo
        from (select * from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        ) a
        inner join
        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
        public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ) b
        using (combo)
        ),
        Mkt_Extract as (Select * from final
        where "Sku" is not null),
        Combo_Flag as (
        Select a.*,
        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
        case when b. combo is not null then  combo else null end as Combo_Name,
        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
        b. extracted_number
        from Mkt_Extract a
        left join
        Product_Combo b
        using ("Sku")
        ),
        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
        from Combo_Flag
        group by 1,2,3
        ),
        Combo_Indicator as (Select a.*,
        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
        from Combo_Flag a
        left join
        Count_Flag b
        using (Combo_Name, event_name,"Preliminary Offer Name")
        ),
        Final_Combo_Table as (
        select "Preliminary Offer Name",event_name, "Sku",
        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
        from
        (Select *,
        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
        ) as a
        ),
        Output as (
        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
        from
        (Select a.*, b. min_number from Final_Combo_Table a
        inner join
        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
        group by 1,2,3
        ) b
        using ("Preliminary Offer Name",event_name, "Sku")
        ) a
        where min_number= extracted_number or min_number is null
        ))
        Select * from Output),
        sku_offer as 
        (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart",ROUND(avg("% Discount from Current Retail"),2) as "% Discount from Current Retail",ROUND(avg("% Discount from Event Retail"),2) as "% Discount from Event Retail",ROUND(avg(case when "Selling AUR">0 then "Selling AUR" else null end ),2) as "Selling AUR",                                                                                                                                                                                                        
        sum("Baseline Sales Units") as "Baseline Sales Units",sum("Baseline Sales $") as "Baseline Sales $",sum("Baseline GM $") as "Baseline GM $",sum("Forecast Sales Units") as "Forecast Sales Units",sum("Forecast Sales $") as "Forecast Sales $",sum("Forecast Event MD $") as "Forecast Event MD $",
        sum("Forecast GM $") as "Forecast GM $",sum("Sales $ Difference") as "Sales $ Difference",sum("GM $ Difference") as "GM $ Difference",round((max(percent_Message):: Decimal),2) as "% Message",
        round(max(abs(dollar_Message :: Decimal)),2) as "$ Message",min(coalesce("BOGO_Bundle_BMSM_Message")) as "BOGO Bundle BMSM Message",min("Current Retail") as "Current Retail Min Price",max("Current Retail") as "Current Retail Max Price",min("Event Retail") as "Event Retail Min Price",max("Event Retail") as "Event Retail Max Price",
        min(case when "Selling AUR">0 then "Selling AUR" else null end ) as "Selling Price Minimum",max("Selling AUR") as "Selling Price Maximum",min(comp_value) as "Comp Value Min Off",max(comp_value) as "Comp Value Max Off",count(distinct "Sku") as "Total Sku",
        count (distinct(case when "AD Feature Item" like '%yes%' then "Sku" else null end)) as "Feature Sku Count",count(case when "AD Feature Item" = 'yes' and "Image Exists" = 'Y' then "Sku" else null end) as "Count Feature Sku With Image",ROUND(cast(avg("%_Store_with_inventory") as numeric),2) as "% Store With Inventory",ROUND(cast(avg(case when "AD Feature Item" = 'yes' then "%_Store_with_inventory" else null end ) as numeric),2) as "Feature Sku % Store With Inventory",
        ROUND(cast(min(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as numeric),2) as "Comp Value Min %Off",ROUND(cast(max(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as numeric),2) as "Comp Value Max %Off" from                                                                                                                                                                                                  
        (select a.*,case when combo_url is not null then combo_url
        when "web designation" = 'web enabled' then prod_url
        else null end as url
        from 
        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
        from final a
        left join combo b 
        on a."Sku"=b."Sku"
        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
        and a.event_name=b.event_name) as a ) as a
        group by 1,2,3,4,5,6,7),
        feature_sku as 
        (SELECT promo_id,offer_comment,ARRAY_AGG(Item)::text AS sku_ids FROM 
        (select pm.promo_id,pm.offer_comment, cast("item_id" as varchar) as Item from public.promo_products as a
        join public.promo_master pm
        on a.promo_id=pm.promo_id
        WHERE pm.status=4
        and a.is_hero=1
        ) as a
        group by 1,2)
        select a.*,case when "Count Feature Sku With Image">0 then 'Y' else 'N' end as "feature sku image(y/n)" ,b.offer_comment,b.sku_ids as feature_skus
        from sku_offer a
        left join feature_sku b 
        on a."Promo ID"=b.promo_id
        order by "Event Name","Preliminary Offer Name"
"""


MARKETING_DAM_LEVEL = """
        select k.event_name as "Event Name",k.promo_id as "Promo ID" ,k."Promo Name" as "Promo Name",a.assetpath as "Asset Path",a."Dynamic Media Link" as "Dynamic Media Link",a.sku_list as "Sku",
        k.hero_sku as "Feature SKU"
        from
        (SELECT sku_list,assetpath,"Dynamic Media Link"
        FROM public.mkt_dam) as a
        join
        (select p.*,t.name as event_name
        from
        (select y.*,z.promo_name as "Promo Name",event_id
        from
        (select promo_id,item_id,case when is_hero = 1 then 'yes' else 'no' end as "hero_sku"
        from public.promo_products
        inner join public.product_master on item_id = product_h5_id
        where product_h2_id in {{product_h2 | inclause}}
        ) as y
        join
        (
        select promo_id,event_id,name as promo_name
        from public.promo_master where status=4 
        {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as z
        on y.promo_id = z.promo_id) as p
        join
        (
        select event_id,name
        from public.event_master
        {% if event_id %}
                where event_id in {{event_id | inclause}}
        {% endif %}
        ) as t
        on p.event_id = t.event_id) as k
        on a.sku_list = k.item_id
        group by 1,2,3,4,5,6,7
        order by "Event Name","Promo Name"
"""

SIGNAGE_SKU_LEVEL = """
        with base as
        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        ad_type,channel,
        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
        current_retail as "Current Retail",effective_discount,
        "IMU" as "IMU %",div_nm as "Div Name", dept_nm as "Dept Name",brand as "Brand",
        "disc_from_Current_Retail"*100 as "% Discount from Current Retail",
        event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
        round((percent_Message:: Decimal),2) as "% Message",
        round(abs(dollar_Message :: Decimal),2) as "$ Message",
        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail"*100 end as "% Discount from Event Retail",
        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        channel,ad_type,AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
        (1-("event_retail_with_100%_redemption"/nullif(current_retail,0))) as "% Off From Current Retail (With 100% Redemption)",
        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
        when "Offer Type Promosmart"='reg_price' then 0
        else ("event_retail_with_100%_redemption" - current_retail)
        end as dollar_Message,
        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/nullif(comp_value,0))
        when "Offer Type Promosmart"='reg_price' then 0
        else (1-"event_retail_with_100%_redemption"/nullif(current_retail,0))
        end as percent_Message,
        round((Cost::Decimal),2) as Cost,
        effective_discount,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments,
        dept_nm,div_nm,brand
        from (
        select Ad_Zone_Code, SAP_Subclass,
        'CHAIN' as Ad_Zone,
        offer_name,b.promo_id,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
        div_cd, dept_cd , dept_nm ,div_nm,brand,channel,
        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
        ad_type,
        "Status",GRM_Flow,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when Offer_Type = 'fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail end as event_retail,
        case when Offer_Type='fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail*(1-effective_discount/100)
        end as "event_retail_with_100%_redemption",
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,div_nm,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,ad_type,channel,
        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,brand,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
        pm.name as offer_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,im.division_nm as div_nm,brand,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
        case when (em.ad_type = '1') then 'Advertised'
                when (em.ad_type = '2') then 'Unadvertised' end as ad_type,
        case when (em.channel_type = '22') then 'Ecomm Only'
                when (em.channel_type = '23') then 'Omni Channel' end as channel,
        null as AD_Feature_Item,
        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t 
        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value,ad_type,channel_type from public.event_master
        {% if event_id %}
                where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4) as em
        on t.event_id = em.event_id
        inner join (select * from public.promo_master where status=4 
        {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as pm 
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id ,product_h1_name as division_nm, product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible",brand
        From public.product_master where product_h2_id in {{product_h2 | inclause}} ) as im 
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29
        ) as b
        left join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01)),
        base_2 as
        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
        from base a
        left join public.mkt_store_inventory msi
        on a."Sku"=msi.item_id
        left join
        (
        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
        from public.mkt_dam group by 1
        ) as md
        on a."Sku"=md."Sku"),
        offer_x_y_value as
        (WITH promo_product_store as (
        SELECT
        cast(sm.scenario_id as int4) as scenario_id ,
        sm.promo_id,
        pp.item_id,
        pm2."name",
        CASE
        WHEN discount_level = -200 THEN -200
        WHEN discount_level = 1 THEN pm.product_h1_id
        WHEN discount_level = 2 THEN pm.product_h2_id
        WHEN discount_level = 3 THEN pm.product_h3_id
        WHEN discount_level = 4 THEN pm.product_h4_id
        WHEN discount_level = 5 THEN pm.product_h5_id
        END AS discount_level_value
        from
        (select * from public.promo_master
        where status= 4 and last_approved_scenario_id is not null
        )pm2
        inner join
        public.scenario_master sm
        on pm2.promo_id=sm.promo_id
        and pm2.last_approved_scenario_id=sm.scenario_id
        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
        )
        SELECT
        pps.*,
        psd.offer_type,
        psd.offer_value,
        psd.offer_type_id,
        offer_x_value,
        offer_y_value
        FROM
        promo_product_store pps
        INNER JOIN (
        SELECT
        discount_level_value,
        scenario_id,
        offer_value,
        offer_x_value,
        offer_y_value,
        offer_type_id,
        tasm."name" as offer_type
        FROM public.ps_scenario_discounts pd
        INNER JOIN
        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
        ) psd USING (discount_level_value, scenario_id) where offer_type in
        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
        final as 
        (select a.*,
        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
        when "offer_type" = 'bxgy' then b.offer_value
        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
        else null end as "BOGO_Bundle_BMSM_Message"
        from base_2 a
        left join offer_x_y_value b
        on a.promo_id=b.promo_id
        and a."Sku"=b.item_id),
        combo as 
        (With Product_Combo as
        (Select a.id as "Sku", a.combo,
        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
        b. Total_SKUS_in_Combo
        from (select * from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        ) a
        inner join
        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
        public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ) b
        using (combo)
        ),
        Mkt_Extract as (Select * from final
        where "Sku" is not null),
        Combo_Flag as (
        Select a.*,
        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
        case when b. combo is not null then  combo else null end as Combo_Name,
        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
        b. extracted_number
        from Mkt_Extract a
        left join
        Product_Combo b
        using ("Sku")
        ),
        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
        from Combo_Flag
        group by 1,2,3
        ),
        Combo_Indicator as (Select a.*,
        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
        from Combo_Flag a
        left join
        Count_Flag b
        using (Combo_Name, event_name,"Preliminary Offer Name")
        ),
        Final_Combo_Table as (
        select "Preliminary Offer Name",event_name, "Sku",
        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
        from
        (Select *,
        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
        ) as a
        ),
        Output as (
        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
        from
        (Select a.*, b. min_number from Final_Combo_Table a
        inner join
        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
        group by 1,2,3
        ) b
        using ("Preliminary Offer Name",event_name, "Sku")
        ) a
        where min_number= extracted_number or min_number is null
        ))
        Select * from Output),
        planogram as 
        (select * from public.mkt_planogram_report),
        sku_offer as 
        (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP",
        "Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",
        ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",
        ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail","% Discount from Event Retail",
        ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",
        ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",
        combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
        channel as "Channel",ad_type as "Ad_Type","Div Name","Dept Name","Brand",
        ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR","Marketing Specific Comments",                                                                                                                                                                                                    
        "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
        "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount",Combo_Name   from                                                                                                                                                                                                        
        (select a.*,case when combo_url is not null then combo_url
        when "web designation" = 'web enabled' then prod_url
        else null end as url
        from 
        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag,b.Combo_Name 
        from final a
        left join combo b 
        on a."Sku"=b."Sku"
        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
        and a.event_name=b.event_name) as a ) as a),
        pivot_combo as
        (select combo as combo_name,ARRAY_AGG(cast(id as varchar))::text as combo_skus from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        )
        select 
                "Start Date",
                a."End Date",
                "Event Name",
                "Preliminary Offer Name",
                "Offer Type SAP",
                "Div. #","Div Name",
                "Dept. #","Dept Name",
                "Class #","Class Desc",
                "Subclass #","Subclass Desc",
                "Brand","Sku",       
                "Description",
                "Current Retail","Event Retail",
                "Comp Value",
                "% Discount from Current Retail","% Discount from Event Retail",
                b.planogram_name as "Planogram Name",b."Section",b.inline__ool as "Inline Ool",
                b.presentation_detail as "Presentation Detail",b.presentation_type as "Presentation Type",
                b.endcap__side_panel__queue_code as "Endcap Side Panel Queue Code",
                "Ad_Type","Channel",
                "AD Feature Item","Combination Event Retail","Combination Current Retail",
                a.combo_name as "Combo Name",combo_skus as "Combo Skus",
                "Available In Stores Ind","% Store With Inventory","Marketing Specific Comments",
                "Promo ID","Selling AUR","Offer Type Promosmart","$ Message","% Message","BOGO Bundle BMSM Message",
                (("Comp Value"-"Current Retail")/nullif("Comp Value",0)) as "Comp Value %"
        from sku_offer a  
        left join planogram b 
        on a."Sku"=b.itemid
        and b.set_date<=a."Start Date" and a."End Date"<=b.end_date
        left join 
        pivot_combo c   
        on a.Combo_Name=c.combo_name
        order by "Event Name","Preliminary Offer Name"
"""

SIGNAGE_OFFER_LEVEL = """
        with base as
        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
        current_retail as "Current Retail",effective_discount,percent_Message,dollar_Message,
        "IMU" as "IMU %",
        "disc_from_Current_Retail" as "% Discount from Current Retail",
        event_retail as "Event Retail","event_retail_with_100%_redemption", "% Off From Current Retail (With 100% Redemption)",
        round((percent_Message:: Decimal),2) as "% Message",
        round(abs(dollar_Message :: Decimal),2) as "$ Message",
        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
        (1-("event_retail_with_100%_redemption"/nullif(current_retail,0))) as "% Off From Current Retail (With 100% Redemption)",
        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
        when "Offer Type Promosmart"='reg_price' then 0
        else ("event_retail_with_100%_redemption" - current_retail)
        end as dollar_Message,
        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/nullif(comp_value,0))
        when "Offer Type Promosmart"='reg_price' then 0
        else (1-"event_retail_with_100%_redemption"/nullif(current_retail,0))
        end as percent_Message,
        round((Cost::Decimal),2) as Cost,
        effective_discount,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments
        from (
        select Ad_Zone_Code, SAP_Subclass,
        'CHAIN' as Ad_Zone,
        offer_name,b.promo_id,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
        div_cd, dept_cd , dept_nm ,
        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
        "Status",GRM_Flow,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when Offer_Type = 'fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail end as event_retail,
        case when Offer_Type='fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail*(1-effective_discount/100)
        end as "event_retail_with_100%_redemption",
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
        pm.name as offer_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
        null as AD_Feature_Item,
        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t 
        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master 
        {% if event_id %}
                where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4) as em
        on t.event_id = em.event_id
        inner join (select * from public.promo_master where status=4 
        {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as pm 
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD =1 then 'web enabled' else null end as "web designation",
        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
        From public.product_master where product_h2_id in {{product_h2 | inclause}} ) as im 
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
        ) as b
        left join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01)),
        base_2 as
        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
        from base a
        left join public.mkt_store_inventory msi
        on a."Sku"=msi.item_id
        left join
        (
        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
        from public.mkt_dam group by 1
        ) as md
        on a."Sku"=md."Sku"),
        offer_x_y_value as
        (WITH promo_product_store as (
        SELECT
        cast(sm.scenario_id as int4) as scenario_id ,
        sm.promo_id,
        pp.item_id,
        pm2."name",
        CASE
        WHEN discount_level = -200 THEN -200
        WHEN discount_level = 1 THEN pm.product_h1_id
        WHEN discount_level = 2 THEN pm.product_h2_id
        WHEN discount_level = 3 THEN pm.product_h3_id
        WHEN discount_level = 4 THEN pm.product_h4_id
        WHEN discount_level = 5 THEN pm.product_h5_id
        END AS discount_level_value
        from
        (select * from public.promo_master
        where status= 4 and last_approved_scenario_id is not null
        )pm2
        inner join
        public.scenario_master sm
        on pm2.promo_id=sm.promo_id
        and pm2.last_approved_scenario_id=sm.scenario_id
        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
        )
        SELECT
        pps.*,
        psd.offer_type,
        psd.offer_value,
        psd.offer_type_id,
        offer_x_value,
        offer_y_value
        FROM
        promo_product_store pps
        INNER JOIN (
        SELECT
        discount_level_value,
        scenario_id,
        offer_value,
        offer_x_value,
        offer_y_value,
        offer_type_id,
        tasm."name" as offer_type
        FROM public.ps_scenario_discounts pd
        INNER JOIN
        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
        ) psd USING (discount_level_value, scenario_id) where offer_type in
        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
        final as 
        (select a.*,
        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
        when "offer_type" = 'bxgy' then b.offer_value
        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
        else null end as "BOGO_Bundle_BMSM_Message"
        from base_2 a
        left join offer_x_y_value b
        on a.promo_id=b.promo_id
        and a."Sku"=b.item_id),
        combo as 
        (With Product_Combo as
        (Select a.id as "Sku", a.combo,
        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
        b. Total_SKUS_in_Combo
        from (select * from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        ) a
        inner join
        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
        public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ) b
        using (combo)
        ),
        Mkt_Extract as (Select * from final
        where "Sku" is not null),
        Combo_Flag as (
        Select a.*,
        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
        case when b. combo is not null then  combo else null end as Combo_Name,
        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
        b. extracted_number
        from Mkt_Extract a
        left join
        Product_Combo b
        using ("Sku")
        ),
        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
        from Combo_Flag
        group by 1,2,3
        ),
        Combo_Indicator as (Select a.*,
        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
        from Combo_Flag a
        left join
        Count_Flag b
        using (Combo_Name, event_name,"Preliminary Offer Name")
        ),
        Final_Combo_Table as (
        select "Preliminary Offer Name",event_name, "Sku",
        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
        from
        (Select *,
        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
        ) as a
        ),
        Output as (
        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
        from
        (Select a.*, b. min_number from Final_Combo_Table a
        inner join
        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
        group by 1,2,3
        ) b
        using ("Preliminary Offer Name",event_name, "Sku")
        ) a
        where min_number= extracted_number or min_number is null
        ))
        Select * from Output),
        planogram as 
        (select * from public.mkt_planogram_report),
        sku_offer as 
        (select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% Off From Current Retail (With 100% Redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
        ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
        "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
        "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount",dollar_message, percent_message from                                                                                                                                                                                                        
        (select a.*,case when combo_url is not null then combo_url
        when "web designation" = 'web enabled' then prod_url
        else null end as url,cn
        from 
        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag,b.Combo_Name as cn
        from final a
        left join combo b 
        on a."Sku"=b."Sku"
        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
        and a.event_name=b.event_name) as a ) as a),
        pivot_combo as
        (select combo as combo_name,STRING_AGG(cast(id as varchar),',') from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ),
        per_dollar_msg as 
        (select event_name,start_date,end_date,"Preliminary Offer Name",round((max(percent_Message):: Decimal),2) as "% Message",
        round(max(abs(dollar_Message :: Decimal)),2) as "$ Message" from final
        group by 1,2,3,4),
        signange_offer_level as
        (select "Event Name","Preliminary Offer Name",a."Start Date",a."End Date","Offer Type SAP","Offer Type Promosmart",
        b.planogram_name as "Planogram Name" ,b."Section",b.inline__ool as "Inline Ool",b.presentation_detail as "Presentation Detail",b.presentation_type as "Presentation Type",b.endcap__side_panel__queue_code as "Endcap Side Panel Queue Code",min("Event Retail") as "Event Retail Min Price",max("Event Retail") as "Event Retail Max Price",min("Selling AUR") as "Selling Price Minimum",max("Selling AUR") as "Selling Price Maximum",min("Current Retail") as "Current Retail Min Price",max("Current Retail") as "Current Retail Max Price",
        min(abs(dollar_Message)) as "Min $ Discount",max(abs(dollar_Message)) as "Max $ Discount",round(cast(min(percent_Message) as numeric),2) as "Min % Discount",round(cast(max(percent_Message) as numeric),2) as "Max % Discount",min("Comp Value") as "Comp Value Min Off",max("Comp Value") as "Comp Value Max Off",min(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as "Comp Value Min %Off",max(case when "Offer Type Promosmart"='reg_price_cv' then percent_Message else null end) as "Comp Value Max %Off"
        from sku_offer a  
        left join planogram b 
        on a."Sku"=b.itemid
        and b.set_date<=a."Start Date" and a."End Date"<=b.end_date
        group by 1,2,3,4,5,6,7,8,9,10,11,12)
        select a.*,b."% Message","$ Message" from
        signange_offer_level a 
        left join per_dollar_msg b 
        on b.start_date=a."Start Date"
        and b.end_date=a."End Date"
        and trim(a."Preliminary Offer Name")=trim(b."Preliminary Offer Name")
        order by "Event Name","Preliminary Offer Name"
"""


SIGNAGE_SET = """
        with base as
        ((select offer_name as "Preliminary Offer Name", Offer_Type_SAP as "Offer Type SAP","Offer Type Promosmart",promo_id,
        div_cd as "Div. #", dept_cd as "Dept. #", class_cd as "Class #", class_nm as "Class Desc",subcls_cd as "Subclass #", subcls_nm as "Subclass Desc",Sku as "Sku", descr as "Description",comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item as "AD Feature Item", Email_Feature_Item as "Email Feature Item", "Status" as "Status (in store or AD PO)", GRM_Flow as "GRM_Flow (Y or N)",cost as "Cost",
        current_retail as "Current Retail",effective_discount,
        "IMU" as "IMU %",
        "disc_from_Current_Retail" as "% Discount from Current Retail",
        event_retail as "Event Retail","event_retail_with_100%_redemption", "% off from current retail (with 100% redemption)",
        round((percent_Message:: Decimal),2) as "% Message",
        round(abs(dollar_Message :: Decimal),2) as "$ Message",
        case when "Discount_from_Event_Retail"<0 then 0 else "Discount_from_Event_Retail" end as "% Discount from Event Retail",
        case when aur>event_retail then event_retail else aur end as "Selling AUR",baseline_sales_units as "Baseline Sales Units",baseline_sales_all_store as "Baseline # of Stores",baseline_UPAs as "Baseline UPAS",
        baseline_sales as "Baseline Sales $", baseline_GM_$ as "Baseline GM $",UPAs_build as "UPAS Build", forecast_sales_units as "Forecast Sales Units" ,forecast_sales_all_store as "Forecast # of Stores",
        forecast_UPAs as "Forecast UPAS", forecast_sales as "Forecast Sales $", forecast_event_MD as "Forecast Event MD $",forecast_GM_$ as "Forecast GM $",
        unit_lift as "Unit Lift", sales_diff as "Sales $ Difference",sales_lift as "$ Lift",margin_diff as "GM $ Difference",
        AD_Prep_Units as "AD Prep Units", as_Units as "A/S Units", Ad_Prep_$ as "Ad Prep $", Sell_Through as "Sell Through",
        Comments_m as "Comments (Basis for Projection)", Marketing_Specific_Comments as "Marketing Specific Comments"
        from
        (select Ad_Zone_Code, SAP_Subclass, Ad_Zone, offer_name, Offer_Type_SAP,promo_id,"Offer Type Promosmart",
        div_cd, dept_cd , class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item, Email_Feature_Item, "Status" , GRM_Flow ,"event_retail_with_100%_redemption",
        (1-("event_retail_with_100%_redemption"/nullif(current_retail,0))) as "% off from current retail (with 100% redemption)",
        case when "Offer Type Promosmart"='reg_price_cv' then (comp_value-current_retail)
        when "Offer Type Promosmart"='reg_price' then 0
        else ("event_retail_with_100%_redemption" - current_retail)
        end as dollar_Message,
        case when "Offer Type Promosmart"='reg_price_cv' then ((comp_value - current_retail)/nullif(comp_value,0))
        when "Offer Type Promosmart"='reg_price' then 0
        else (1-"event_retail_with_100%_redemption"/nullif(current_retail,0))
        end as percent_Message,
        round((Cost::Decimal),2) as Cost,
        effective_discount,
        round((current_retail::Decimal),2) as current_retail,
        round((IMU::Decimal),2) as "IMU",
        coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
        round((event_retail::Decimal),2) as event_retail,
        coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
        coalesce (round((aur::Decimal),2),0) as aur,
        round((baseline_sales_units::Decimal),2) as baseline_sales_units,
        round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
        coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
        round((baseline_sales::Decimal),2) as baseline_sales,
        round((baseline_GM_$::Decimal),2) as baseline_GM_$,
        coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
        round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
        round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
        coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
        round(( forecast_sales::Decimal),2) as forecast_sales,
        round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
        round((forecast_GM_$::Decimal),2) as forecast_GM_$,
        coalesce (round((unit_lift::Decimal),2),0) as unit_lift,
        round(( sales_diff::Decimal),2) as sales_diff,
        coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
        round((margin_diff::Decimal),2) as margin_diff,
        AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
        Comments_m, Marketing_Specific_Comments
        from (
        select Ad_Zone_Code, SAP_Subclass,
        'CHAIN' as Ad_Zone,
        offer_name,b.promo_id,
        case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
        when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
        when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'Reg Price'
        else 'BOGO (APR Offer)' end as Offer_Type_SAP,Offer_Type as "Offer Type Promosmart",effective_discount,
        div_cd, dept_cd , dept_nm ,
        class_cd , class_nm , subcls_cd, subcls_nm,Sku, descr,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item, Email_Feature_Item,
        "Status",GRM_Flow,
        cost, current_retail,
        (1 - (cost/nullif(current_retail,0))) as IMU,
        case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
        case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                when Offer_Type = 'fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail end as event_retail,
        case when Offer_Type='fixed_price' then offer_value
        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
        else current_retail*(1-effective_discount/100)
        end as "event_retail_with_100%_redemption",
        (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
        aur,baseline_sales_units , baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units,forecast_sales_all_store ,
        forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$, unit_lift , sales_diff, sales_lift, margin_diff,
        AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
        from (
        select SAP_Subclass, 'CHAIN' as "Ad_Zone",offer_name, Offer_Type ,div_cd,dept_cd,dept_nm,class_cd,class_nm,subcls_cd,subcls_nm,Sku,
        descr,promo_id,comp_value,"web designation","store pickup eligible",event_name,start_date , end_date,min_percent_value,
        AD_Feature_Item,Email_Feature_Item, "Status",GRM_Flow,
        avg(offer_value) as offer_value,
        avg(effective_discount) as effective_discount,
        case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(cost) end as cost,
        case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
        else avg(current_retail) end as current_retail,
        max(Ad_Zone_Code) as Ad_Zone_Code,
        max(IMU) as IMU ,
        max(disc_from_CR) as disc_from_Current_Retail,
        max(event_retail) as event_retail,
        max(disc_from_ER) as disc_from_ER,
        sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
        sum(baseline_sales_units) as baseline_sales_units,
        avg(baseline_sales_all_store) as baseline_sales_all_store,
        sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
        sum(baseline_sales) as baseline_sales ,
        sum(baseline_GM_$) as baseline_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
        sum(forecast_sales_units) as forecast_sales_units,
        avg(forecast_sales_all_store) as forecast_sales_all_store,
        sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
        sum(forecast_sales) as forecast_sales,
        sum(forecast_event_MD) as forecast_event_MD,
        sum(forecast_GM_$) as forecast_GM_$,
        sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as unit_lift,
        sum(sales_diff) as sales_diff,
        sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
        sum(margin_diff) as margin_diff,
        max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
        max(Sell_Through) as Sell_Through,
        max(Comments_m) as Comments_m,
        max(Marketing_Specific_Comments) as Marketing_Specific_Comments
        from (
        select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,
        pm.name as offer_name,
        concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
        tasm.name as Offer_Type, t.effective_discount ,t.promo_id as promo_id,case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
        im.division_id as div_cd, im.department_id as dept_cd, im.department_nm as dept_nm,
        im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
        im.product_h5_id as Sku, im.item_nm as descr,comp_value,"web designation","store pickup eligible",event_name,pm.start_date ,pm.end_date,em.min_percent_value,
        null as AD_Feature_Item,
        null as Email_Feature_Item, 'In Store' as "Status", null as GRM_Flow,
        t.recommendation_date as dt,
        im.cost as cost,
        im.price as current_retail,
        '1 - cost/CR' as IMU,
        'use case when on %off' as disc_from_CR,
        'CR * (1-%offfromCR)' as event_retail,
        '1 - AUR/ER' as disc_from_ER,
        t.aur as aur, t.baseline_sales_units,
        1435 as baseline_sales_all_store,
        'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
        t.baseline_revenue as baseline_sales,
        t.baseline_margin as baseline_GM_$,
        0 as UPAs_build,
        t.sales_units as forecast_sales_units,
        1435 as forecast_sales_all_store,
        'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
        t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
        'forecast units / bs units' as unit_lift,
        (t.revenue - t.baseline_revenue) as sales_diff,
        'forecast sales / bs sales' as sales_lift,
        (t.margin - t.baseline_margin ) as margin_diff,
        null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
        null as Comments_m, null as Marketing_Specific_Comments
        from simulation.ps_recommended_finalized as t 
        inner join ( select event_id ,name as event_name, start_date,end_date,min_percent_value from public.event_master 
        {% if event_id %}
                where event_id in {{event_id | inclause}}
        {% endif %}
        group by 1,2,3,4) as em
        on t.event_id = em.event_id
        inner join (select * from public.promo_master where status=4
        {% if promo_id %}
                and promo_id in {{promo_id | inclause}}
        {% endif %}
        ) as pm 
        on pm.promo_id = t.promo_id and pm.event_id = t.event_id
        and t.recommendation_date between pm.start_date and pm.end_date
        inner join (select product_h1_id as division_id , product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
        product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,cost,price,comp_value,case when WEB_DESIGNATION_CD=1 then 'web enabled' else null end as "web designation",
        case when STORE_PICKUP_ELIGIBLE_IND=1 then 'in store' else null end as "store pickup eligible"
        From public.product_master where product_h2_id in {{product_h2 | inclause}}) as im 
        on cast(im.product_h5_id as integer) = t.item_id
        left join metaschema.tb_app_sub_master as tasm
        on t.offer_type_id = tasm.id
        ) as a
        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
        ) as b
        left join
        promo_products as pp
        on b.promo_id=pp.promo_id
        and cast(b.Sku as integer)=pp.item_id
        )as c
        ) as d
        where current_retail >0.01)),
        base_2 as
        (select a.*,msi.store_with_inventory as "%_Store_with_inventory",case when md.count_image > 0 then 'Y' else 'N' end as "Image Exists"
        from base a
        left join public.mkt_store_inventory msi
        on a."Sku"=msi.item_id
        left join
        (
        select sku_list as "Sku",count(distinct "Dynamic Media Link") as count_image
        from public.mkt_dam group by 1
        ) as md
        on a."Sku"=md."Sku"),
        offer_x_y_value as
        (WITH promo_product_store as (
        SELECT
        cast(sm.scenario_id as int4) as scenario_id ,
        sm.promo_id,
        pp.item_id,
        pm2."name",
        CASE
        WHEN discount_level = -200 THEN -200
        WHEN discount_level = 1 THEN pm.product_h1_id
        WHEN discount_level = 2 THEN pm.product_h2_id
        WHEN discount_level = 3 THEN pm.product_h3_id
        WHEN discount_level = 4 THEN pm.product_h4_id
        WHEN discount_level = 5 THEN pm.product_h5_id
        END AS discount_level_value
        from
        (select * from public.promo_master
        where status= 4 and last_approved_scenario_id is not null
        )pm2
        inner join
        public.scenario_master sm
        on pm2.promo_id=sm.promo_id
        and pm2.last_approved_scenario_id=sm.scenario_id
        LEFT JOIN ( SELECT promo_id, item_id FROM public.promo_products group by 1,2) pp on pm2.promo_id=pp.promo_id
        LEFT JOIN public.product_master pm ON pm.product_h5_id = pp.item_id
        )
        SELECT
        pps.*,
        psd.offer_type,
        psd.offer_value,
        psd.offer_type_id,
        offer_x_value,
        offer_y_value
        FROM
        promo_product_store pps
        INNER JOIN (
        SELECT
        discount_level_value,
        scenario_id,
        offer_value,
        offer_x_value,
        offer_y_value,
        offer_type_id,
        tasm."name" as offer_type
        FROM public.ps_scenario_discounts pd
        INNER JOIN
        metaschema.tb_app_sub_master tasm ON pd.offer_type_id = tasm.id
        ) psd USING (discount_level_value, scenario_id) where offer_type in
        ('bxgy','bxgy_$','bmsm_$','extra_amount_off')),
        final as 
        (select a.*,
        case when "offer_type" = 'bmsm_$' then concat('Buy',' ','$',b.offer_x_value,' ','Get',' ','$',b.offer_y_value,' ','off')
        when "offer_type" = 'bxgy' then b.offer_value
        when "offer_type" = 'bxgy_$' then concat('Buy',' ',b.offer_x_value,' ','for',' ','$',b.offer_y_value)
        else null end as "BOGO_Bundle_BMSM_Message"
        from base_2 a
        left join offer_x_y_value b
        on a.promo_id=b.promo_id
        and a."Sku"=b.item_id),
        combo as 
        (With Product_Combo as
        (Select a.id as "Sku", a.combo,
        REGEXP_REPLACE(combo, 'combo([0-9]+)', '\1') AS extracted_number,
        b. Total_SKUS_in_Combo
        from (select * from public.mkt_combo_sku_attribute
        where combo like '%combo%'
        ) a
        inner join
        (Select combo, count(distinct(id)) as Total_SKUS_in_Combo from
        public.mkt_combo_sku_attribute
        where combo like '%combo%'
        group by 1
        ) b
        using (combo)
        ),
        Mkt_Extract as (Select * from final
        where "Sku" is not null),
        Combo_Flag as (
        Select a.*,
        case when b. "Sku" is not null then "Sku" else null end as SKU_Combo,
        case when b. combo is not null then  combo else null end as Combo_Name,
        case when b. Total_SKUS_in_Combo is not null then  Total_SKUS_in_Combo else null end as Total_Combo_SKUs,
        b. extracted_number
        from Mkt_Extract a
        left join
        Product_Combo b
        using ("Sku")
        ),
        Count_Flag as (Select Combo_Name, event_name,"Preliminary Offer Name", count(distinct("Sku")) as Promoted_Combo_SKUs, sum("Current Retail") as Combo_Current_Retail,
        sum("event_retail_with_100%_redemption") as  Combo_Event_Retail
        from Combo_Flag
        group by 1,2,3
        ),
        Combo_Indicator as (Select a.*,
        case when b.Promoted_Combo_SKUs = a.Total_Combo_SKUs then 'Complete Combo' else 'Incomplete' end as Completion_Flag, b. Combo_Current_Retail, b. Combo_Event_Retail
        from Combo_Flag a
        left join
        Count_Flag b
        using (Combo_Name, event_name,"Preliminary Offer Name")
        ),
        Final_Combo_Table as (
        select "Preliminary Offer Name",event_name, "Sku",
        Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, Combo_Name, extracted_number
        from
        (Select *,
        case when Completion_Flag= 'Incomplete' then null else Combo_Current_Retail end as Combination_Current_Retail,
        case when Completion_Flag= 'Incomplete' then null else Combo_Event_Retail end as Combination_Event_Retail from Combo_Indicator
        ) as a
        ),
        Output as (
        (Select "Preliminary Offer Name", event_name, "Sku", Combination_Current_Retail,Combination_Event_Retail, Completion_Flag, 
        case when Completion_Flag='Incomplete' then null else Combo_Name end as Combo_Name
        from
        (Select a.*, b. min_number from Final_Combo_Table a
        inner join
        (Select "Preliminary Offer Name",event_name, "Sku",min(extracted_number) as min_number from Final_Combo_Table
        group by 1,2,3
        ) b
        using ("Preliminary Offer Name",event_name, "Sku")
        ) a
        where min_number= extracted_number or min_number is null
        ))
        Select * from Output),
        sku_offer as (
        select event_name as "Event Name",start_date as "Start Date",end_date as "End Date","Preliminary Offer Name",promo_id as "Promo ID","Offer Type SAP","Offer Type Promosmart","Div. #","Dept. #","Class #", "Class Desc","Subclass #","Subclass Desc","Sku","Description","AD Feature Item",ROUND("Current Retail",2) as "Current Retail",comp_value as "Comp Value",ROUND("Event Retail",2) as "Event Retail",ROUND("% Discount from Current Retail",2) as "% Discount from Current Retail",ROUND(cast("event_retail_with_100%_redemption" as numeric),2) as "Event Retail With 100% Redemption",ROUND(cast("% off from current retail (with 100% redemption)" as numeric),2) as "% Off From Current Retail (With 100% Redemption)",combination_event_retail  as "Combination Event Retail",combination_current_retail as "Combination Current Retail",
        ROUND("% Message",2) as "% Message","$ Message","BOGO_Bundle_BMSM_Message" as "BOGO Bundle BMSM Message","Selling AUR",                                                                                                                                                                                                        
        "store pickup eligible" as "Available In Stores Ind","web designation" as "Web Enabled Ind",url as "Url","Image Exists",ROUND(cast("%_Store_with_inventory" as numeric),1) as "% Store With Inventory",                                                                                                                                                                                                        
        "Baseline Sales Units","Baseline Sales $","Baseline GM $","Forecast Sales Units","Forecast Sales $","Forecast Event MD $","Forecast GM $","Sales $ Difference","GM $ Difference",Completion_Flag as "Combo Completion Flag",min_percent_value as "Min Recommended Discount" from                                                                                                                                                                                                        
        (select a.*,case when combo_url is not null then combo_url
        when "web designation" = 'web enabled' then prod_url
        else null end as url
        from 
        (select a.*,case when b.Combo_Name is null then null else concat('https://www.biglots.com/product/', b.Combo_Name) end as combo_url,
        concat('https://www.biglots.com/product/p',a."Sku") as prod_url,b.Combination_Current_Retail,b.Combination_Event_Retail,b.Completion_Flag
        from final a
        left join combo b 
        on a."Sku"=b."Sku"
        and a."Preliminary Offer Name"=b."Preliminary Offer Name"
        and a.event_name=b.event_name) as a ) as a),
        Marketing_Set as (
        Select a.*, b. price as Current_Retail_Price_PM, 
        b. price as Event_Retail_Price_PM from
        (SELECT distinct childskus,id FROM public.mkt_set
        where childskus is not null
        ) a
        inner join public.product_master b 
        on a. childskus=b.product_h5_id),
        Set_Offer_Combinations as 
        (Select distinct "Event Name", "Preliminary Offer Name", "Start Date", "End Date", id as set_name from 
        (Select a.*, b. * from sku_offer a
        left join
        Marketing_Set b  
        on b.childskus= a."Sku") a
        where id is not null
        ),
        SKU_Set_Values as 
        (Select a.*, b.childskus as "Sku", b. Current_Retail_Price_PM, 
        b. Event_Retail_Price_PM
        from Set_Offer_Combinations a
        inner join 
        Marketing_Set b
        on a.set_name= b.id),
        Prices_Choice as (
        Select a.*,
        case when b. "Current Retail" is not null then b."Current Retail" else Current_Retail_Price_PM end as "Current Retail",
        case when b. "Event Retail With 100% Redemption" is not null then b."Event Retail With 100% Redemption" else Event_Retail_Price_PM end as "Event Retail Price" from SKU_Set_Values a 
        left join 
        sku_offer b
        using ("Event Name","Preliminary Offer Name","Sku" )),
        Set_Rollup as (
        Select "Event Name","Preliminary Offer Name","Start Date","End Date",set_name ,
        sum("Current Retail") as "Set Ticket Price", sum("Event Retail Price") as "Set Event Price",ARRAY_AGG(cast("Sku" as varchar))::text as set_list
        from Prices_Choice
        group by 1,2,3,4,5)
        (Select distinct a."Event Name", a."Preliminary Offer Name", a."Start Date", a."End Date",
        a.set_name as "Set Name",a."Sku",pm.product_h5_name as "Description",pm.product_h1_name as "Division",pm.product_h2_name as "Department",product_h3_name as "Class Desc",product_h4_name as "Subclass Desc", a."Current Retail",b."Set Ticket Price",a."Event Retail Price", b."Set Event Price",b. set_list as "Set List"
        from Prices_Choice a 
        inner join 
        Set_Rollup b
        using (set_name,"Event Name","Preliminary Offer Name")
        inner join public.product_master pm 
        on a."Sku"=pm.product_h5_id
        )  
        order by "Event Name","Preliminary Offer Name"
"""
        