from report_data_models.query_store import *
from client_configuration import constants as client_configuration_constants

REPORT_PARAMETERS = [
    {
        "name": "report_name",
        "data_type": "str",
        "formate": "str",
        "default": "",
        "validation": {
            "expression": "A",
            "message": "provided parameter must be in the proper format"
        }
    },
    {
        "name": "product_h1",
        "data_type": "str",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "A",
            "message": "provided parameter must be in the proper format"
        }
    },
    {
        "name": "product_h2",
        "data_type": "int",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "A",
            "message": "provided parameter must be in the proper format"
        }
    },
    {
        "name": "product_h3",
        "data_type": "int",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "A",
            "message": "provided parameter must be in the proper format"
        }
    },
    {
        "name": "product_h4",
        "data_type": "int",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "A",
            "message": "provided parameter must be in the proper format"
        }
    },
    {
        "name": "event_id",
        "data_type": "int",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "validate_date(startDate)",
            "message": "provided parameter value must be numeric"
        }
    },
    {
        "name": "promo_id",
        "data_type": "int",
        "formate": "list",
        "default": "",
        "validation": {
            "expression": "validate_date(startDate)",
            "message": "provided parameter value must be numeric"
        }
    },
    {
        "name": "start_date",
        "data_type": "date",
        "formate": "MM/DD/YYYY",
        "default": "default_start_date",
        "validation": {
            "expression": "(start_date)",
            "message": "provided parameter must be numeric"
        }
    },
    {
        "name": "end_date",
        "data_type": "date",
        "formate": "MM/DD/YYYY",
        "default": "default_end_date",
        "validation": {
            "expression": "(end_date)",
            "message": "provided parameter must be in the proper format"
        }
    }
]

DATA_MODELS = {
    "17": {
        "pre_actions": [],
        "post_actions": [],
        "query": SAP_EXTRACT,
        "sheet-name": "SAP-Data",
        "parameters": REPORT_PARAMETERS
    },
    "18": {
        "pre_actions": [],
        "post_actions": [],
        "query": MARKETING_SKU_LEVEL,
        "sheet-name": "SKU",
        "parameters": REPORT_PARAMETERS
    },
    "19": {
        "pre_actions": [],
        "post_actions": [],
        "query": MARKETING_OFFER_LEVEL,
        "sheet-name": client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize(),
        "parameters": REPORT_PARAMETERS
    },
    "20": {
        "pre_actions": [],
        "post_actions": [],
        "query": MARKETING_OFFER_LEVEL,
        "sheet-name": client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize(),
        "parameters": REPORT_PARAMETERS
    },
    "21": {
        "pre_actions": [],
        "post_actions": [],
        "query": MARKETING_DAM_LEVEL,
        "sheet-name": "DAM",
        "parameters": REPORT_PARAMETERS
    },
    "22": {
        "pre_actions": [],
        "post_actions": [],
        "query": SIGNAGE_SKU_LEVEL,
        "sheet-name": "SKU",
        "parameters": REPORT_PARAMETERS
    },
    "23": {
        "pre_actions": [],
        "post_actions": [],
        "query": SIGNAGE_OFFER_LEVEL,
        "sheet-name": client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize(),
        "parameters": REPORT_PARAMETERS
    },
    "24": {
        "pre_actions": [],
        "post_actions": [],
        "query": SIGNAGE_SET,
        "sheet-name": "Set",
        "parameters": REPORT_PARAMETERS
    },
    "25": {
        "pre_actions": [],
        "post_actions": [],
        "query": SKU_EXTRACT,
        "sheet-name": "SKU-Extract",
        "parameters": REPORT_PARAMETERS
    },
    "26": {
        "pre_actions": [],
        "post_actions": [],
        "query": FILTERED_SKU_EXTRACT,
        "sheet-name": "SKU-Extract",
        "parameters": REPORT_PARAMETERS
    }
}
