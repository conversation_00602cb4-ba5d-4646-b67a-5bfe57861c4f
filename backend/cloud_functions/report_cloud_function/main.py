import functions_framework
from report_data_models.utils import process_model_ids
import asyncio

@functions_framework.http
def report_generator(request):
    request_json = request.get_json(silent=True)
    id_list = request_json["id"]
    user_id = request_json["user_id"]
    parameters = request_json["parameters"]
    print(id_list,user_id,parameters)

    data = asyncio.run(process_model_ids(id_list,parameters,user_id))
    return data


