[local]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart-dev
API_ENDPOINT = http://localhost:8004/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-dev

[dev]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart-dev
API_ENDPOINT = https://pricesmart-biglots-dev.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-dev

[test]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart-test
API_ENDPOINT = https://pricesmart-biglots-test.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-test

[uat]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart-uat
API_ENDPOINT = https://pricesmart-biglots-uat.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-uat

[prod]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart
API_ENDPOINT = https://pricesmart-biglots.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-prod


[demo]
SERVICE_ACCOUNT_EMAIL = <EMAIL>
BUCKET_NAME = biglots-pricesmart-demo
API_ENDPOINT = https://pricesmart-demo.iaproducts.ai/pricesmart/api/v1/user/notify
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-demo