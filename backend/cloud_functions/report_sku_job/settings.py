from pydantic import BaseModel
import configparser
import os

class Settings(BaseModel):
    SECRET_PROJECT : str
    SECRET_ID : str
    BUCKET_NAME : str
    CLOUD_STORAGE_PATH : str
    SERVICE_ACCOUNT_EMAIL : str
    TASKS_QUEUE : str
    SFTP_URL : str
    LOCATION : str
    SFTP_REMOTE_PATH : str

config = configparser.ConfigParser()
config.optionxform = str # type: ignore

config.read("environment.ini")


environment = os.getenv("ENV") or "local"
config = {**config[environment]}
settings = Settings(**config)