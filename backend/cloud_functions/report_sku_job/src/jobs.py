from datetime import datetime, timedelta

from settings import settings
from .utils import DbConnection, generate_file_name,  publish_to_queue, save_to_cloud_storage
from .secret import secret_data

async def generate_sku_extract(start_date,end_date):
    SKU_EXTRACT_QUERY = """
        with event_promos as (
            select em.event_id,promo_id from public.event_master as em
            inner join 
            public.promo_master using (event_id)
            where em.start_date <= $2 and em.end_date >= $1
        ),
        base as
                (select Ad_Zone_Code,event_name,cast(start_date as date) as start_date,cast(end_date as date) as end_date, SAP_Subclass, Ad_Zone, offer_name, Offer_Type,tool_offer_type,
                div_cd,div_nm, dept_cd ,dept_nm, class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
                AD_Feature_Item,event_objective,ad_type,channel,
                round((Cost::Decimal),2) as Cost,
                round((current_retail::Decimal),2) as current_retail,
                round((IMU::Decimal),2) as "IMU",
                coalesce(round((disc_from_Current_Retail::Decimal),2),0) as "disc_from_Current_Retail",
                round((event_retail::Decimal),2) as event_retail,
                coalesce (round((Discount_from_Event_Retail::Decimal),2),0) as "Discount_from_Event_Retail",
                round((baseline_sales_all_store::Decimal),2) as baseline_sales_all_store,
                coalesce (round((aur::Decimal),2),0) as aur,
                round((baseline_sales_units::Decimal),2) as baseline_sales_units,
                coalesce (round((baseline_UPAs::Decimal),2),0) as baseline_UPAs,
                round((baseline_sales::Decimal),2) as baseline_sales,
                round((baseline_GM_$::Decimal),2) as baseline_GM_$,
                coalesce (round((UPAs_build::Decimal),2),0) as UPAs_build,
                round((forecast_sales_units ::Decimal),2) as forecast_sales_units ,
                round((forecast_sales_all_store::Decimal),2) as forecast_sales_all_store,
                coalesce (round((forecast_UPAs::Decimal),2),0) as forecast_UPAs,
                round(( forecast_sales::Decimal),2) as forecast_sales,
                round(( forecast_event_MD::Decimal),2) as forecast_event_MD,
                round((forecast_GM_$::Decimal),2) as forecast_GM_$,
                round(( forecast_sales_units_diff::Decimal),2) as forecast_sales_units_diff,
                round(( sales_diff::Decimal),2) as sales_diff,
                coalesce (round((sales_lift::Decimal),2),0) as sales_lift,
                round((margin_diff::Decimal),2) as margin_diff,
                AD_Prep_Units, as_Units,Ad_Prep_$, Sell_Through,
                Comments_m, Marketing_Specific_Comments
                from (
                select Ad_Zone_Code, SAP_Subclass, start_date,end_date,
                'CHAIN' as Ad_Zone,event_objective,
                event_name,offer_name,
                case when (tool_offer_type = 'percent_off') then 'percent_off'
                when (tool_offer_type = 'fixed_price') then 'fixed_price'
                when (tool_offer_type = 'extra_amount_off') then 'extra_amount_off'
                when (tool_offer_type = 'reg_price_cv') then 'reg_price_cv'
                when (tool_offer_type = 'reg_price') then 'reg_price'
                when (tool_offer_type = 'bxgy') then 'bxgy'
                when (tool_offer_type = 'bundle_offer') then 'bundle_offer'
                when (tool_offer_type = 'bxgy_$') then 'bxgy_$'
                when (tool_offer_type = 'bmsm_$') then 'bmsm_$'
                else 'BOGO (APR Offer)' end as tool_offer_type,
                case when ( Offer_Type = 'percent_off' ) then 'Temp % off'
                when ((Offer_Type = 'fixed_price' or Offer_Type= 'extra_amount_off')) then 'Temp $ off'
                when ((Offer_Type = 'reg_price_cv' or Offer_Type='reg_price' )) then 'reg_price'
                else 'BOGO (APR Offer)' end as Offer_Type,
                case when (ad_type = '1') then 'Advertised'
                    when (ad_type = '2') then 'Unadvertised' end as ad_type,
                case when (channel_type = '22') then 'Ecomm Only'
                    when (channel_type = '23') then 'Omni Channel' end as channel,
                div_cd,div_nm,dept_cd , dept_nm ,
                class_cd , class_nm , subcls_cd, subcls_nm,brand,Sku, descr,
                case when pp.is_hero=1 then 'yes' else null end as AD_Feature_Item,
                cost, current_retail,
                (1 - (cost/nullif(current_retail,0))) as IMU,
                case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end as disc_from_Current_Retail,
                case when Offer_Type in ('percent_off','reg_price','reg_price_cv') then current_retail*(1-effective_discount/100)
                        when Offer_Type = 'fixed_price' then offer_value
                        when Offer_Type = 'extra_amount_off' then (current_retail-offer_value)
                        else current_retail end as event_retail,
                (1-(aur/nullif((current_retail * (1 -( case when Offer_Type = 'percent_off' then effective_discount/100 else 0 end ))),0))) as Discount_from_Event_Retail,
                aur,baseline_sales_units, baseline_sales_all_store,baseline_UPAs, baseline_sales, baseline_GM_$,UPAs_build,forecast_sales_units, forecast_sales_all_store ,
                forecast_UPAs, forecast_sales, forecast_event_MD,forecast_GM_$,forecast_sales_units_diff, sales_diff, sales_lift, margin_diff,
                AD_Prep_Units, as_Units, Ad_Prep_$, Sell_Through, Comments_m, Marketing_Specific_Comments
                from (
                select SAP_Subclass, 'CHAIN' as "Ad_Zone",event_name,offer_name, Offer_Type,tool_offer_type , start_date,end_date,
                div_cd,div_nm, dept_cd, dept_nm, class_cd, class_nm, subcls_cd, subcls_nm, brand,
                Sku, descr,promo_id,channel_type,
                AD_Feature_Item,event_objective,ad_type,
                avg(effective_discount) as effective_discount ,
                case when sum(baseline_sales_units)> 0 then sum(cost*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(cost) end as cost,
                case when sum(baseline_sales_units)> 0 then sum(current_retail*baseline_sales_units)/ nullif(sum(baseline_sales_units),0)
                else avg(current_retail) end as current_retail, avg(offer_value) as offer_value,
                max(Ad_Zone_Code) as Ad_Zone_Code,
                max(IMU) as IMU ,
                max(disc_from_CR) as disc_from_Current_Retail,
                max(event_retail) as event_retail,
                max(disc_from_ER) as disc_from_ER,
                sum(forecast_sales)/ nullif(sum(forecast_sales_units),0) as aur,
                sum(baseline_sales_units) as baseline_sales_units,
                avg(baseline_sales_all_store) as baseline_sales_all_store,
                sum(baseline_sales_units)/ nullif(avg(baseline_sales_all_store),0) as baseline_UPAs,
                sum(baseline_sales) as baseline_sales ,
                sum(baseline_GM_$) as baseline_GM_$,
                sum(forecast_sales_units) / nullif(sum(baseline_sales_units),0) as UPAs_build,
                sum(forecast_sales_units) as forecast_sales_units,
                avg(forecast_sales_all_store) as forecast_sales_all_store,
                sum(forecast_sales_units) / nullif(avg(forecast_sales_all_store),0) as forecast_UPAs,
                sum(forecast_sales) as forecast_sales,
                sum(forecast_event_MD) as forecast_event_MD,
                sum(forecast_GM_$) as forecast_GM_$,
                sum(forecast_sales_units_diff) as forecast_sales_units_diff,
                sum(sales_diff) as sales_diff,
                sum(forecast_sales)/nullif(sum(baseline_sales),0) as sales_lift,
                sum(margin_diff) as margin_diff,
                max(AD_Prep_Units) as AD_Prep_Units, max( as_Units) as as_Units, max(Ad_Prep_$) as Ad_Prep_$,
                max(Sell_Through) as Sell_Through,
                max(Comments_m) as Comments_m,
                max(Marketing_Specific_Comments) as Marketing_Specific_Comments
                from (
                select (cast(im.class_code_id as integer)*1000 + cast(im.subclass_id as integer) ) as SAP_Subclass,pm.start_date as start_date, pm.end_date as end_date,
                pm.name as offer_name, em.channel_type as channel_type,em.ad_type as ad_type,em.name as event_name,
                concat(cast((extract(month from em.start_date)) as text),(case when (extract(day from em.start_date))>9 then cast((extract(day from em.start_date)) as text) else cast((concat('0',(extract(day from em.start_date)))) as text) end),cast((extract(year from em.start_date)-2000) as text),'A') as Ad_Zone_Code,
                t.effective_discount ,t.promo_id as promo_id,em.event_objective as event_objective,
                tasm.name as Offer_Type, tasm.name as tool_offer_type, case when tasm.name in ('fixed_price' ,'extra_amount_off') then cast(t.offer_value as float) else 1 end as offer_value,
                im.division_id as div_cd,im.division_nm as div_nm, im.department_id as dept_cd, im.department_nm as dept_nm,im.brand as brand,
                im.class_code_id as class_cd, im.class_code_nm as class_nm, im.subclass_id as subcls_cd, im.subclass_nm as subcls_nm,
                im.product_h5_id as Sku, im.item_nm as descr,
                null as AD_Feature_Item,
                t.recommendation_date as dt,
                im.cost as cost,
                im.price as current_retail,
                '1 - cost/CR' as IMU,
                'use case when on %off' as disc_from_CR,
                'CR * (1-%offfromCR)' as event_retail,
                '1 - AUR/ER' as disc_from_ER,
                t.aur as aur, t.baseline_sales_units as baseline_sales_units,
                1435 as baseline_sales_all_store,
                'baseline_sales_units/baseline_sales_all_store' as baseline_UPAs,
                t.baseline_revenue as baseline_sales,
                t.baseline_margin as baseline_GM_$,
                0 as UPAs_build,
                t.sales_units as forecast_sales_units,
                1435 as forecast_sales_all_store,
                'forecast_sales_units/forecast_sales_all_store' as forecast_UPAs, t.revenue as forecast_sales,
                t.promo_spend as forecast_event_MD, t.margin as forecast_GM_$,
                (t.sales_units-t.baseline_sales_units) as forecast_sales_units_diff,
                (t.revenue - t.baseline_revenue) as sales_diff,
                'forecast sales / bs sales' as sales_lift,
                (t.margin - t.baseline_margin ) as margin_diff,
                null as AD_Prep_Units, null as as_Units, null as Ad_Prep_$, null as Sell_Through,
                null as Comments_m, null as Marketing_Specific_Comments
                from simulation.ps_recommended_finalized as t
                inner join ( select event_id ,name, start_date , end_date,event_objective,channel_type,ad_type from public.event_master 
                where event_id in (select event_id from event_promos)
                group by 1,2,3,4,5,6) as em
                on t.event_id = em.event_id
                inner join (
                    select * from public.promo_master 
                    where status=4 and promo_id in (select promo_id from event_promos)
                ) as pm
                on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                and t.recommendation_date between pm.start_date and pm.end_date
                inner join (select product_h1_id as division_id , product_h1_name as division_nm, product_h2_id as department_id , product_h2_name as department_nm , client_classid as class_code_id ,
                product_h3_name as class_code_nm , client_subclassid as subclass_id , product_h4_name as subclass_nm , product_h5_id, product_h5_name as item_nm,brand as brand,cost,price
                From public.product_master
                ) as im
                on cast(im.product_h5_id as integer) = t.item_id
                left join metaschema.tb_app_sub_master as tasm
                on t.offer_type_id = tasm.id
                ) as a
                group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
                ) as b
                inner join
                promo_products as pp
                on b.promo_id=pp.promo_id
                and cast(b.Sku as integer)=pp.item_id
                )as c
                where current_retail >0.01
                ),
            base_2 as
                (select u.*,case when u.Performance <-0.05 then 'toxic' when u.Performance between -0.05 and 0.05 then 'average' else 'good' end as Performance_cal
                from
                (select y.*,y.incremental_per/nullif(y.baseline_per,0) as performance
                from
                (select em."name" as event_name,pm.start_date,pm.end_date,pm."name" as offer_name,item_id as item_id,
                avg(sum(t.incremental_margin)) over (partition by pm.promo_id,pm.event_id) as incremental_per,
                avg(sum(t.baseline_margin)) over (partition by pm.promo_id,pm.event_id) as baseline_per,
                sum(t.sales_units) as Actual_Sales_Units,sum(t.revenue) as Actual_Sales_$,sum(t.baseline_sales_units) as Actual_Baseline_Sales_Units,
                sum(t.incremental_sales_units) as Incremental_Sales_Units,sum(t.baseline_revenue) as Actual_Baseline_Revenue,
                sum(t.incremental_revenue) as Actual_Incremental_Revenue,sum(t.margin) as Actual_Margin,sum(t.baseline_margin) as Actual_Baseline_Margin,
                sum(t.incremental_margin) as Actual_Incremental_Margin,
                sum(promo_spend) as Actual_MD,
                sum(t.sales_units-t.baseline_sales_units) as actual_sales_unit_diff,
                sum(t.sales_units)/1435 as Actual_UPAs,
                sum(t.ecom_actual_units) AS ecom_actual_units,
                sum(t.ecom_actual_revenue) AS ecom_actual_revenue,
                sum(t.ecom_actual_margin) AS ecom_actual_margin
                from simulation.ps_recommended_actuals as t
                inner join public.promo_master as pm
                on pm.promo_id = t.promo_id and pm.event_id = t.event_id
                and t.recommendation_date >= pm.start_date and t.recommendation_date <= pm.end_date
                inner join event_master em
                on em.event_id =pm.event_id
                and pm.status=4
                group by 1,2,3,4,5,pm.promo_id,pm.event_id
                )as y)as u)
            select a.Ad_Zone_Code as "Ad Zone Code",a.event_name as "Event Name", a.start_date as "Start Date",a.end_date as "End Date",a.SAP_Subclass as "SAP Subclass",
                a.Ad_Zone as "Ad Zone (Chain or specific DC)", a.offer_name as "Preliminary Offer Name",
                a.Offer_Type as "Offer Type", a.tool_offer_type as "PriceSmart Offer Type",a.div_cd as "Div. #", a.div_nm as "Div Name",a.dept_cd as "Dept. #", a.dept_nm as "Dept Name",
                a.class_cd as "Class #", a.class_nm as "Class Desc",a.subcls_cd as "Subclass #", a.subcls_nm as "Subclass Desc", a.brand as "Brand",
                Sku as "Sku", descr as "Description",
                a.AD_Feature_Item as "AD Feature Item",a.event_objective as "Intent of Promo" ,a.cost as "Cost",
                a.current_retail as "Current Retail",a.ad_type as "Ad_Type",a.channel as "Channel",b.Performance_cal as "Performance",
                a."IMU"*100 as "IMU %",
                a."disc_from_Current_Retail"*100 as "% Discount from Current Retail",
                a.event_retail as "Event Retail",
                a."Discount_from_Event_Retail"*100 as "% Discount from Event Retail",
                a.aur as "Selling AUR",
                a.AD_Prep_Units as "AD Prep Units", a.as_Units as "A/S Units", a.Ad_Prep_$ as "Ad Prep $", a.Sell_Through as "Sell Through",
                a.Comments_m as "Comments (Basis for Projection)", a.Marketing_Specific_Comments as "Marketing Specific Comments",
                a.baseline_sales_all_store as "Baseline # of Stores",
                a.baseline_UPAs as "Baseline UPAS",
                a.forecast_sales_all_store as "Forecast # of Stores",
                a.forecast_UPAs as "Forecast UPAS",
                a.UPAs_build as "UPAS Build",
                a.forecast_event_MD as "Forecast Event MD $",
                a.forecast_sales_units as "Forecast Sales Units",
                a.baseline_sales_units as "Forecast Baseline Sales Units",
                a.forecast_sales_units_diff as "Forecast Incremental Units",
                a.forecast_sales as "Forecast Sales $",
                a.baseline_sales as " Forecast Baseline Sales $",
                a.sales_diff as "Forecast Incremental Sales $",
                a.baseline_GM_$ as "Forecast Baseline GM $",
                a.forecast_GM_$ as "Forecast GM $",
                a.margin_diff as "Forecast Incremental Margin $",
                b.Actual_UPAs as "Actual UPAS",
                b.Actual_MD as "Actual MD $",
                b.Actual_Sales_Units as "Actual Sales Units",
                b.Actual_Baseline_Sales_Units as "Actual Baseline Sales Units",
                b.actual_sales_unit_diff as "Actual Incremental Units",
                b.Actual_Sales_$ as "Actual Sales $",b.actual_baseline_revenue as "Actual Baseline Sales $",
                b.Actual_Incremental_Revenue as "Actual Incremental Sales $",
                b.Actual_Baseline_Margin as "Actual Baseline GM $",
                b.Actual_Baseline_Margin + b.Actual_Incremental_Margin  as "Actual GM $",
                b.Actual_Incremental_Margin as "Actual Incremental Margin $",
                b.ecom_actual_units AS "Actual Ecom Units",
                b.ecom_actual_revenue AS "Actual Ecom Revenue",
                b.ecom_actual_margin AS "Actual Ecom Margin",
                CASE WHEN b.Actual_Sales_Units = 0 then 0 
                else a.forecast_sales_units*b.ecom_actual_units/b.Actual_Sales_Units end as "Ecom Forecast Units",
                CASE WHEN b.Actual_Sales_$ = 0 then 0 
                else a.forecast_sales*b.ecom_actual_revenue/b.Actual_Sales_$ end as "Ecom Forecast Sale",
                CASE WHEN (b.Actual_Baseline_Margin + b.Actual_Incremental_Margin) = 0  then 0 
                else a.forecast_GM_$*b.ecom_actual_margin/(b.Actual_Baseline_Margin + b.Actual_Incremental_Margin) end as "Ecom Forecast Margin"

                from base as a
                left join base_2 as b
                on a.Start_Date=b.start_date
                and a.End_Date=b.end_date
                and a.Sku=b.item_id
                and upper(trim(a.offer_name))=upper(trim(b.offer_name))
                order by "Event Name","Preliminary Offer Name"
    """

    async with DbConnection() as conn:
        res = await conn.fetch(
            SKU_EXTRACT_QUERY,
            start_date,
            end_date
        )
    print(len(res))
    return res


async def generate_sku_report_weekly():
    print("entered job")
    now = datetime.now()
    start_date = now.date() + timedelta(weeks=-1) + timedelta(days = -1) # this becomes last week sunday as we are going one week and one day back
    end_date = start_date + timedelta(days = 6) # this becomes saturday which is the end of the week
    print(start_date,end_date)
    res = await generate_sku_extract(start_date,end_date)
    print(res and res[0])
    report_name = generate_file_name("sku-extracts","sku-report")
    blob = save_to_cloud_storage({"sku-data":res},report_name)
    sftp_connection_details = secret_data["sftp_connection_details"]
    body = {
        "bucket_name": settings.BUCKET_NAME,
        "blob_name": blob.name,
        "sftp_connection_details": sftp_connection_details,
        "remote_upload_path":settings.SFTP_REMOTE_PATH.format(file_name = blob.name.rsplit("/",maxsplit=1)[-1])
    }
    print(blob.name,body["remote_upload_path"])
    publish_to_queue(settings.TASKS_QUEUE,body)
    return "success"

