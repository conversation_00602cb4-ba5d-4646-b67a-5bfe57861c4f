from settings import settings
from google.cloud import secretmanager
import ast

def get_secret_data():
    client_secret_manager = secretmanager.SecretManagerServiceClient()
    SECRET_NAME = f"projects/{settings.SECRET_PROJECT}/secrets/{settings.SECRET_ID}/versions/latest"
    response = client_secret_manager.access_secret_version(name=SECRET_NAME)
    return ast.literal_eval(response.payload.data.decode("UTF-8")) # type: ignore

secret_data = get_secret_data()

