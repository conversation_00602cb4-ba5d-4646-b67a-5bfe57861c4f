from datetime import datetime, timed<PERSON>ta
from io import BytesIO
import json
import os
from tempfile import NamedTemporaryFile
from typing import Dict, List
# import subprocess

from openpyxl import Workbook

import asyncpg

from .secret import secret_data
from google.cloud import storage
from google.cloud.storage import <PERSON>lob
from settings import settings
from google.auth.transport.requests import Request
from google.auth import compute_engine
from google.cloud import tasks_v2


class DbConnection:

    async def __aenter__(self):
        self.conn = await asyncpg.connect(
            user = secret_data["db_user"],
            password = secret_data["db_password"],
            host = secret_data["db_host"],
            port = secret_data["db_port"],
            database = secret_data["db_name"],
            timeout=1200
        )
        return self.conn

    async def __aexit__(self, exc_type, exc_value, exc_tb):
        await self.conn.close()



def create_excel(workbook: Workbook,data_dict: Dict[str,List]):
    for index,(sheet_name, data) in enumerate(data_dict.items()):
        if not data:
            continue
        sheet = workbook.create_sheet(sheet_name,index = index)

        # Write the headers for the query
        sheet.append(list(data[0].keys()))

        # Write the data
        for r in data:
            sheet.append(list(r.values()))




def generate_file_name(folder,name: str):
    return f'{settings.CLOUD_STORAGE_PATH}/{folder}/{name}_{datetime.now().strftime("%m-%d-%y-%H-%M-%S")}.xlsx'


def get_blob(report_name: str) -> Blob:
    storage_client = storage.Client()
    bucket = storage_client.bucket(settings.BUCKET_NAME)
    return bucket.blob(report_name)

def write_blob(blob: Blob,output):
    with blob.open("wb") as f:
        f.write(output.read())


def save_to_cloud_storage(data:Dict[str,List],report_name: str) -> Blob:

    workbook = Workbook(write_only=True)

    create_excel(workbook,data)

    blob = get_blob(report_name)

    #saving the data to a temporary file (which gets deleted later) and getting the bytes data
    with NamedTemporaryFile() as tmp:
        workbook.save(tmp.name)
        output = BytesIO(tmp.read())

    write_blob(blob,output)

    return blob


def publish_to_queue(queue_name: str,body: Dict):
    client = tasks_v2.CloudTasksClient()
    task = tasks_v2.Task(
        http_request = tasks_v2.HttpRequest(
            http_method = tasks_v2.HttpMethod.POST,
            url = settings.SFTP_URL,
            headers = {"Content-Type": "application/json"},
            body = json.dumps(
                body
            ).encode(),
            oidc_token = tasks_v2.OidcToken(
                service_account_email = settings.SERVICE_ACCOUNT_EMAIL,
                audience = settings.SFTP_URL
            )
        ),
    )
    response = client.create_task(
        request=tasks_v2.CreateTaskRequest(parent = client.queue_path(settings.SECRET_PROJECT,settings.LOCATION,queue_name),
        task=task)
    )
    return response.name

