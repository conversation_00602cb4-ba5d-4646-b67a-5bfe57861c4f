[local]
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-dev
BUCKET_NAME = biglots-pricesmart-dev
CLOUD_STORAGE_PATH = sftp_uploads_dev
TASKS_QUEUE = pricesmart-biglots-sftp-uploads-queue-dev
SERVICE_ACCOUNT_EMAIL = <EMAIL>
SFTP_URL = https://us-central1-pricesmart-e1078.cloudfunctions.net/pricesmart-biglots-sftp-upload-job-dev
LOCATION = us-central1
SFTP_REMOTE_PATH = /sku_report/{file_name}

[dev]
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-dev
BUCKET_NAME = biglots-pricesmart-dev
CLOUD_STORAGE_PATH = sftp_uploads_dev
TASKS_QUEUE = pricesmart-biglots-sftp-uploads-queue-dev
SERVICE_ACCOUNT_EMAIL = <EMAIL>
SFTP_URL = https://us-central1-pricesmart-e1078.cloudfunctions.net/pricesmart-biglots-sftp-upload-job-dev
LOCATION = us-central1
SFTP_REMOTE_PATH = /sku_report/{file_name}

[test]
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-test
BUCKET_NAME = biglots-pricesmart-test
CLOUD_STORAGE_PATH = sftp_uploads_test
TASKS_QUEUE = pricesmart-biglots-sftp-uploads-queue-dev
SERVICE_ACCOUNT_EMAIL = <EMAIL>
SFTP_URL = https://us-central1-pricesmart-e1078.cloudfunctions.net/pricesmart-biglots-sftp-upload-job-dev
LOCATION = us-central1
SFTP_REMOTE_PATH = /sku_report/{file_name}

[uat]
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-uat
BUCKET_NAME = biglots-pricesmart-uat
CLOUD_STORAGE_PATH = sftp_uploads_uat
TASKS_QUEUE = pricesmart-biglots-sftp-uploads-queue-dev
SERVICE_ACCOUNT_EMAIL = <EMAIL>
SFTP_URL = https://us-central1-pricesmart-e1078.cloudfunctions.net/pricesmart-biglots-sftp-upload-job-dev
LOCATION = us-central1
SFTP_REMOTE_PATH = /sku_report/{file_name}

[prod]
SECRET_PROJECT = pricesmart-e1078
SECRET_ID = biglots-prod
BUCKET_NAME = biglots-pricesmart
CLOUD_STORAGE_PATH = sftp_uploads_prod
TASKS_QUEUE = pricesmart-biglots-sftp-uploads-queue-prod
SERVICE_ACCOUNT_EMAIL = <EMAIL>
SFTP_URL = https://us-central1-pricesmart-e1078.cloudfunctions.net/pricesmart-biglots-sftp-upload-job-prod
LOCATION = us-central1
SFTP_REMOTE_PATH = /sku_report/{file_name}