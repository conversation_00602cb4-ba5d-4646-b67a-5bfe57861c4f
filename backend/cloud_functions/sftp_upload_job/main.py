from io import BytesIO
import functions_framework
from paramiko import Transport, SFTPClient
import logging
from google.cloud import storage
logging.basicConfig(format='%(levelname)s : %(message)s',
                    level=logging.INFO)


def create_sftp_connection(host, port, username, password):
    transport = Transport(sock=(host, int(port)))
    transport.connect(username=username, password=password)
    client =  SFTPClient.from_transport(transport)
    if client is None:
        raise Exception("sftp connection error")
    return client

def get_data(bucket_name,blob_name):
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    with blob.open("rb") as f :
        data = f.read()

    return data


functions_framework.http
def sftp_upload(request):
    request = request.get_json()
    sftp_connection_details = request["sftp_connection_details"]
    remote_upload_path = request["remote_upload_path"]
    bucket_name = request["bucket_name"]
    blob_name = request["blob_name"]
    print(remote_upload_path,bucket_name,blob_name)

    uploading_info = lambda uploaded_file_size, total_file_size : logging.info(
            'uploaded_file_size : {} total_file_size : {}'.format(uploaded_file_size, total_file_size)
        )
    
    
    
    file_data = get_data(bucket_name,blob_name)

    client = create_sftp_connection(
        host = sftp_connection_details["host"],
        port = sftp_connection_details["port"],
        username = sftp_connection_details["username"],
        password = sftp_connection_details["password"]
    )

    file_data_io = BytesIO(file_data) # type: ignore

    client.putfo(file_data_io,remotepath= remote_upload_path, callback = uploading_info, confirm= True)
    client.close()
    return "successfully uploaded to client's sftp"