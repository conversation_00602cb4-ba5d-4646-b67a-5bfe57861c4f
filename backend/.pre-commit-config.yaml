fail_fast: true
exclude: '(frontend|model_framework|collection|optimization)/.*|queries.py'
default_language_version:
    python: python3.12
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      # 1. Prevent committing to the main branch
      - id: no-commit-to-branch
        args: [--branch,main]

      # 2. Validate YAML files
      - id: check-yaml

      # 3. Validate Python syntax tree
      - id: check-ast
        pass_filenames: false

      # 4. Ensure files end with a newline
      - id: end-of-file-fixer

      # 5. Remove trailing whitespace
      - id: trailing-whitespace

      # 6. Detect and remove debug statements
      - id: debug-statements

      # 7. Check for unresolved merge conflicts
      - id: check-merge-conflict

      # 8. Check for large files being added
      - id: check-added-large-files

      # 9. Fix and sort requirements.txt
      - id: requirements-txt-fixer
        pass_filenames: false

  - repo: local
    hooks:
      - id: custom-message
        name: Print Custom Message
        entry: echo "Running pre-commit hooks..."
        language: system
        stages: [commit, push]
        pass_filenames: false

  - repo: https://github.com/ambv/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3.12
        types: [python]
        args: ["--quiet"]

  # Flake8 and Pylint hooks (commented out)
  # - repo: https://github.com/PyCQA/flake8
  #   rev: 7.0.0
  #   hooks:
  #     - id: flake8
  #       args: [--max-line-length=120]

  # - repo: https://github.com/pycqa/pylint
  #   rev: v3.0.1
  #   hooks:
  #     - id: pylint
  #       args:
  #         [
  #           "--rcfile=./backend/pylintrc",
  #           "--extension-pkg-whitelist=pydantic"
  #         ]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]
        types: [python]

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.10.1
    hooks:
      - id: pyupgrade
        types: [python]
        pass_filenames: false

ci:
  autofix_commit_msg: All done! ✨ 🍰 ✨ \n [pre-commit.ci] Auto format from pre-commit.com hooks
  autoupdate_commit_msg: ⬆ [pre-commit.ci] pre-commit autoupdate
  skip: []
  show_diff_on_failure: true
  max_parallel: 4
  retry_failed_hooks: 2
