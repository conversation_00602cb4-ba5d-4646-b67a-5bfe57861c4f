from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from fastapi.exceptions import HTTPException
from pricesmart_common.models import BaseJSONResponse
from exceptions.constants import COMMON_ERROR_MSG
from logger.logger import logger
from fastapi import status


class CustomExceptionMiddleware(BaseHTTPMiddleware):

    def create_error_response(
        self,
        exc: Exception,
        message: str,
        status_code: int
    ):
        logger.error(f"Error: {exc}",exc_info=True)
        return BaseJSONResponse(
            message=message,
            status_code=status_code,
            success=False
        )

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            return await call_next(request)
        except HTTPException as e:
            message = e.detail
            status_code = e.status_code
            return self.create_error_response(e, message, status_code)
        except Exception as e:
            message = getattr(e, "message", COMMON_ERROR_MSG)
            status_code = getattr(e, "status_code", status.HTTP_400_BAD_REQUEST)
            return self.create_error_response(e, message, status_code)
