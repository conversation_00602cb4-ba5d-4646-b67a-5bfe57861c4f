from async_lru import alru_cache
from pricesmart_common.utils import async_execute_query
from configuration.environment import environment

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from pricesmart_common.models import BaseJSONResponse

from pricesmart_common.constants import API_PREFIX,BIGLOTS_URL_PREFIX, APP_VERSION_HEADER, APP_VERSION_MISMATCH


@alru_cache(ttl=60)
async def get_app_version():
    query = """
        select 
            remarks 
        from 
            {meta_schema}.tb_app_sub_master 
        where name='Appversion'
    """.format(
        meta_schema=environment.meta_schema
    )
    data = await async_execute_query(query=query)
    return data[0]['remarks']


class AppversionMiddleware(BaseHTTPMiddleware):

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        if request.headers.get("origin") and request.headers["origin"].startswith(BIGLOTS_URL_PREFIX):
            excluded_routes = [
                f"{API_PREFIX}/login",
                f"{API_PREFIX}/samlCustomLogin",
                f"{API_PREFIX}/register",
                f"{API_PREFIX}/logout",
                f"{API_PREFIX}/sse-output"
            ]
            if request.url.path not in excluded_routes:
                app_version = request.headers.get(APP_VERSION_HEADER)
                if request.method != 'OPTIONS':
                    latest_appversion = await get_app_version()
                    if app_version != latest_appversion:
                        return BaseJSONResponse(status_code=418, message=APP_VERSION_MISMATCH)
        return await call_next(request)
