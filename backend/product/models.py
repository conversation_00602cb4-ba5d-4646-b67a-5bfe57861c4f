from typing import Any, List, Tuple, Optional

from pricesmart_common.models import BaseResponseBody
from pydantic import BaseModel, Field, validator


# Request Models
class ProductHierarchy(BaseModel):
    l0_ids: Optional[List[int]] = None
    l1_ids: Optional[List[int]] = None
    l2_ids: Optional[List[int]] = None
    l3_ids: Optional[List[int]] = None
    l4_ids: Optional[List[int]] = None
    l5_ids: Optional[List[int]] = None
    brand: Optional[List[int]] = None
    lifecycle_indicator: Optional[List[int]] = None


class StoreHierarchy(BaseModel):
    s0_ids: Optional[List] = None
    s1_ids: Optional[List] = None
    s2_ids: Optional[List] = None
    s3_ids: Optional[List] = None
    s4_ids: Optional[List] = None
    s5_ids: Optional[List] = None


class Hierarchy(BaseModel):
    product_hierarchy: Optional[ProductHierarchy]
    store_hierarchy: Optional[StoreHierarchy]


class HierarchyFilters(BaseModel):
    filters: Hierarchy
    hierarchy_type: object
    allow_only_active_products: bool = False
    channel_type_id: Optional[int] = None


class HierarchyFilter(BaseModel):
    product_hierarchy: Optional[ProductHierarchy]


# Response Models
class HierarchyFilterData(BaseModel):
    label: str
    value: object


class HierarchyFilterDataResponse(BaseResponseBody):
    data: List[HierarchyFilterData]


class ProductDetails(BaseModel):
    products_data: List[Tuple[Any, ...]]
    event_id: Optional[int] = None

class StoreDetails(BaseModel):
    stores_data: List[Tuple[Any, ...]]
    event_id: Optional[int] = None


class ProductListRequest(BaseModel):
    event_id: Optional[int] = None
    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)


class StoretListRequest(BaseModel):
    store_code: List[int] = Field(default_factory=list)
    event_id: Optional[int] = None
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)