from __future__ import annotations

from typing import Any, List, Tuple
import pandas as pd
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from exceptions.exceptions import NoFileNameException, UnknownFileException
from fastapi import UploadFile
from filters.types import HierarchyFiltersInfoType, ExcelConfigInfoType
from pricesmart_common.data import get_config_value
from pricesmart_common.utils import get_str_repr
from product import constants as product_constants
from promotions import constants as promo_constants


def integer_converter(item):
    if isinstance(item, (float, int)):
        return int(item)

    if item.isdigit():
        return int(item)

    return str(item)


def convert_to_str(val):
    return str(val)


async def get_data_frame_from_file(file: UploadFile):
    if not file.filename:
        raise NoFileNameException()

    converters = {
        product_constants.SKU_ID_COLUMN: convert_to_str,
        # "parent_id": convert_to_str,
    }

    if file.filename.endswith(".csv"):
        # Specify the data type for the SKU_ID_COLUMN to ensure it's read as a string
        dtype = {product_constants.SKU_ID_COLUMN: str}
        # df = pd.read_csv(file.file, converters=converters)
        df = pd.read_csv(file.file, dtype=str)
    elif file.filename.endswith(".xlsx"):
        df = pd.read_excel(file.file, dtype=str)
    else:
        raise UnknownFileException()

    return df


def create_tuples_for_upload_xl(excel_config_info: dict[str,ExcelConfigInfoType], df: pd.DataFrame):

    # Desired column order (case-insensitive)
    desired_order = sorted(excel_config_info, key=lambda k: excel_config_info[k]["order"])

    # Resolve the actual column names in the desired order
    resolved_columns = [col for col in desired_order if col in df.columns]

    # Reorder the DataFrame columns
    df_ordered = df[resolved_columns]

    # Convert rows to list of tuples
    hierarchy = [tuple(str(item) for item in row) for row in df_ordered.to_numpy()]
    return hierarchy


def generate_where_str(hierarchies):
    where_arr = []
    for key, value in hierarchies.items():
        if not value:
            continue
        value = str(value).replace("[", "(").replace("]", ")")
        where_arr.append(f"{key} in {value}")
    print(where_arr)
    return promo_constants.AND.join(where_arr)


def generate_sql_clauses(data: List[Tuple[Any, ...]], excel_config_info: dict[str,ExcelConfigInfoType], module=ConfigModuleEnum.PRODUCT):
    select_sql_clause = []
    from_sql_clause = []
    join_condition = []

    source_table = "pm"
    if module == ConfigModuleEnum.STORE:
        source_table = "tsm"

    from_sql_clause = ", ".join(str(f"('{tup[0]}')") if len(tup) == 1 else str(tup) for tup in data)
    sorted_columns = [v["value_column"] for _, v in sorted(excel_config_info.items(), key=lambda item: item[1]["order"])]
    select_sql_clause = ", ".join(sorted_columns)

    for key in sorted_columns:
        join_condition.append(f"u.{key} = {source_table}.{key}::TEXT")
    

    join_condition = " AND ".join(join_condition)

    return select_sql_clause, from_sql_clause, join_condition


def generate_user_entered_details_sql_clauses(hierarchy_filters_info: dict[str,HierarchyFiltersInfoType], excel_config_info: dict[str,ExcelConfigInfoType], module=ConfigModuleEnum.PRODUCT):
    select_sql_clause = []
    final_output_select = []
    source_table = "pm"
    if module == ConfigModuleEnum.STORE:
        source_table = "tsm"

    sorted_excel_config_data = [value for _, value in 
                   sorted(excel_config_info.items(), key=lambda item: item[1]["order"])]

    for value in sorted_excel_config_data:
        select_sql_clause.append(f"u.{value["value_column"]}")
        final_output_select.append(f"'{value.get("display_name", value["value_column"])}', {value["value_column"]}")

    for value in hierarchy_filters_info.values():
        if value["id"] is None or f"u.{value["name_column"]}" in select_sql_clause:
            continue
        
        select_sql_clause.append(f"{source_table}.{value["name_column"]}")
        final_output_select.append(f"'{value.get("name_column", value["value_column"])}', {value["name_column"]}")
    
    select_sql_clause = ", ".join(select_sql_clause)
    final_output_select = ", ".join(final_output_select)

    return select_sql_clause, final_output_select


def product_generate_where_str(hierarchies):
    where_arr = []
    for key, value in hierarchies.items(): 
        if not value: continue

        where_arr.append(f" {key} in {get_str_repr(value)}")

    return promo_constants.AND.join(where_arr)

async def get_lifecycle_indicator_level_id():
    product_hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    lifecycle_indicator_level_id = product_hierarchy_filters_info["lifecycle_indicator_ids"]["id"]
    return lifecycle_indicator_level_id
