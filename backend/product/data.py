from __future__ import annotations
from typing import Optional
from configuration.environment import environment
from filters.types import HierarchyFiltersInfoType, ExcelConfigInfoType
from pricesmart_common.utils import async_execute_query
from product import queries as product_queries
from product import utils as product_utils
from product import models as product_models
from events import data as events_data_module
from events.enums import EventProductSelectionType,EventStoreSelectionType
from logger.logger import logger
from pricesmart_common.data import get_config_value
from enums.Enums import ConfigModuleEnum,ConfigKeyEnum

async def get_product_details_data(
    request_payload: product_models.ProductListRequest,
    product_hierarchies: dict
):
    client_product_id_key = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.CLIENT_PRODUCT_ID_KEY)
    product_where_str = product_utils.product_generate_where_str(product_hierarchies)
    if request_payload.event_id:
        event_data = await events_data_module.get_event_details(request_payload.event_id,"product_inclusion_type")
    else:
        event_data = {}
    if event_data.get("product_inclusion_type") in (
        EventProductSelectionType.SPECIFIC_PRODUCTS.value,
        EventProductSelectionType.WHOLE_CATEGORY.value
    ):
        event_where_str = f"""
            and pm.product_id in (
                select product_id 
                from price_promo.fn_get_products_based_on_event({request_payload.event_id})
            )
        """
    else:
        event_where_str = ""

    query = product_queries.GET_PRODUCTS_DETAILS_QUERY.format(
        product_where_str=product_where_str,
        event_where_str=event_where_str,
        client_product_id_key=client_product_id_key
    )
    logger.info(query)
    results = await async_execute_query(query)
    return results

async def get_store_details_data(
    request_payload: product_models.StoretListRequest,
):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)

    store_hierarchies = {
        store_hierarchy_config[hierarchy_key]["id_column"]: hierarchy_values
        for hierarchy_key,hierarchy_values in request_payload.store_hierarchies.items()
        if store_hierarchy_config[hierarchy_key]["id"] is not None
    }

    store_where_str = product_utils.generate_where_str(store_hierarchies)
    event_where_str = ""
    if request_payload.event_id is not None: 
        event_details = await events_data_module.get_event_details(
            request_payload.event_id,
            select="store_selection_type"
        ) 

        if (
            (
                event_details["store_selection_type"] or EventStoreSelectionType.ALL_STORES.value
            ) != EventStoreSelectionType.ALL_STORES.value
        ):
            event_where_str = f"""
                and tsm.store_id in (
                    select store_id 
                    from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                )
            """

    store_hierarchy_columns = [
        f'tsm.{config["value_column"]}'
        for config in store_hierarchy_config.values()
        if config["id"] is not None
    ]
    query = product_queries.GET_STORES_DETAILS_QUERY.format(
        store_where_str=store_where_str,
        event_where_str = event_where_str,
        store_hierarchy_columns=",".join(store_hierarchy_columns)
    )
    logger.info(query)
    results = await async_execute_query(query)
    return results


async def validate_and_get_product_data(
    request_payload: product_models.ProductDetails,
    excel_config_info: dict[str,ExcelConfigInfoType]
):

    event_details = {}
    if request_payload.event_id:
        event_details = await events_data_module.get_event_details(
            request_payload.event_id,
            select="product_inclusion_type"
        )

    event_cte = ""
    event_out_of_scope_condition = ""
    event_join_contition = ""
    if event_details.get("product_inclusion_type") in (
        EventProductSelectionType.SPECIFIC_PRODUCTS.value,
        EventProductSelectionType.WHOLE_CATEGORY.value
    ):
        event_cte = f"""event_products_cte as (
                        select 
                            product_id
                        from price_promo.fn_get_products_based_on_event(
                            {request_payload.event_id}
                        )
                    ),"""
        
        event_out_of_scope_condition = f"""when epc.product_id is null then 'out_of_scope'"""
        event_join_contition = f"""left join event_products_cte epc on epc.product_id = pm.product_id"""
            
    select_sql_clause, from_sql_clause, product_join_condition = product_utils.generate_sql_clauses(
                                                                request_payload.products_data, 
                                                                excel_config_info,
                                                                ConfigModuleEnum.PRODUCT
                                                            )
    
    product_hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )
    user_entered_product_details_cte_select_condition, final_output_select = product_utils.generate_user_entered_details_sql_clauses(
                product_hierarchy_filters_info, 
                excel_config_info,
                ConfigModuleEnum.PRODUCT
            )
    
    valid_sku_query = product_queries.GET_VALID_PRODUCT_IDS_EVENT_BASED_QUERY.format(
        user_entered_product_details_cte_select_condition=user_entered_product_details_cte_select_condition,
        select_sql_clause=select_sql_clause,
        from_sql_clause=from_sql_clause,
        product_join_condition=product_join_condition,
        final_output_select=final_output_select,
        event_out_of_scope_condition=event_out_of_scope_condition,
        event_cte=event_cte,
        event_join_contition=event_join_contition
    )

    result = await async_execute_query(query=valid_sku_query)

    response: dict = {"invalid": [], "valid": [], "inactive": [],'out_of_scope': []}

    for data in result:
        response[data["validity"]] = data["product_data"]
    return response


async def get_offer_type_details_data():
    offer_type_query = product_queries.GET_OFFER_TYPE_DETAILS_QUERY.format(
        client_schema=environment.client_schema
    )
    return await async_execute_query(query=offer_type_query)


async def validate_and_get_store_data(
    request_payload: product_models.StoreDetails,
    excel_config_info: dict[str,ExcelConfigInfoType]
):
    event_condition,event_join = "",""
    if request_payload.event_id:
        event_details = await events_data_module.get_event_details(
            request_payload.event_id,select="store_selection_type"
        )

        if (
            (
                event_details.get("store_selection_type") or 
                EventStoreSelectionType.ALL_STORES.value
            ) != EventStoreSelectionType.ALL_STORES.value
        ):
            event_condition = """
                when ies.store_id is null then 'out_of_scope'
            """
            event_join = f"""
                left join (
                    select store_id from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                ) ies
                on ies.store_id = tsm.store_id
            """

    select_sql_clause, from_sql_clause, store_join_condition = product_utils.generate_sql_clauses(
                                                                request_payload.stores_data, 
                                                                excel_config_info,
                                                                ConfigModuleEnum.STORE
                                                            )

    stores_hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )
    user_entered_store_details_cte_select_condition, final_output_select = product_utils.generate_user_entered_details_sql_clauses(
                stores_hierarchy_filters_info, 
                excel_config_info,
                ConfigModuleEnum.STORE
            )

    valid_sku_query = product_queries.GET_VALID_STORE_IDS_QUERY.format(
        select_sql_clause=select_sql_clause,
        from_sql_clause=from_sql_clause,
        store_join_condition=store_join_condition,
        user_entered_store_details_cte_select_condition=user_entered_store_details_cte_select_condition,
        final_output_select=final_output_select,
        event_join = event_join,
        event_condition = event_condition
    )
    logger.info(valid_sku_query)
    result = await async_execute_query(query=valid_sku_query)

    response: dict = {"invalid": [], "valid": [], "inactive": [],"out_of_scope": []}

    for data in result:
        response[data["validity"]] = data["store_details"]
    return response


async def get_customer_type():
    query = product_queries.GET_CUSTOMER_TYPE_QUERY.format(
        promo_schema=environment.promo_schema
    )
    result = await async_execute_query(query=query)
    return result


async def get_distribution_channel_type():
    query = product_queries.GET_OFFER_DISTRIBUTION_CHANNEL_QUERY.format(
        promo_schema=environment.promo_schema
    )
    result = await async_execute_query(query=query)
    return result


async def get_config_details_data(config):
    if config == "product_selections_type":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="price_promo", table_name="product_selection_type_config"
        )
    elif config == "store_selections_type":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="price_promo", table_name="store_selection_type_config"
        )
    elif config == "promo_status":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="price_promo", table_name="promo_status_config"
        )
    elif config == "offer_distribution_channel":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="price_promo", table_name="tb_offer_distributor_channel_config"
        )
    elif config == "customer_type":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="price_promo", table_name="tb_customer_type_config"
        )
    elif config == "group":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="global", table_name="tb_group_config"
        )
    elif config == "lifecycle_indicator":
        config_details_query = product_queries.FETCH_CONFIG_DETAILS_QUERY.format(
            schema="global", table_name="tb_lifecycle_indicator_config"
        )

    return await async_execute_query(query=config_details_query)
