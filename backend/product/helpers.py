from product import constants as product_constants
from pricesmart_common.utils import get_key_value_str, escape_quotes


class FilterModel:

    def __init__(self, filters):
        self.prod_filters = filters[product_constants.PRODUCT_HIERARCHY]
        self.store_filters = filters[product_constants.STORE_HIERARCHY]
        self.modified_prod_filters = {}
        self.modified_store_filters = {}

        self.where_arr = []
        self.where_str = ""
        self.errors = []

        for key in self.prod_filters:
            if self.prod_filters[key] is not None:
                self.modified_prod_filters[key] = self.prod_filters[key]

        for key in self.store_filters:
            if self.store_filters[key] is not None:
                self.modified_store_filters[key] = self.store_filters[key]

    def build_where_arr(self, build_config):
        table_name = build_config["table_name"]
        table_alias = build_config.get("table_alias", "")

        if table_alias:
            table_alias = f"{table_alias}."

        if table_name == product_constants.PRODUCT_TABLE:
            mod_filters = self.modified_prod_filters.copy()

            for key in mod_filters:
                if mod_filters[key] and (key in product_constants.PRODUCT_HIERARCHIES):
                    if key == product_constants.BRAND:
                        col_name = product_constants.PH_NAME_MAPPING[key]
                    else:
                        col_name = product_constants.PH_ID_MAPPING[key]
                    self.where_arr.append(get_key_value_str(f"{table_alias}{col_name}", mod_filters[key]))

        if table_name == product_constants.STORE_TABLE:
            mod_filters = self.modified_store_filters.copy()

            for key in mod_filters:
                if mod_filters[key] and (key in product_constants.STORE_HIERARCHIES):
                    col_name = product_constants.SH_ID_MAPPING[key]
                    self.where_arr.append(get_key_value_str(f"{table_alias}{col_name}", escape_quotes(mod_filters[key])))

    def extend_where_arr(self, string):
        self.where_arr.append(string)

    def reset_where_arr(self):
        self.where_arr = []

    def build_where_str(self):
        if len(self.where_arr) > 0:
            self.where_str = (
                    " where " + " \nand ".join(self.where_arr)
            )
        else:
            self.where_str = ""

    def extend_where_str(self, string):
        if self.where_str == "":
            self.where_str = "where \n" + string
        else:
            self.where_str = self.where_str + " \nand " + string

    def reset_where_str(self):
        self.where_str = ""
