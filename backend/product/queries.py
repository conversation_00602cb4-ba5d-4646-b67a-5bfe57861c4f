
GET_HIERARCHY_FILTERS_QUERY = """ 
    select  
        {select_str}
    from 
        {client_schema}.{table_name} {table_alias}
    {where_str}
    group by 
        {group_by_str}
    order by 
        {order_by_str}
"""

GET_PRODUCTS_DETAILS_QUERY = """
    with
        product_details as (
            SELECT
                pm.{client_product_id_key} as client_product_id,
                pm.product_id as product_id,
                pm.product_name,
                ROUND(pm.msrp::numeric, 2) AS msrp,
                ROUND(pm.cost::numeric, 2) AS cost,
                ROUND(pm.current_price::numeric, 2) AS current_price,
                ROUND(pm.launch_price::numeric, 2) AS launch_price
            FROM price_promo.product_master pm
            where
                {product_where_str}
                {event_where_str}
                and is_active = 1
            group by
                1,2,3,4,5,6,7
        )
    select
        pd.*,
        tll.oh as oh_inventory,
        tll.it as it_inventory,
        tll.oo as oo_inventory
    from product_details as pd
        left join global.tb_latest_inventory_agg as tll using (product_id) 
"""

GET_STORES_DETAILS_QUERY = """ 
    SELECT
        tsm.store_id AS store_id,
        tsm.store_name,
        {store_hierarchy_columns}
    FROM global.tb_store_master tsm
    where
        {store_where_str}
        {event_where_str}
        and is_active = 1
"""


GET_H5_DETAILS_QUERY = """
    with prod_details as (
        select 
            pm.product_h1_id,
            pm.product_h1_name,
            pm.product_h2_id,
            pm.product_h2_name,
            pm.product_h3_id,
            pm.product_h3_name,
            pm.product_h4_id,
            pm.product_h4_name,
            pm.product_h5_id,
            pm.product_h5_name,
            pm.brand,
            pm.brand_id,
            pm.price
        from 
            public.product_master pm 
        where
            pm.product_h5_id in {sku_id_list}
    ), 
    h1_cte as (
        select
            distinct product_h1_id as id, 
            product_h1_name as name,
            null::decimal as price,
            'product_h1' as hierarchy_level
        from 
            prod_details
    ), 
    h2_cte as (
        select
            distinct product_h2_id as id, 
            product_h2_name as name,
            null::decimal as price,
            'product_h2' as hierarchy_level
        from 
            prod_details
    ), 
    h3_cte as (
        select
            distinct product_h3_id as id, 
            product_h3_name as name,
            null::decimal as price,
            'product_h3' as hierarchy_level
        from 
            prod_details
    ), 
    h4_cte as (
        select
            distinct product_h4_id as id, 
            product_h4_name as name,
            null::decimal as price,
            'product_h4' as hierarchy_level
        from 
            prod_details
    ), 
    h5_cte as (
        select
            distinct product_h5_id as id, 
            product_h5_name as name,
            price::decimal,
            'product_h5' as hierarchy_level
        from 
            prod_details
    ),
    brand_cte as (
        select
            distinct brand as id, 
            brand as name,
            null::decimal,
            'brand' as hierarchy_level
        from 
            prod_details
    ),
    total_cte as (
        select 
            * 
        from
            h1_cte 
        union
            select * from h2_cte
        union
            select * from h3_cte
        union
            select * from h4_cte
        union
            select * from h5_cte
        union 
            select * from brand_cte
    )
    select
        hierarchy_level,
        case 
            when hierarchy_level = 'product_h5' then jsonb_agg(json_build_object('product_h5_id', id, 'product_h5_name', name, 'price', price, 'is_hero', 0))
            else jsonb_agg(json_build_object('label', name, 'value', id))
        end as hierarchy_details
    from 
        total_cte
    group by hierarchy_level
"""


GET_VALID_PRODUCT_IDS_EVENT_BASED_QUERY = """
    WITH user_input AS (
        SELECT 
            {select_sql_clause}
        FROM 
            (VALUES
                {from_sql_clause}

            ) AS t({select_sql_clause})
    ),
    {event_cte}
    user_entered_sku_details_cte AS (
        SELECT
            DISTINCT
            {user_entered_product_details_cte_select_condition},
            pm.product_id as product_id_value,
            ROUND(pm.msrp::numeric, 2) AS msrp,
            ROUND(pm.cost::numeric, 2) AS cost,
            ROUND(pm.current_price::numeric, 2) AS current_price,
            ROUND(pm.launch_price::numeric, 2) AS launch_price,
            CASE 
                WHEN pm.product_name IS NULL THEN 'invalid'
                {event_out_of_scope_condition}
                WHEN pm.is_active = 0 THEN 'inactive'
                ELSE 'valid'
            END AS validity,
            tll.oh AS oh_inventory,
            tll.it AS it_inventory,
            tll.oo AS oo_inventory
        FROM user_input u
        LEFT JOIN price_promo.product_master AS pm ON {product_join_condition}
        LEFT JOIN global.tb_latest_inventory_agg AS tll ON pm.product_id = tll.product_id
        {event_join_contition}
    )
    SELECT 
        validity,
        json_agg(
            json_build_object(
                {final_output_select},
                'status', validity,
                'product_id', product_id_value, 
                'l5_name', product_name,
                'msrp', msrp, 
                'cost', cost, 
                'current_price', current_price,
                'launch_price', launch_price,
                'oh_inventory', oh_inventory,
                'it_inventory', it_inventory,
                'oo_inventory', oo_inventory
            )
        ) AS product_data
    FROM 
        user_entered_sku_details_cte 
    GROUP BY validity;
"""


GET_VALID_STORE_IDS_QUERY = """
    WITH user_input AS (
    SELECT 
        {select_sql_clause}
    FROM 
        (VALUES
			{from_sql_clause}

		) AS t({select_sql_clause})
    ),
    user_entered_store_details_cte AS (
        SELECT
            {user_entered_store_details_cte_select_condition},
            CASE 
                WHEN tsm.store_name IS NULL THEN 'invalid'
                {event_condition}
                WHEN tsm.is_active = 0 THEN 'inactive'
                ELSE 'valid'
            END AS validity
        FROM user_input u
        LEFT JOIN global.tb_store_master tsm ON
            {store_join_condition}
            {event_join}
    )
    SELECT 
        validity,
        json_agg(
            json_build_object(
                'status', validity,
                'store_id', store_id,
                {final_output_select}
            )
        ) AS store_details
    FROM 
        user_entered_store_details_cte 
    GROUP BY validity;
"""


GET_OFFER_TYPE_DETAILS_QUERY = """
    select 
        id, 
        offer_type, 
        offer_display_name 
    from 
        {client_schema}.offer_type_master;
"""


GET_VALID_STORE_DETAILS_QUERY = """
    with user_entered_store_details_cte as (
        select 
            u.sid as store_h6_id,
            sm.store_h6_name,
            sm.store_h1_id,
            sm.store_h1_name,
            sm.store_h2_id,
            sm.store_h2_name,
            sm.store_h3_id,
            sm.store_h3_name,
            sm.store_h4_id,
            sm.store_h4_name,
            sm.store_h5_id,
            sm.store_h5_name,
            sm.is_active,
            case 
                when sm.store_h6_id is null {channel_type_condition} then 'invalid'
                else 'valid'
            end as validity
        from 
            (select unnest(array{user_input_store_list}) as sid) u
        left join              
            {client_schema}.store_master sm on u.sid=sm.store_h6_id::text
    )
    select 
        validity,
        jsonb_agg( 
            jsonb_build_object(
                'store_h1_id', store_h1_id, 'store_h1_name', store_h1_name,
                'store_h2_id', store_h2_id, 'store_h2_name', store_h2_name,
                'store_h3_id', store_h3_id, 'store_h3_name', store_h3_name,
                'store_h4_id', store_h4_id, 'store_h4_name', store_h4_name,
                'store_h5_id', store_h5_id, 'store_h5_name', store_h5_name, 
                'store_h6_id', store_h6_id, 'store_h6_name', store_h6_name, 
                'is_active', is_active
            )
        ) as store_details
    from 
        user_entered_store_details_cte
    group by 
        validity
        
"""

GET_CUSTOMER_TYPE_QUERY = """
    SELECT 
        id as value, 
        customer_type as label 
    FROM 
        {promo_schema}.tb_customer_type_config
"""

GET_OFFER_DISTRIBUTION_CHANNEL_QUERY = """
    SELECT 
        id as value,
        channel as label 
    FROM 
        {promo_schema}.tb_offer_distributor_channel_config
"""


FETCH_CONFIG_DETAILS_QUERY = """
    select * from {schema}.{table_name} pstc order by id
"""