from typing import Optional
from fastapi import Request, UploadFile, status
from fastapi.routing import APIRouter
from pricesmart_common.constants import PRODUCT_API_TAG
from product import models as product_models
from product import service as product_service
from pricesmart_common import utils as common_utils

product_router = APIRouter()


@product_router.post(
    path="/products",
    tags=[PRODUCT_API_TAG],
)
async def get_product_details(
    request_payload: product_models.ProductListRequest
):
    """
    Get product details based on request criteria.
    
    Args:
        request (Request): FastAPI request object
        request_payload (ProductListRequest): Product filter criteria
        
    Returns:
        dict: Response containing:
            - data (dict): Product details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    product_details = await product_service.get_product_details_service(
        request_payload
    )

    return common_utils.create_response(data=product_details)


@product_router.post(
    path="/stores",
    tags=[PRODUCT_API_TAG],
)
async def get_stores(
    request_payload: product_models.StoretListRequest,
):
    """
    Get store details based on request criteria.
    
    Args:
        request_payload (StoretListRequest): Store filter criteria
        
    Returns:
        dict: Response containing:
            - data (dict): Store details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    product_details = await product_service.get_store_details_service(
        request_payload
    )

    return common_utils.create_response(
        data=product_details
    )


@product_router.post(
    "/get-products-from-file",
    tags=[PRODUCT_API_TAG],
)
async def get_products_from_file(request: Request, file: UploadFile, event_id: Optional[int] = None):
    """
    Get product details from uploaded file.
    
    Args:
        request (Request): FastAPI request object
        file (UploadFile): Uploaded file containing product data
        event_id (Optional[int]): Event identifier
        
    Returns:
        dict: Response containing:
            - data (dict): Product details from file
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK

    # Execute the service operation and retrieve the result
    products_data = await product_service.get_products_from_file(file,event_id)

    return common_utils.create_response(_message, _status, user_id, products_data)


@product_router.post(
    "/get-stores-from-file",
    tags=[PRODUCT_API_TAG],
)
async def get_stores_from_file(file: UploadFile,event_id: Optional[int]=None):
    """
    Get store details from uploaded file.
    
    Args:
        file (UploadFile): Uploaded file containing store data
        event_id (Optional[int]): Event identifier
        
    Returns:
        dict: Response containing:
            - data (dict): Store details from file
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    products_data = await product_service.get_stores_from_file(file,event_id)
    return common_utils.create_response(data=products_data)


@product_router.post(path="/sku-details", tags=[PRODUCT_API_TAG])
async def get_sku_details(request: Request, request_payload: product_models.ProductDetails):
    """
    Get SKU details based on request criteria.
    
    Args:
        request (Request): FastAPI request object
        request_payload (ProductDetails): SKU filter criteria
        
    Returns:
        dict: Response containing:
            - data (dict): SKU details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK


    # Execute the service operation and retrieve the result
    data = await product_service.validate_and_get_sku_details_service(
        request_payload
    )

    return common_utils.create_response(_message, _status, user_id, data)


@product_router.get(path="/offer-types", tags=[PRODUCT_API_TAG])
async def get_offer_types(request: Request):
    """
    Get available offer types.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        dict: Response containing:
            - data (dict): Offer type details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK

    # Execute the service operation and retrieve the result
    data = await product_service.get_offer_type_details_service()

    return common_utils.create_response(_message, _status, user_id, data)


@product_router.post(path="/store-details", tags=[PRODUCT_API_TAG])
async def get_store_details(
    request_payload: product_models.StoreDetails
):
    """
    Get detailed store information.
    
    Args:
        request_payload (StoreDetails): Store filter criteria
        
    Returns:
        dict: Response containing:
            - data (dict): Store details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    product_details = await product_service.validate_and_get_store_details_service(
        request_payload
    )

    return common_utils.create_response(data=product_details)


@product_router.get(
    path="/customer-type",
    response_model=product_models.HierarchyFilterDataResponse,
    tags=[PRODUCT_API_TAG],
)
async def get_customer_type(request: Request):
    """
    Get available customer types.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Customer type details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK

    # Execute the service operation and retrieve the result
    result = await product_service.get_customer_type()

    return common_utils.create_response(_message, _status, user_id, result)


@product_router.get(
    path="/distribution-channel-type",
    response_model=product_models.HierarchyFilterDataResponse,
    tags=[PRODUCT_API_TAG],
)
async def get_distribution_channel_type(request: Request):
    """
    Get available distribution channel types.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Distribution channel type details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK

    # Execute the service operation and retrieve the result
    result = await product_service.get_distribution_channel_type()

    return common_utils.create_response(_message, _status, user_id, result)


@product_router.get(
    path="/config-details",
    tags=[PRODUCT_API_TAG],
)
async def get_config_details(
    request: Request,
    config: Optional[str] = None,
):
    """
    Get configuration details.
    
    Args:
        request (Request): FastAPI request object
        config (Optional[str]): Configuration type filter
        
    Returns:
        dict: Response containing:
            - data (dict): Configuration details
            - message (str): Success/error message
            - status (int): HTTP status code
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK

    # Execute the service operation and retrieve the result
    result = await product_service.get_config_details_service(config)

    return common_utils.create_response(_message, _status, user_id, result)