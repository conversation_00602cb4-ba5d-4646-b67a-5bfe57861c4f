# Validation messages
SKU_UPLOAD_SUCCESS_MSG = "SKU details fetched successfully"
SKU_UPLOAD_PARTIAL_SUCCESS_MSG = (
    "SKU details fetched. Could not fetch details for SKU IDs {invalid_sku_ids}, "
    "as these are invalid"
)

STORE_UPLOAD_SUCCESS_MSG = "Store details fetched successfully"

# Product Attributes
CHANNEL_ECOMMERCE = "Ecom"
CHANNEL_BNM = "B&M"
CHANNEL_VALUES = [CHANNEL_BNM, CHANNEL_ECOMMERCE]

STORE_TABLE = "store_master"
STORE_TABLE_ALIAS = "sm"
PRODUCT_TABLE = "product_master"
PRODUCT_TABLE_ALIAS = "pm"

STORE_HIERARCHY = "store_hierarchy"
PRODUCT_HIERARCHY = "product_hierarchy"
HIERARCHY_TYPES = [PRODUCT_HIERARCHY, STORE_HIERARCHY]

BRAND = "brand"
PROD_HIERARCHY1 = "product_h1"
PROD_HIERARCHY2 = "product_h2"
PROD_HIERARCHY3 = "product_h3"
PROD_HIERARCHY4 = "product_h4"
PROD_HIERARCHY5 = "product_h5"

STORE_HIERARCHY0 = "store_h0"
STORE_HIERARCHY1 = "store_h1"
STORE_HIERARCHY2 = "store_h2"
STORE_HIERARCHY3 = "store_h3"
STORE_HIERARCHY4 = "store_h4"
STORE_HIERARCHY5 = "store_h5"
STORE_HIERARCHY6 = "store_h6"

NUM_BRAND = 6
NUM_PROD_HIERARCHY1 = 1
NUM_PROD_HIERARCHY2 = 2
NUM_PROD_HIERARCHY3 = 3
NUM_PROD_HIERARCHY4 = 4
NUM_PROD_HIERARCHY5 = 5
NUM_STORE_HIERARCHY0 = 10
NUM_STORE_HIERARCHY1 = 11
NUM_STORE_HIERARCHY2 = 12
NUM_STORE_HIERARCHY3 = 13
NUM_STORE_HIERARCHY4 = 14
NUM_STORE_HIERARCHY5 = 15
NUM_STORE_HIERARCHY6 = 16
STR_NUM_BRAND = "6"
STR_NUM_PROD_HIERARCHY1 = "1"
STR_NUM_PROD_HIERARCHY2 = "2"
STR_NUM_PROD_HIERARCHY3 = "3"
STR_NUM_PROD_HIERARCHY4 = "4"
STR_NUM_PROD_HIERARCHY5 = "5"
STR_NUM_STORE_HIERARCHY0 = "10"
STR_NUM_STORE_HIERARCHY1 = "11"
STR_NUM_STORE_HIERARCHY2 = "12"
STR_NUM_STORE_HIERARCHY3 = "13"
STR_NUM_STORE_HIERARCHY4 = "14"
STR_NUM_STORE_HIERARCHY5 = "15"
STR_NUM_STORE_HIERARCHY6 = "16"


NON_BASE_HIERARCHIES = [
    BRAND,
    PROD_HIERARCHY1,
    PROD_HIERARCHY2,
    PROD_HIERARCHY3,
    PROD_HIERARCHY4,
    STORE_HIERARCHY0,
    STORE_HIERARCHY1,
    STORE_HIERARCHY2,
    STORE_HIERARCHY3,
    STORE_HIERARCHY4,
    STORE_HIERARCHY5,
    NUM_BRAND,
    NUM_PROD_HIERARCHY1,
    NUM_PROD_HIERARCHY2,
    NUM_PROD_HIERARCHY3,
    NUM_PROD_HIERARCHY4,
    NUM_STORE_HIERARCHY0,
    NUM_STORE_HIERARCHY1,
    NUM_STORE_HIERARCHY2,
    NUM_STORE_HIERARCHY3,
    NUM_STORE_HIERARCHY4,
    NUM_STORE_HIERARCHY5,
    STR_NUM_BRAND,
    STR_NUM_PROD_HIERARCHY1,
    STR_NUM_PROD_HIERARCHY2,
    STR_NUM_PROD_HIERARCHY3,
    STR_NUM_PROD_HIERARCHY4,
    STR_NUM_STORE_HIERARCHY0,
    STR_NUM_STORE_HIERARCHY1,
    STR_NUM_STORE_HIERARCHY2,
    STR_NUM_STORE_HIERARCHY3,
    STR_NUM_STORE_HIERARCHY4,
    STR_NUM_STORE_HIERARCHY5,
]

BASE_HIERARCHIES = [PROD_HIERARCHY5, STORE_HIERARCHY6]

PRODUCT_HIERARCHIES = [
    BRAND,
    PROD_HIERARCHY1,
    PROD_HIERARCHY2,
    PROD_HIERARCHY3,
    PROD_HIERARCHY4,
    PROD_HIERARCHY5,
    NUM_BRAND,
    NUM_PROD_HIERARCHY1,
    NUM_PROD_HIERARCHY2,
    NUM_PROD_HIERARCHY3,
    NUM_PROD_HIERARCHY4,
    NUM_PROD_HIERARCHY5,
    STR_NUM_BRAND,
    STR_NUM_PROD_HIERARCHY1,
    STR_NUM_PROD_HIERARCHY2,
    STR_NUM_PROD_HIERARCHY3,
    STR_NUM_PROD_HIERARCHY4,
    STR_NUM_PROD_HIERARCHY5,
]

STORE_HIERARCHIES = [
    STORE_HIERARCHY0,
    STORE_HIERARCHY1,
    STORE_HIERARCHY2,
    STORE_HIERARCHY3,
    STORE_HIERARCHY4,
    STORE_HIERARCHY5,
    STORE_HIERARCHY6,
]

PH_ID_MAPPING = {
    BRAND: "brand_id",
    NUM_BRAND: "brand_id",
    PROD_HIERARCHY1: "product_h1_id",
    PROD_HIERARCHY2: "product_h2_id",
    PROD_HIERARCHY3: "product_h3_id",
    PROD_HIERARCHY4: "product_h4_id",
    PROD_HIERARCHY5: "product_h5_id",
    NUM_PROD_HIERARCHY1: "product_h1_id",
    NUM_PROD_HIERARCHY2: "product_h2_id",
    NUM_PROD_HIERARCHY3: "product_h3_id",
    NUM_PROD_HIERARCHY4: "product_h4_id",
    NUM_PROD_HIERARCHY5: "product_h5_id",
    STR_NUM_PROD_HIERARCHY1: "product_h1_id",
    STR_NUM_PROD_HIERARCHY2: "product_h2_id",
    STR_NUM_PROD_HIERARCHY3: "product_h3_id",
    STR_NUM_PROD_HIERARCHY4: "product_h4_id",
    STR_NUM_PROD_HIERARCHY5: "product_h5_id",
}

PH_NAME_MAPPING = {
    BRAND: "brand",
    PROD_HIERARCHY1: "product_h1_name",
    PROD_HIERARCHY2: "product_h2_name",
    PROD_HIERARCHY3: "product_h3_name",
    PROD_HIERARCHY4: "product_h4_name",
    PROD_HIERARCHY5: "product_h5_name",
    NUM_BRAND: "brand",
    NUM_PROD_HIERARCHY1: "product_h1_name",
    NUM_PROD_HIERARCHY2: "product_h2_name",
    NUM_PROD_HIERARCHY3: "product_h3_name",
    NUM_PROD_HIERARCHY4: "product_h4_name",
    NUM_PROD_HIERARCHY5: "product_h5_name",
    STR_NUM_BRAND: "brand",
    STR_NUM_PROD_HIERARCHY1: "product_h1_name",
    STR_NUM_PROD_HIERARCHY2: "product_h2_name",
    STR_NUM_PROD_HIERARCHY3: "product_h3_name",
    STR_NUM_PROD_HIERARCHY4: "product_h4_name",
    STR_NUM_PROD_HIERARCHY5: "product_h5_name",
}

SH_ID_MAPPING = {
    STORE_HIERARCHY0: "store_h0_id",
    STORE_HIERARCHY1: "store_h1_id",
    STORE_HIERARCHY2: "store_h2_id",
    STORE_HIERARCHY3: "store_h3_id",
    STORE_HIERARCHY4: "store_h4_id",
    STORE_HIERARCHY5: "store_h5_id",
    STORE_HIERARCHY6: "store_h6_id",
    NUM_STORE_HIERARCHY0: "store_h0_id",
    NUM_STORE_HIERARCHY1: "store_h1_id",
    NUM_STORE_HIERARCHY2: "store_h2_id",
    NUM_STORE_HIERARCHY3: "store_h3_id",
    NUM_STORE_HIERARCHY4: "store_h4_id",
    NUM_STORE_HIERARCHY5: "store_h5_id",
    NUM_STORE_HIERARCHY6: "store_h6_id",
    STR_NUM_STORE_HIERARCHY0: "store_h0_id",
    STR_NUM_STORE_HIERARCHY1: "store_h1_id",
    STR_NUM_STORE_HIERARCHY2: "store_h2_id",
    STR_NUM_STORE_HIERARCHY3: "store_h3_id",
    STR_NUM_STORE_HIERARCHY4: "store_h4_id",
    STR_NUM_STORE_HIERARCHY5: "store_h5_id",
    STR_NUM_STORE_HIERARCHY6: "store_h6_id",
}

SH_NAME_MAPPING = {
    STORE_HIERARCHY0: "store_h0_name",
    STORE_HIERARCHY1: "store_h1_name",
    STORE_HIERARCHY2: "store_h2_name",
    STORE_HIERARCHY3: "store_h3_name",
    STORE_HIERARCHY4: "store_h4_name",
    STORE_HIERARCHY5: "store_h5_name",
    STORE_HIERARCHY6: "store_h6_name",
    NUM_STORE_HIERARCHY0: "store_h0_name",
    NUM_STORE_HIERARCHY1: "store_h1_name",
    NUM_STORE_HIERARCHY2: "store_h2_name",
    NUM_STORE_HIERARCHY3: "store_h3_name",
    NUM_STORE_HIERARCHY4: "store_h4_name",
    NUM_STORE_HIERARCHY5: "store_h5_name",
    NUM_STORE_HIERARCHY6: "store_h6_name",
    STR_NUM_STORE_HIERARCHY0: "store_h0_name",
    STR_NUM_STORE_HIERARCHY1: "store_h1_name",
    STR_NUM_STORE_HIERARCHY2: "store_h2_name",
    STR_NUM_STORE_HIERARCHY3: "store_h3_name",
    STR_NUM_STORE_HIERARCHY4: "store_h4_name",
    STR_NUM_STORE_HIERARCHY5: "store_h5_name",
    STR_NUM_STORE_HIERARCHY6: "store_h6_name",
}

HIERARCHY_NAME_MAPPING = {**PH_NAME_MAPPING, **SH_NAME_MAPPING}
HIERARCHY_ID_MAPPING = {**PH_ID_MAPPING, **SH_ID_MAPPING}

SKU_ID_COLUMN = "SVS ID"
STORE_ID_COLUMN = "Store ID"


PRODUCT_GROUP_API_TAG = "Product Group"