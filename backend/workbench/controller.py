from fastapi import APIRouter
from workbench import models as workbench_models
from workbench import service as workbench_service
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as pricesmart_common_constants

router = APIRouter(tags=["Workbench"])

@router.post("/workbench/promos")
async def get_promos(
    get_promos_request: workbench_models.GetWorkbenchPromosRequest,
):
    """
    Get promotions for the workbench view.
    
    Args:
        get_promos_request (GetWorkbenchPromosRequest): Request model containing filters 
            and parameters for fetching workbench promotions
            
    Returns:
        dict: Response containing:
            - data: List of promotions matching the request criteria
            - message: Success/error message
            - status: HTTP status code
    """
    print('Use events in controller:  ', pricesmart_common_constants.USE_EVENTS)
    data = await workbench_service.get_workbench_promos(
        get_promos_request,
    )

    return common_utils.create_response(
        data = data
    )