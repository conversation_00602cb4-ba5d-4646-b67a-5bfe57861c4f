from pricesmart_common import models as common_models
from pricesmart_common import helpers as common_helpers
from pydantic import model_validator, PydanticUserError
from typing import Optional

class GetWorkbenchPromosRequest(common_models.OptionalBaseFilters):
    promo_ids: list[int] = []
    event_ids: list[int] = []
    target_currency_id: Optional[int] = None

    @model_validator(mode="before")
    def validate_params(cls, values):
        return common_helpers.validate_non_id_fields(values)

