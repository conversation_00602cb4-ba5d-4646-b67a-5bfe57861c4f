

FETCH_PROMOS_WORKBENCH = """
    SELECT
        promo_id,
        promo_name AS promo_name,
        start_date,
        end_date,
        event_id,
        event_name,
        is_locked,
        created_by,
        offer_comment,
        status_id,
        status,
        step_count,
        products_count,
        stores_count,
        product_selection_type_id,
        product_selection_type,
        store_selection_type_id,
        store_selection_type,
        exclusion_selection_type_id,
        exclusion_selection_type,
        customer_type_id,
        customer_type,
        offer_distribution_channel_id,
        offer_distribution_channel,
        last_approved_scenario_id,
        recommendation_type_id,
        recommendation_type,
        is_under_processing,
        is_auto_resimulated,
        is_overridden,
        override_comment,
        override_reason,
        currency_id,
        currency_name,
        currency_symbol,
        --
        discount_level_id,
        discount_level,
        discount_type,
        --
        target_sales_units,
        target_revenue,
        target_margin,
        --
        performance,

        jsonb_build_object(
            'margin',finalized_margin,
            'revenue',finalized_revenue,
            'discount',finalized_discount,
            'promo_spend',finalized_promo_spend,
            'sales_units',finalized_sales_units,
            'margin_percent',finalized_margin_percent,
            'contribution_margin',finalized_contribution_margin,
            'contribution_revenue',finalized_contribution_revenue,
            'contribution_margin_percent',finalized_contribution_margin_percent,
            'total_inventory', finalized_total_inventory,
            'finalized_st_percent', finalized_st_percent
        ) as finalized,

        jsonb_build_object(
            'discount',ia_recc_discount
        ) as ia_recommend,

        jsonb_build_object(
            'margin', finalized_baseline_margin,
            'revenue', finalized_baseline_revenue,
            'sales_units', finalized_baseline_sales_units
        ) as baseline,  
    
        jsonb_build_object(
            'margin',original_margin,
            'revenue',original_revenue,
            'discount',original_discount,
            'promo_spend',original_promo_spend,
            'sales_units',original_sales_units,
            'margin_percent',original_margin_percent,
            'contribution_margin',original_contribution_margin,
            'contribution_revenue',original_contribution_revenue,
            'contribution_margin_percent',original_contribution_margin_percent
        ) as original,

        jsonb_build_object(
            'margin', finalized_stack_baseline_margin,
            'revenue', finalized_stack_baseline_revenue,
            'sales_units', finalized_stack_baseline_sales_units
        ) as stack_baseline,

        

        jsonb_build_object(
            'margin',finalized_stack_margin,
            'revenue',finalized_stack_revenue,
            'promo_spend',finalized_stack_promo_spend,
            'sales_units',finalized_stack_sales_units,
            'margin_percent',finalized_stack_margin_percent,
            'contribution_margin',finalized_stack_contribution_margin,
            'contribution_revenue',finalized_stack_contribution_revenue,
            'contribution_margin_percent',finalized_stack_contribution_margin_percent,
            'total_inventory', finalized_total_inventory,
            'finalized_st_percent', finalized_stack_st_percent
        ) as finalized_stack,

        
        jsonb_build_object(
            'margin',original_stack_margin,
            'revenue',original_stack_revenue,
            'promo_spend',original_stack_promo_spend,
            'sales_units',original_stack_sales_units,
            'margin_percent',original_stack_margin_percent,
            'contribution_margin',original_stack_contribution_margin,
            'contribution_revenue',original_stack_contribution_revenue,
            'contribution_margin_percent',original_stack_contribution_margin_percent
        ) as original_stack

    FROM 
        price_promo.fn_fetch_workbench_table_data({request_payload}::jsonb)
"""

FETCH_PROMOS_WORKBENCH_BY_IDS = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.event_id, 
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated,
            pm.is_overridden_scenario_finalized
        FROM
            {promo_schema}.promo_master pm
        {where_str}
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.promo_name,
            pmfc.start_date,
            pmfc.end_date,
            em.name as event_name,
            em.event_id,
            em.is_locked,
            pmfc.created_by,
            pmfc.status_id,
            STRING_AGG(psc.status_name::text, ', ') AS status,
            pmfc.step_count,
            pmfc.offer_comment,
            pmfc.products_count,
            pmfc.stores_count,
            pmfc.product_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN pstc.product_selection_sub_type::text IS NOT NULL THEN CONCAT(pstc.product_selection_type::text, '-', pstc.product_selection_sub_type::text)
                    ELSE pstc.product_selection_type::text
                END,
                ', '
            ) AS product_selection_type,
            pmfc.store_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN sstc.store_selection_sub_type::text IS NOT NULL THEN CONCAT(sstc.store_selection_type::text, '-', sstc.store_selection_sub_type::text)
                    ELSE sstc.store_selection_type::text
                END,
                ', '
            ) AS store_selection_type,
            pmfc.exclusion_selection_type_id, 
            case 
            	when exclusion_selection_type_id = 1 then 'hierarchy based exclusion '
            	when exclusion_selection_type_id = 2 then 'product based exclusion '
            	when exclusion_selection_type_id = 3 then 'product group based exclusion '
            	when exclusion_selection_type_id = 4 then 'file upload based exclusion '
            end as exclusion_selection_type,
            pmfc.customer_type_id,
            STRING_AGG(tctc.customer_type::text, ', ') AS customer_type,
            pmfc.offer_distribution_channel_id,
            STRING_AGG(todcc.channel::text, ', ') AS offer_distribution_channel,
            pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id,
            STRING_AGG(tasm.name, ', ') AS recommendation_type,
            pmfc.is_under_processing,
            pmfc.is_auto_resimulated,
            pmfc.is_overridden_scenario_finalized
        FROM
            promo_master_filtered_cte pmfc
        left join 
            {promo_schema}.event_master em on pmfc.event_id = em.event_id
        LEFT JOIN
            {promo_schema}.promo_status_config psc ON pmfc.status_id = psc.status_id
        LEFT JOIN
            {promo_schema}.product_selection_type_config pstc ON pmfc.product_selection_type_id = pstc.id
        LEFT JOIN
            {promo_schema}.store_selection_type_config sstc ON pmfc.store_selection_type_id = sstc.id
        LEFT JOIN
            {promo_schema}.tb_customer_type_config tctc ON pmfc.customer_type_id = tctc.id
        LEFT JOIN
            {promo_schema}.tb_offer_distributor_channel_config todcc ON pmfc.offer_distribution_channel_id = todcc.id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pmfc.recommendation_type_id = tasm.id
        GROUP BY
            pmfc.promo_id, pmfc.promo_name, pmfc.start_date, pmfc.end_date, em.name, em.event_id, em.is_locked, pmfc.created_by, pmfc.status_id, pmfc.step_count,
            pmfc.offer_comment, pmfc.products_count, pmfc.stores_count, pmfc.product_selection_type_id, pmfc.store_selection_type_id, pmfc.exclusion_selection_type_id,
            pmfc.customer_type_id, pmfc.offer_distribution_channel_id, pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id, pmfc.is_under_processing, pmfc.is_auto_resimulated, pmfc.is_overridden_scenario_finalized
    ),
    promo_rules_cte AS (
        SELECT
            pr.promo_id,
            pr.discount_level AS discount_level_id,
            dlc.discount_level_value AS discount_level,
            pr.discount_type,
            tasm.display_name as display_discount_type,
            pr.units_target as sales_units_target,
            pr.revenue_target,
            pr.gross_margin_target as margin_target
        FROM
            {promo_schema}.ps_rules pr
        LEFT JOIN
            {promo_schema}.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
        LEFT JOIN
            metaschema.tb_app_sub_master tasm ON pr.discount_type = tasm.name
        WHERE
            pr.promo_id IN (SELECT DISTINCT promo_id FROM promo_master_filtered_cte)
    ),
    original_cte AS (
        SELECT
            fe.promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_contribution_margin_percent,
            MIN(offer_type_combined_display_name) AS original_discount,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_performance
        FROM
            promo_master_filtered_cte fe 
        INNER JOIN
            price_promo.ps_recommended_finalized_agg pa using(promo_id)
        GROUP BY
            fe.promo_id
    ),
    stacked_original_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_stack_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_stack_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_stack_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_stack_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_stack_contribution_margin_percent,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_stack_performance
        FROM
            price_promo.ps_recommended_finalized_stack_agg pa
        INNER JOIN
            promo_master_filtered_cte fe ON fe.promo_id = any(pa.promo_ids)
        GROUP BY
            promo_id
    ),
    finalized_scenarios_cte AS (
        SELECT 
            fep.promo_id,
            pof.is_default
        FROM 
            promo_master_filtered_cte fep
        LEFT JOIN 
            price_promo.promo_master pm ON fep.promo_id = pm.promo_id
        LEFT JOIN 
            price_promo.tb_promo_override_forecast pof ON fep.promo_id = pof.promo_id AND coalesce(pm.last_approved_scenario_id,0) = pof.scenario_id
    ),
    finalized_cte AS (
        SELECT
            sfsc.promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))
                        ELSE (SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0)) END)::NUMERIC, 2) AS finalized_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_contribution_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))
                        ELSE (SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0)) END)::NUMERIC, 2) AS finalized_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN MIN(override.offer_type_combined_display_name)
                ELSE MIN(original.offer_type_combined_display_name)
            END AS finalized_discount,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))
                        ELSE (SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0)) END)::DECIMAL * 100::DECIMAL, 2) AS finalized_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_agg original ON sfsc.promo_id = original.promo_id
        LEFT JOIN 
            price_promo.ps_recommended_finalized_override_agg override ON sfsc.promo_id = override.promo_id and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    stacked_finalized_cte AS (
        SELECT
            promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_stack_baseline_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_stack_contribution_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
                ELSE ROUND((SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
            END AS finalized_stack_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_agg original ON sfsc.promo_id = ANY(original.promo_ids)
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_override_agg override ON sfsc.promo_id = ANY(override.promo_ids) and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    ia_recc_cte AS (
        SELECT
            promo_id,
            MIN(offer_type_combined_display_name) AS ia_recc_discount
        FROM
            price_promo.ps_recommended_ia_projected_agg pripa
        WHERE
            promo_id IN (SELECT DISTINCT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            promo_id
    ),
    actualized_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS actualized_sales_units,
            ROUND(SUM(baseline_sales_units)::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(SUM(incremental_sales_units)::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS actualized_revenue,
            ROUND(SUM(baseline_revenue)::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(SUM(incremental_revenue)::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS actualized_margin,
            ROUND(SUM(baseline_margin)::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(SUM(incremental_margin)::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN SUM(baseline_margin) IS NULL OR SUM(baseline_margin) = 0 THEN NULL
                ELSE ROUND((SUM(incremental_margin) / ABS(SUM(baseline_margin)))::DECIMAL * 100::DECIMAL, 2)
            END AS performance
        FROM
            {promo_schema}.ps_recommended_actuals_agg praa
        WHERE
            promo_id IN (SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            promo_id
    )
    SELECT
        pmc.promo_id,
        pmc.promo_name AS promo_name,
        pmc.start_date,
        pmc.end_date,
        pmc.event_id, 
        pmc.event_name,
        pmc.is_locked,
        um.name AS created_by,
        pmc.offer_comment,
        pmc.status_id,
        pmc.status,
        pmc.step_count,
        pmc.products_count,
        pmc.stores_count,
        pmc.product_selection_type_id,
        pmc.product_selection_type,
        pmc.store_selection_type_id,
        pmc.store_selection_type,
        pmc.exclusion_selection_type_id,
        pmc.exclusion_selection_type,
        pmc.customer_type_id,
        pmc.customer_type,
        pmc.offer_distribution_channel_id,
        pmc.offer_distribution_channel,
        pmc.last_approved_scenario_id,
        pmc.recommendation_type_id,
        pmc.recommendation_type,
        pmc.is_under_processing,
        pmc.is_auto_resimulated,
        --
        prc.discount_level_id,
        prc.discount_level,
        display_discount_type AS discount_type,
        --
        prc.sales_units_target AS target_sales_units,
        prc.revenue_target AS target_revenue,
        prc.margin_target AS target_margin,
        
        price_promo.fn_get_performance_repr(COALESCE(acc.performance, fc.finalized_performance)) AS performance,
        iarc.ia_recc_discount AS ia_recc_discount,
        --
        jsonb_build_object(
            'margin',fc.finalized_margin,
            'revenue',fc.finalized_revenue,
            'discount',fc.finalized_discount,
            'promo_spend',fc.finalized_promo_spend,
            'sales_units',fc.finalized_sales_units,
            'margin_percent',fc.finalized_margin_percent,
            'contribution_margin',fc.finalized_contribution_margin,
            'contribution_revenue',fc.finalized_contribution_revenue,
            'contribution_margin_percent',fc.finalized_contribution_margin_percent
        ) as finalized,

        jsonb_build_object(
            'discount',iarc.ia_recc_discount
        ) as ia_recommend,

        jsonb_build_object(
            'margin', fc.finalized_baseline_margin,
            'revenue', fc.finalized_baseline_revenue,
            'sales_units', fc.finalized_baseline_sales_units
        ) as baseline,  
    
        jsonb_build_object(
            'margin',oc.original_margin,
            'revenue',oc.original_revenue,
            'discount',oc.original_discount,
            'promo_spend',oc.original_promo_spend,
            'sales_units',oc.original_sales_units,
            'margin_percent',oc.original_margin_percent,
            'contribution_margin',oc.original_contribution_margin,
            'contribution_revenue',oc.original_contribution_revenue,
            'contribution_margin_percent',oc.original_contribution_margin_percent
        ) as original,

        jsonb_build_object(
            'margin', sfc.finalized_stack_baseline_margin,
            'revenue', sfc.finalized_stack_baseline_revenue,
            'sales_units', sfc.finalized_stack_baseline_sales_units
        ) as stack_baseline,

        

        jsonb_build_object(
            'margin',sfc.finalized_stack_margin,
            'revenue',sfc.finalized_stack_revenue,
            'promo_spend',sfc.finalized_stack_promo_spend,
            'sales_units',sfc.finalized_stack_sales_units,
            'margin_percent',sfc.finalized_stack_margin_percent,
            'contribution_margin',sfc.finalized_stack_contribution_margin,
            'contribution_revenue',sfc.finalized_stack_contribution_revenue,
            'contribution_margin_percent',sfc.finalized_stack_contribution_margin_percent
        ) as finalized_stack,

        

        jsonb_build_object(
            'margin',soc.original_stack_margin,
            'revenue',soc.original_stack_revenue,
            'promo_spend',soc.original_stack_promo_spend,
            'sales_units',soc.original_stack_sales_units,
            'margin_percent',soc.original_stack_margin_percent,
            'contribution_margin',soc.original_stack_contribution_margin,
            'contribution_revenue',soc.original_stack_contribution_revenue,
            'contribution_margin_percent',soc.original_stack_contribution_margin_percent
        ) as original_stack
    FROM 
        promo_master_details_cte pmc
    LEFT JOIN 
        promo_rules_cte prc ON pmc.promo_id = prc.promo_id
    LEFT JOIN 
        finalized_cte fc ON pmc.promo_id = fc.promo_id
    LEFT JOIN 
        stacked_finalized_cte sfc ON pmc.promo_id = sfc.promo_id
    LEFT JOIN 
        ia_recc_cte iarc ON pmc.promo_id = iarc.promo_id
    LEFT JOIN
        original_cte oc ON pmc.promo_id = oc.promo_id
    LEFT JOIN
        stacked_original_cte soc ON pmc.promo_id = soc.promo_id
    LEFT JOIN 
        actualized_cte acc ON pmc.promo_id = acc.promo_id
    LEFT JOIN 
        global.user_master um ON pmc.created_by = um.user_code
    order by promo_id;
"""
