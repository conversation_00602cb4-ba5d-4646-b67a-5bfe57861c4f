from workbench import models as workbench_models
from pricesmart_common.utils import async_execute_query,get_str_repr,get_key_value_str
from workbench import queries as workbench_queries
from configuration.environment import environment
from promotions import utils as promo_utils
from pricesmart_common import constants as common_constants
from promotions import constants as promo_constants

async def get_workbench_promos_data(
    request_payload: workbench_models.GetWorkbenchPromosRequest
):
    
    request_payload=request_payload.model_dump()
    
    query = workbench_queries.FETCH_PROMOS_WORKBENCH.format(
        request_payload=get_str_repr(request_payload)
    )
    print(query)
    
    data = await async_execute_query(query)
    return data
