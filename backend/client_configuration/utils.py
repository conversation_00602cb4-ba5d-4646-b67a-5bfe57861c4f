from client_configuration import constants as client_configuration_constants
from enums.Enums import IdentifierEnum

def update_client_configuration_constants(identifiers):
    
    update_promo_identifiers(identifiers["promo"])
    update_event_identifiers(identifiers["event"])
    
    return


def update_promo_identifiers(promo_identifiers):
    client_configuration_constants.PROMO_IDENTIFIER_PRIMARY = promo_identifiers.get(
        IdentifierEnum.PRIMARY.value, client_configuration_constants.PROMO_IDENTIFIER_PRIMARY
    )
    client_configuration_constants.PROMO_IDENTIFIER_EXPANDED = promo_identifiers.get(
        IdentifierEnum.EXPANDED.value, client_configuration_constants.PROMO_IDENTIFIER_EXPANDED
    )
    client_configuration_constants.PROMO_IDENTIFIER_STANDARD = promo_identifiers.get(
        IdentifierEnum.STANDARD.value, client_configuration_constants.PROMO_IDENTIFIER_STANDARD
    )
    client_configuration_constants.PROMO_IDENTIFIER_PLURAL = promo_identifiers.get(
        IdentifierEnum.PLURAL.value, client_configuration_constants.PROMO_IDENTIFIER_PLURAL
    )
    client_configuration_constants.PROMO_IDENTIFIER_STANDARD_PLURAL = promo_identifiers.get(
        IdentifierEnum.STANDARD_PLURAL.value, client_configuration_constants.PROMO_IDENTIFIER_STANDARD_PLURAL
    )


def update_event_identifiers(event_identifiers):
    
    client_configuration_constants.EVENT_IDENTIFIER_PRIMARY = event_identifiers.get(
        "primary", client_configuration_constants.EVENT_IDENTIFIER_PRIMARY
    )
    client_configuration_constants.EVENT_IDENTIFIER_EXPANDED = event_identifiers.get(
        IdentifierEnum.EXPANDED.value, client_configuration_constants.EVENT_IDENTIFIER_EXPANDED
    )
    client_configuration_constants.EVENT_IDENTIFIER_STANDARD = event_identifiers.get(
        IdentifierEnum.STANDARD.value, client_configuration_constants.EVENT_IDENTIFIER_STANDARD
    )
    client_configuration_constants.EVENT_IDENTIFIER_PLURAL = event_identifiers.get(
        IdentifierEnum.PLURAL.value, client_configuration_constants.EVENT_IDENTIFIER_PLURAL
    )
    client_configuration_constants.EVENT_IDENTIFIER_STANDARD_PLURAL = event_identifiers.get(
        IdentifierEnum.STANDARD_PLURAL.value, client_configuration_constants.EVENT_IDENTIFIER_STANDARD_PLURAL
    )