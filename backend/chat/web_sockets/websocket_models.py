from typing import Dict
from pydantic import BaseModel
from enum import Enum


class WebSocketEventType(str, Enum):
    """Types of websocket events"""
    TOPIC_UPDATE = "topic_update"
    MEMBER_UPDATE = "member_update"
    CONNECTION_STATUS = "connection_status"
    ERROR = "error"


class WebSocketAction(str, Enum):
    """Types of websocket actions"""
    TOPIC_CREATED = "topic_created"
    TOPIC_UPDATED = "topic_updated"
    TOPIC_DELETED = "topic_deleted"
    MEMBERS_ADDED = "members_added"
    MEMBERS_REMOVED = "members_removed"
    MESSAGE_CREATED = "message_created"
    MESSAGE_UPDATED = "message_updated"
    MESSAGE_DELETED = "message_deleted"


class WebSocketStats(BaseModel):
    """WebSocket connection statistics"""
    total_active: int
    by_object_type: Dict[str, int]
    timestamp: str
