from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Path
from typing import Dict, Any
from chat.web_sockets.websocket_manager import websocket_manager, topic_websocket_manager
from chat.web_sockets.websocket_models import WebSocketStats
from chat import constants as chat_constants
from logger.logger import logger
from datetime import datetime
import json



router = APIRouter(tags=[chat_constants.CHAT_API_TAG])





@router.websocket("/ws/{obj_type}/{user_id}")
async def websocket_endpoint(websocket: WebSocket, obj_type: str, user_id: int):
    """
    WebSocket endpoint for topic updates.
    
    URL pattern: /ws/{obj_type}/{user_id}
    - obj_type: Object type (any text)
    - user_id: User ID subscribing to updates
    """
    
    
    logger.info(f"WebSocket connection attempt: obj_type={obj_type}, user_id={user_id}")
    
    # Subscribe user to websocket notifications using unified interface
    success = await websocket_manager.subscribe(obj_type, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to {obj_type}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Successfully connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_client_message(websocket, obj_type, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: obj_type={obj_type}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        try:
            await websocket_manager.disconnect(websocket)
            logger.info(f"WebSocket cleanup completed: obj_type={obj_type}, user_id={user_id}")
        except Exception as cleanup_error:
            logger.error(f"Error during websocket cleanup for user {user_id}: {str(cleanup_error)}")


@router.websocket("/ws/topic/{topic_id}/{user_id}")
async def topic_websocket_endpoint(websocket: WebSocket, topic_id: str, user_id: int):
    """
    WebSocket endpoint for individual topic message updates.
    
    URL pattern: /ws/topic/{topic_id}/{user_id}
    - topic_id: The specific topic ID to subscribe to
    - user_id: User ID subscribing to topic updates
    
    """
    
    logger.info(f"Topic WebSocket connection attempt: topic_id={topic_id}, user_id={user_id}")
    
    # Subscribe user to topic websocket notifications using unified interface
    success = await topic_websocket_manager.subscribe(topic_id, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to topic {topic_id}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Successfully connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_topic_client_message(websocket, topic_id, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"Topic WebSocket disconnected: topic_id={topic_id}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in topic websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in topic websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        try:
            await topic_websocket_manager.disconnect(websocket)
            logger.info(f"Topic WebSocket cleanup completed: topic_id={topic_id}, user_id={user_id}")
        except Exception as cleanup_error:
            logger.error(f"Error during topic websocket cleanup for user {user_id}: {str(cleanup_error)}")


async def _handle_client_message(websocket: WebSocket, obj_type: str, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "obj_type": obj_type,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(websocket_manager.get_connected_users(obj_type))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


async def _handle_topic_client_message(websocket: WebSocket, topic_id: str, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the topic client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "topic_id": topic_id,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(topic_websocket_manager.get_connected_users(topic_id))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


@router.get("/ws/stats", response_model=WebSocketStats)
async def get_websocket_stats():
    """
    Get current WebSocket connection statistics.
    
    Returns connection counts by object type and total active connections.
    Useful for monitoring and debugging.
    """
    try:
        stats_data = websocket_manager.get_connection_stats()
        
        return WebSocketStats(
            total_active=stats_data.get("total_active", 0),
            by_object_type={k: v for k, v in stats_data.items() if k != "total_active"},
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error getting websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving websocket statistics")


@router.get("/ws/connections/{obj_type}")
async def get_connected_users(obj_type: str = Path(..., description="Object type")):
    """
    Get list of users connected to a specific object type.
    Args:
        obj_type: Object type (any text)
    Returns:
        List of connected user IDs
    """
    
    try:
        connected_users = websocket_manager.get_connected_users(obj_type)
        
        return {
            "obj_type": obj_type,
            "connected_users": connected_users,
            "count": len(connected_users),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting connected users for {obj_type}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving connected users")





@router.get("/ws/topic/connections/{topic_id}")
async def get_topic_connected_users(topic_id: str = Path(..., description="Topic ID")):
    """
    Get list of users connected to a specific topic.
    Args:
        topic_id: Topic ID
    Returns:
        List of connected user IDs
    """
    # Validate topic_id
    if topic_id <= 0:
        raise HTTPException(status_code=400, detail="Invalid topic_id")
    
    try:
        connected_users = topic_websocket_manager.get_connected_users(topic_id)
        
        return {
            "topic_id": topic_id,
            "connected_users": connected_users,
            "count": len(connected_users),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting connected users for topic {topic_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic connected users")





@router.get("/ws/topic/stats")
async def get_topic_websocket_stats():
    """
    Get current topic WebSocket connection statistics.
    
    Returns connection counts by topic ID and total active connections.
    Useful for monitoring and debugging.
    """
    try:
        stats_data = topic_websocket_manager.get_connection_stats()
        
        return {
            "total_active": stats_data.get("total_active", 0),
            "by_topic": {k: v for k, v in stats_data.items() if k != "total_active"},
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting topic websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic websocket statistics")



