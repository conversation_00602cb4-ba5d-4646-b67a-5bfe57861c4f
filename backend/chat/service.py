from typing import List, Optional
from chat import data as chat_data
from chat import models as chat_models
from chat import constants as chat_constants
from chat.utils import ChatPaginator
from chat.web_sockets.websocket_notifications import notification_service
from chat.web_sockets.websocket_manager import topic_websocket_manager
from exceptions.exceptions import NotFoundException, InvalidAccessException
from logger.logger import logger
import asyncio


async def get_topics_by_objects(
    object_type: str, object_ids: Optional[List[str]] = None, user_ids: Optional[List[int]] = None
) -> List[dict]:
    """Get all topics for objects of a specific type for one or more users"""
    return await chat_data.get_topics_by_objects(object_type, object_ids, user_ids)


async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: int) -> dict:
    """Create a new chat topic"""
    topic_id = await chat_data.create_topic(topic_data, user_id)
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    
    # Send websocket notification for topic creation (async, don't wait)
    asyncio.create_task(notification_service.handle_topic_created(
        topic_id=topic_id,
        object_type=topic_data.object_type,
        object_ids=topic_data.object_ids,
        creator_user_id=int(user_id)
    ))

    logger.info(f"Created topic {topic_id} for user {user_id}")
    
    return topics[0] if topics else {}


async def get_topic_by_id(topic_id: str, user_id: int) -> dict:
    """Get a specific topic by ID using the same query structure as object endpoints"""
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    if not topics:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)
    return topics[0]  # Return the first (and only) topic


async def update_topic(topic_id: str, topic_data: chat_models.ChatTopicUpdate, user_id: int) -> dict:
    """Update a chat topic"""
    await chat_data.update_topic(topic_id, topic_data, user_id)
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    return topics[0] if topics else {}


async def delete_topic(topic_id: str, user_id: int) -> bool:
    """Archive a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Send websocket notification for topic deletion BEFORE deletion (async, don't wait)
    # Pass the user_id who is deleting the topic
    asyncio.create_task(notification_service.handle_topic_deleted_with_user(topic_id, user_id))

    return await chat_data.delete_topic(topic_id, user_id)


async def get_messages_by_topic_paginated(topic_id: str, user_id: int, limit: int = 50, page: int = 1, search: Optional[str] = None) -> dict:
    """Get paginated messages for a specific topic with optional search"""
    
    # Create paginator instance
    paginator = ChatPaginator(limit=limit, page=page)
    
    # Get paginated messages and total count
    messages = await chat_data.get_messages_by_topic_paginated(topic_id, limit, paginator.offset, search)
    total_count = await chat_data.get_messages_count_by_topic(topic_id, search)
    
    # Create paginated response
    return paginator.create_paginated_response(messages, total_count)


async def reset_topic_unread_count(topic_id: str, user_id: int) -> bool:
    """Reset unread count for a user in a specific topic"""
    return await chat_data.reset_unread_count(topic_id, user_id)


async def clear_messages_by_topic(topic_id: str, user_id: int) -> bool:
    """Clear all messages in a topic (user must be a member)"""
    # Verify user is a member of the topic
    is_member = await chat_data.check_user_is_member(topic_id, user_id)
    if not is_member:
        raise InvalidAccessException(chat_constants.USER_NOT_MEMBER)
    
    # Clear all messages
    success = await chat_data.clear_messages_by_topic(topic_id)
    
    if success:
        # Broadcast message clearing to all connected topic members (async, don't wait)
        asyncio.create_task(_broadcast_message_to_topic(
            topic_id=topic_id,
            message_data={
                "topic_id": topic_id,
                "action": chat_constants.MESSAGES_CLEARED,
                "cleared_by": user_id
            },
            sender_id=user_id,
            action=chat_constants.MESSAGES_CLEARED
        ))
    
    return success


async def create_message(message_data: chat_models.ChatMessageCreate, user_id: int) -> dict:
    """Create a new message and broadcast to topic members"""
    message_id = await chat_data.create_message(message_data, user_id)
    message = await chat_data.get_message_by_id(message_id)

    # Broadcast message to all connected topic members (async, don't wait)
    asyncio.create_task(_broadcast_message_to_topic(
        topic_id=message_data.topic_id,
        message_data=message,
        sender_id=user_id,
        action=chat_constants.MESSAGE_CREATED
    ))
    
    return message


async def update_message(message_id: str, message_data: chat_models.ChatMessageUpdate, user_id: int) -> dict:
    """Update a message and broadcast to topic members"""
    # Verify message exists and belongs to user
    existing_message = await chat_data.get_message_by_id(message_id)
    if not existing_message:
        raise NotFoundException(chat_constants.MESSAGE_NOT_FOUND)

    await chat_data.update_message(message_id, message_data, user_id)
    updated_message = await chat_data.get_message_by_id(message_id)
    
    # Broadcast message update to all connected topic members (async, don't wait)
    asyncio.create_task(_broadcast_message_to_topic(
        topic_id=updated_message["topic_id"],
        message_data=updated_message,
        sender_id=user_id,
        action=chat_constants.MESSAGE_UPDATED
    ))

    return updated_message


async def delete_message(message_id: str, user_id: int) -> bool:
    """Delete a message and broadcast to topic members"""
    # Verify message exists and belongs to user
    existing_message = await chat_data.get_message_by_id(message_id)
    if not existing_message:
        raise NotFoundException(chat_constants.MESSAGE_NOT_FOUND)

    if str(existing_message["sender_id"]) != str(user_id):
        raise InvalidAccessException("You can only delete your own messages")

    # Store message info before deletion for broadcasting
    topic_id = existing_message["topic_id"]
    message_info = {
        "id": message_id,
        "topic_id": topic_id,
        "deleted": True,
        "deleted_by": user_id
    }

    success = await chat_data.delete_message(message_id, user_id)
    if not success:
        raise Exception("Message cannot be deleted")
    
    # Broadcast message deletion to all connected topic members (async, don't wait)
    asyncio.create_task(_broadcast_message_to_topic(
        topic_id=topic_id,
        message_data=message_info,
        sender_id=user_id,
        action=chat_constants.MESSAGE_DELETED
    ))

    return success


async def _broadcast_message_to_topic(topic_id: str, message_data: dict, sender_id: int, action: str):
    """
    Broadcast a message to all topic members and handle unread counts for offline users.
    
    Args:
        topic_id: The topic ID
        message_data: The message data to broadcast
        sender_id: The user ID who sent the message
        action: The action type (MESSAGE_CREATED, MESSAGE_UPDATED, MESSAGE_DELETED)
    """
    try:
        # Get topic members for broadcasting
        topic_members = await chat_data.get_topic_members(topic_id)
        member_user_ids = [member["user_id"] for member in topic_members]
        
        # For message creation, broadcast to connected users and update unread counts for offline users
        if action == chat_constants.MESSAGE_CREATED:
            # Broadcast to connected users and get list of offline users
            offline_users = await topic_websocket_manager.broadcast_message_to_topic(
                topic_id=topic_id,
                message_data=message_data,
                sender_id=sender_id,
                topic_members=member_user_ids
            )
            
            # Update unread counts for offline users
            if offline_users:
                await chat_data.update_unread_counts_for_offline_users(topic_id, offline_users)
                logger.info(f"Message created in topic {topic_id}. Updated unread count for offline users: {offline_users}")
        
        # For message updates and deletions, just broadcast to connected users (no unread count changes)
        else:
            await topic_websocket_manager.broadcast_message_update_to_topic(
                topic_id=topic_id,
                message_data=message_data,
                sender_id=sender_id,
                topic_members=member_user_ids,
                action=action
            )
            logger.info(f"Message {action} in topic {topic_id}. Broadcasted to connected users.")
            
    except Exception as e:
        logger.error(f"Error broadcasting message {action} to topic {topic_id}: {str(e)}")


async def get_unread_counts_by_user(user_id: int) -> List[dict]:
    """Get unread counts for all topics for a user"""
    return await chat_data.get_unread_counts_by_user(user_id)


async def get_topic_members(topic_id: str) -> List[dict]:
    """Get all members of a chat topic"""
    return await chat_data.get_topic_members(topic_id)


async def add_topic_members(topic_id: str, user_ids: List[int], created_by: int) -> bool:
    """Add members to a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    result = await chat_data.add_topic_members(topic_id, user_ids, created_by)
    
    # Send websocket notification for members added (async, don't wait)
    asyncio.create_task(notification_service.handle_members_added(
        topic_id=topic_id,
        added_user_ids=user_ids
    ))
    
    return result


async def remove_topic_member(topic_id: str, user_ids: List[int]) -> List[int]:
    """Remove multiple members from a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Remove members using single query
    removed_user_ids = await chat_data.remove_topic_members(topic_id, user_ids)

    # Send websocket notification for members removed (async, don't wait)
    if removed_user_ids:
        asyncio.create_task(notification_service.handle_members_removed(
            topic_id=topic_id,
            removed_user_ids=removed_user_ids
        ))

    return removed_user_ids


async def get_topics_by_object_with_members(object_type: str, object_id: str, user_id: int) -> List[dict]:
    """Get topics for a specific object with members for the authenticated user"""
    return await chat_data.get_topics_by_object_with_members(object_type, object_id, user_id)


async def pin_topic(topic_id: str, is_pinned: bool, user_id: int) -> dict:
    """Pin or unpin a chat topic for the authenticated user"""
    return await chat_data.pin_topic(topic_id, is_pinned, user_id)
