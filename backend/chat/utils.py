from typing import List, Dict, Any
from dataclasses import dataclass
from logger.logger import logger


@dataclass
class PaginationInfo:
    """Pagination information for API responses"""
    limit: int
    offset: int
    total_count: int
    has_more: bool
    
    @property
    def current_page(self) -> int:
        """Calculate current page number (1-based)"""
        return (self.offset // self.limit) + 1 if self.limit > 0 else 1
    
    @property
    def total_pages(self) -> int:
        """Calculate total number of pages"""
        return (self.total_count + self.limit - 1) // self.limit if self.limit > 0 else 1


class ChatPaginator:
    """Simple paginator utility for chat messages"""
    
    def __init__(self, limit: int = 50, page: int = 1):
        self.limit = max(1, min(100, limit))  # Ensure limit is between 1 and 100
        self.page = max(1, page)  # Ensure page is at least 1
        self.offset = (self.page - 1) * self.limit  # Calculate offset from page
        
        logger.debug(f"Initialized ChatPaginator with limit={self.limit}, page={self.page}, offset={self.offset}")
    
    def get_pagination_info(self, total_count: int) -> PaginationInfo:
        """Get pagination information"""
        pagination_info = PaginationInfo(
            limit=self.limit,
            offset=self.offset,
            total_count=total_count,
            has_more=(self.offset + self.limit) < total_count
        )
        
        logger.debug(f"Generated pagination info: current_page={pagination_info.current_page}, total_pages={pagination_info.total_pages}, has_more={pagination_info.has_more}")
        return pagination_info
    
    def create_paginated_response(self, data: List[Dict[str, Any]], total_count: int) -> Dict[str, Any]:
        """Create a paginated response with data and pagination info"""
        try:
            pagination_info = self.get_pagination_info(total_count)
            response = {
                "data": data,
                "pagination": {
                    "limit": pagination_info.limit,
                    "page": self.page,
                    "total_count": pagination_info.total_count,
                    "has_more": pagination_info.has_more,
                    "current_page": pagination_info.current_page,
                    "total_pages": pagination_info.total_pages
                }
            }
            
            logger.debug(f"Created paginated response with {len(data)} items, total_count={total_count}")
            return response
        except Exception as e:
            logger.error(f"Error creating paginated response: {str(e)}")
            raise
