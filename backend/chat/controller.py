from fastapi import APIRouter
from app.dependencies import UserDependency
from chat import constants as chat_constants
from chat import models as chat_models
from chat import service as chat_service
from chat.web_sockets.websocket_controller import router as websocket_router
from pricesmart_common import utils as common_utils
from typing import Optional
from exceptions.exceptions import CommonException
import logging

# Set up logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=[chat_constants.CHAT_API_TAG])

# Include websocket routes
router.include_router(websocket_router, prefix="/chat")


# Topic Endpoints
@router.post("/topics")
async def get_topics_by_objects(filter_data: chat_models.ChatTopicFilter):
    """Get all topics for objects of a specific type"""
    try:
        data = await chat_service.get_topics_by_objects(
            filter_data.object_type, filter_data.object_ids, filter_data.user_ids
        )
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_topics_by_objects: {str(e)}")
        raise CommonException(f"Failed to retrieve topics: {str(e)}")


@router.get("/topic/{object_type}/{object_id}")
async def get_topics_by_object_with_members(object_type: str, object_id: str, user_id: UserDependency):
    """Get topics for a specific object with members for the authenticated user"""
    try:
        data = await chat_service.get_topics_by_object_with_members(object_type, object_id, user_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_topics_by_object_with_members: {str(e)}")
        raise CommonException(f"Failed to retrieve topics for object: {str(e)}")


@router.post("/topic")
async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: UserDependency):
    """Create a new chat topic"""
    try:
        data = await chat_service.create_topic(topic_data, user_id)
        return common_utils.create_response(data=data, message=f"Chat topic '{topic_data.name}' created successfully.")
    except Exception as e:
        logger.error(f"Error in create_topic: {str(e)}")
        raise CommonException(f"Failed to create chat topic: {str(e)}")


@router.get("/topic/{topic_id}")
async def get_topic(topic_id: str, user_id: UserDependency):
    """Get a specific topic by ID using the same response structure as object endpoints"""
    try:
        data = await chat_service.get_topic_by_id(topic_id, user_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_topic: {str(e)}")
        raise CommonException(f"Failed to retrieve topic: {str(e)}")


@router.patch("/topic/{topic_id}")
async def update_topic(topic_id: str, topic_data: chat_models.ChatTopicUpdate, user_id: UserDependency):
    """Update a chat topic"""
    try:
        data = await chat_service.update_topic(topic_id, topic_data, user_id)
        return common_utils.create_response(data=data, message="Chat topic updated successfully.")
    except Exception as e:
        logger.error(f"Error in update_topic: {str(e)}")
        raise CommonException(f"Failed to update chat topic: {str(e)}")


@router.delete("/topic/{topic_id}")
async def delete_topic(topic_id: str, user_id: UserDependency):
    """Archive a chat topic"""
    try:
        await chat_service.delete_topic(topic_id, user_id)
        return common_utils.create_response(message="Chat topic archived successfully.")
    except Exception as e:
        logger.error(f"Error in delete_topic: {str(e)}")
        raise CommonException(f"Failed to archive chat topic: {str(e)}")


@router.patch("/topic/{topic_id}/pin")
async def pin_topic(topic_id: str, pin_data: chat_models.ChatPinTopicRequest, user_id: UserDependency):
    """Pin or unpin a chat topic for the authenticated user"""
    try:
        data = await chat_service.pin_topic(topic_id, pin_data.is_pinned, user_id)
        action = "pinned" if pin_data.is_pinned else "unpinned"
        return common_utils.create_response(data=data, message=f"Chat topic {action} successfully.")
    except Exception as e:
        logger.error(f"Error in pin_topic: {str(e)}")
        raise CommonException(f"Failed to pin/unpin chat topic: {str(e)}")


# Message Endpoints
@router.get("/messages/{topic_id}/paginated")
async def get_messages_by_topic_paginated(
    topic_id: str, 
    user_id: UserDependency,
    limit: int = 50,
    page: int = 1,
    search: Optional[str] = None
):
    """Get paginated messages for a specific topic with optional search"""
    try:
        result = await chat_service.get_messages_by_topic_paginated(topic_id, user_id, limit, page, search)
        return common_utils.create_response(data=result)
    except Exception as e:
        logger.error(f"Error in get_messages_by_topic_paginated: {str(e)}")
        raise CommonException(f"Failed to retrieve messages: {str(e)}")


@router.delete("/messages/{topic_id}/clear")
async def clear_messages_by_topic(topic_id: str, user_id: UserDependency):
    """Clear all messages in a topic (user must be a member)"""
    try:
        await chat_service.clear_messages_by_topic(topic_id, user_id)
        return common_utils.create_response(message="All messages in the topic have been cleared successfully.")
    except Exception as e:
        logger.error(f"Error in clear_messages_by_topic: {str(e)}")
        raise CommonException(f"Failed to clear messages: {str(e)}")


@router.post("/message")
async def create_message(message_data: chat_models.ChatMessageCreate, user_id: UserDependency):
    """Create a new message"""
    try:
        data = await chat_service.create_message(message_data, user_id)
        return common_utils.create_response(data=data, message="Message sent successfully.")
    except Exception as e:
        logger.error(f"Error in create_message: {str(e)}")
        raise CommonException(f"Failed to send message: {str(e)}")


@router.patch("/message/{message_id}")
async def update_message(message_id: str, message_data: chat_models.ChatMessageUpdate, user_id: UserDependency):
    """Update a message"""
    try:
        data = await chat_service.update_message(message_id, message_data, user_id)
        return common_utils.create_response(data=data, message="Message updated successfully.")
    except Exception as e:
        logger.error(f"Error in update_message: {str(e)}")
        raise CommonException(f"Failed to update message: {str(e)}")


@router.delete("/message/{message_id}")
async def delete_message(message_id: str, user_id: UserDependency):
    """Delete a message"""
    try:
        await chat_service.delete_message(message_id, user_id)
        return common_utils.create_response(message="Message deleted successfully.")
    except Exception as e:
        logger.error(f"Error in delete_message: {str(e)}")
        raise CommonException(f"Failed to delete message: {str(e)}")


@router.post("/topic/{topic_id}/reset-unread")
async def reset_topic_unread_count(topic_id: str, user_id: UserDependency):
    """Reset unread count for a user in a specific topic"""
    try:
        await chat_service.reset_topic_unread_count(topic_id, user_id)
        return common_utils.create_response(message="Unread count reset successfully.")
    except Exception as e:
        logger.error(f"Error in reset_topic_unread_count: {str(e)}")
        raise CommonException(f"Failed to reset unread count: {str(e)}")


# Unread Endpoints
@router.post("/unread")
async def get_unread_counts_by_user(filter_data: chat_models.ChatUnreadFilter):
    """Get unread counts for all topics for a user"""
    try:
        data = await chat_service.get_unread_counts_by_user(filter_data.user_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_unread_counts_by_user: {str(e)}")
        raise CommonException(f"Failed to retrieve unread counts: {str(e)}")


# Member Endpoints
@router.get("/topic-members/{topic_id}")
async def get_topic_members(topic_id: str):
    """Get all members of a chat topic"""
    try:
        data = await chat_service.get_topic_members(topic_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_topic_members: {str(e)}")
        raise CommonException(f"Failed to retrieve topic members: {str(e)}")


@router.post("/topic-members/{topic_id}")
async def add_topic_members(topic_id: str, user_ids: list[int], user_id: UserDependency):
    """Add members to a chat topic"""
    try:
        await chat_service.add_topic_members(topic_id, user_ids, user_id)
        return common_utils.create_response(message="Members added to chat topic successfully.")
    except Exception as e:
        logger.error(f"Error in add_topic_members: {str(e)}")
        raise CommonException(f"Failed to add members to chat topic: {str(e)}")


@router.delete("/topic-members/{topic_id}")
async def remove_topic_members(topic_id: str, request: chat_models.ChatRemoveMembersRequest, user_id: UserDependency):
    """Remove multiple members from a chat topic"""
    try:
        removed_user_ids = await chat_service.remove_topic_member(topic_id, request.user_ids)
        return common_utils.create_response(
            data={"removed_user_ids": removed_user_ids},
            message=f"Successfully removed {len(removed_user_ids)} member(s) from chat topic.",
        )
    except Exception as e:
        logger.error(f"Error in remove_topic_members: {str(e)}")
        raise CommonException(f"Failed to remove members from chat topic: {str(e)}")
