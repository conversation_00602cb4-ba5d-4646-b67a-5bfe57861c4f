GET_TOPICS_BY_OBJECTS = """
    WITH object_ids_expanded AS (
        SELECT DISTINCT unnest({object_ids}::text[]) AS object_id
    ),
    user_ids_expanded AS (
        SELECT DISTINCT unnest({user_ids}::int[]) AS user_id
    ),
    topic_object_map AS (
        SELECT 
            oie.object_id,
            ct.topic_id
        FROM object_ids_expanded oie
        JOIN {promo_schema}.tb_chat_topic ct
            ON ct.object_ids && ARRAY[oie.object_id]
        WHERE ct.object_type = {object_type}
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            uie.user_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        CROSS JOIN user_ids_expanded uie
        LEFT JOIN {promo_schema}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = uie.user_id
        GROUP BY tom.object_id, uie.user_id, cu.unread_count
    )
    SELECT 
        object_id as obj_id,
        user_id,
        topics_count,
        message_count,
        unread_count,
        {object_type}::text as obj_type
    FROM counts
    ORDER BY object_id, user_id
"""


GET_TOPICS_BY_OBJECT_TYPE = """
    WITH topic_object_map AS (
        SELECT 
            unnest(ct.object_ids) AS object_id,
            ct.topic_id
        FROM {promo_schema}.tb_chat_topic ct
        WHERE ct.object_type = {object_type}
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        LEFT JOIN {promo_schema}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = {user_id}
        GROUP BY tom.object_id, cu.unread_count
    )
    SELECT 
        object_id as obj_id,
        topics_count,
        message_count,
        unread_count,
        {object_type}::text as obj_type
    FROM counts
    ORDER BY object_id
"""


GET_TOPIC_BY_ID = """
    SELECT 
        ct.topic_id,
        ct.name,
        ct.description,
        ct.object_type,
        ct.object_ids,
        ct.app_code,
        ct.created_by,
        ct.created_at,
        ct.updated_by,
        ct.updated_at,
        ct.status,
        um.name as created_by_user,
        uum.name as updated_by_user
    FROM {promo_schema}.tb_chat_topic ct
    LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
    WHERE ct.topic_id = '{topic_id}'::uuid
"""


# Optimized batch queries for create_topic
CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD = """
    WITH topic_insert AS (
        INSERT INTO {promo_schema}.tb_chat_topic (
            name, description, object_type, object_ids, app_code, created_by, status
        ) VALUES (
            {name}, {description}, {object_type}, {object_ids}, {app_code}, {created_by}, 'open'
        ) RETURNING topic_id
    ),
    members_insert AS (
        INSERT INTO {promo_schema}.tb_chat_member (topic_id, user_id, created_by)
        SELECT ti.topic_id, unnest({all_user_ids}::int[]), {created_by}
        FROM topic_insert ti
        ON CONFLICT (topic_id, user_id) DO NOTHING
    ),
    unread_insert AS (
        INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
        SELECT ti.topic_id, unnest({all_user_ids}::int[]), 0
        FROM topic_insert ti
        ON CONFLICT (topic_id, user_id) DO NOTHING
    )
    SELECT topic_id FROM topic_insert
"""

UPDATE_TOPIC = """
    UPDATE {promo_schema}.tb_chat_topic 
    SET 
        name = COALESCE({name}, name),
        description = COALESCE({description}, description),
        status = COALESCE({status}, status),
        app_code = COALESCE({app_code}, app_code),
        updated_by = {updated_by},
        updated_at = NOW()
    WHERE topic_id = '{topic_id}'::uuid
"""

DELETE_TOPIC = """
    UPDATE {promo_schema}.tb_chat_topic 
    SET status = 'archived', updated_at = NOW(), updated_by = {updated_by}
    WHERE topic_id = '{topic_id}'::uuid
"""

# Chat Member Queries
ADD_TOPIC_MEMBERS = """
    INSERT INTO {promo_schema}.tb_chat_member (topic_id, user_id, created_by)
    SELECT '{topic_id}'::uuid, unnest({user_ids}::int[]), {created_by}
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

CREATE_UNREAD_ENTRIES_FOR_MEMBERS = """
    INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
    SELECT '{topic_id}'::uuid, unnest({user_ids}::int[]), 0
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

REMOVE_TOPIC_MEMBERS = """
    WITH removed_members AS (
        DELETE FROM {promo_schema}.tb_chat_member 
        WHERE topic_id = '{topic_id}'::uuid AND user_id = ANY({user_ids}::int[])
        RETURNING user_id
    ),
    removed_unread AS (
        DELETE FROM {promo_schema}.tb_chat_unread 
        WHERE topic_id = '{topic_id}'::uuid AND user_id = ANY({user_ids}::int[])
    )
    SELECT user_id FROM removed_members
"""

PIN_TOPIC = """
    UPDATE {promo_schema}.tb_chat_member 
    SET 
        is_pinned = {is_pinned},
        updated_by = {user_id},
        updated_at = NOW()
    WHERE topic_id = '{topic_id}'::uuid 
    AND user_id = {user_id}
"""

GET_TOPIC_MEMBERS = """
    SELECT 
        cm.user_id,
        um.name as user_name,
        cm.is_pinned,
        cm.id as member_id
    FROM {promo_schema}.tb_chat_member cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
    WHERE cm.topic_id = '{topic_id}'::uuid
    ORDER BY um.name
"""

CHECK_USER_IS_MEMBER = """
    SELECT COUNT(*) as count
    FROM {promo_schema}.tb_chat_member
    WHERE topic_id = '{topic_id}'::uuid AND user_id = {user_id}
"""

# Chat Message Queries
GET_MESSAGES_BY_TOPIC_PAGINATED = """
    SELECT 
        cm.id,
        cm.topic_id,
        cm.sender_id,
        cm.content,
        cm.tagged_users,
        cm.priority,
        cm.created_at,
        cm.edited,
        cm.deleted,
        cm.reply_to,
        cm.is_pinned,
        um.name as sender_name
    FROM {promo_schema}.tb_chat_message cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.sender_id
    WHERE cm.topic_id = '{topic_id}'::uuid
    {search_condition}
    ORDER BY cm.created_at DESC
    LIMIT {limit} OFFSET {offset}
"""

GET_MESSAGES_COUNT_BY_TOPIC = """
    SELECT COUNT(*) as total_count
    FROM {promo_schema}.tb_chat_message cm
    WHERE cm.topic_id = '{topic_id}'::uuid
    {search_condition}
"""

CLEAR_MESSAGES_BY_TOPIC = """
    UPDATE {promo_schema}.tb_chat_message 
    SET deleted = true
    WHERE topic_id = '{topic_id}'::uuid
"""

CREATE_MESSAGE = """
    INSERT INTO {promo_schema}.tb_chat_message (
        topic_id, sender_id, content, tagged_users, priority, reply_to
    ) VALUES (
        '{topic_id}'::uuid, {sender_id}, {content}, {tagged_users}, {priority}, {reply_to}
    ) RETURNING id
"""

UPDATE_MESSAGE = """
    UPDATE {promo_schema}.tb_chat_message 
    SET 
        content = {content},
        is_pinned = COALESCE({is_pinned}, is_pinned),
        edited = true
    WHERE id = '{message_id}'::uuid
"""

DELETE_MESSAGE = """
    UPDATE {promo_schema}.tb_chat_message 
    SET deleted = true
    WHERE id = '{message_id}'::uuid
"""

GET_MESSAGE_BY_ID = """
    SELECT 
        cm.id,
        cm.topic_id,
        cm.sender_id,
        cm.content,
        cm.tagged_users,
        cm.priority,
        cm.created_at,
        cm.edited,
        cm.deleted,
        cm.reply_to,
        cm.is_pinned,
        um.name as sender_name
    FROM {promo_schema}.tb_chat_message cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.sender_id
    WHERE cm.id = '{message_id}'::uuid
"""

# Chat Unread Queries
GET_UNREAD_COUNTS_BY_USER = """
    SELECT 
        cu.topic_id,
        cu.unread_count,
        cu.last_read_at,
        ct.name as topic_name,
        ct.object_type,
        ct.object_ids
    FROM {promo_schema}.tb_chat_unread cu
    LEFT JOIN {promo_schema}.tb_chat_topic ct ON ct.topic_id = cu.topic_id
    WHERE cu.user_id = {user_id}
    AND ct.status != 'archived'
    ORDER BY cu.unread_count DESC
"""

CREATE_MULTIPLE_UNREAD_ENTRIES = """
    INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
    SELECT '{topic_id}'::uuid, unnest({user_ids}::int[]), 0
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = unread_count + 1
    WHERE topic_id = '{topic_id}'::uuid 
    AND user_id = ANY({user_ids}::int[])
"""

RESET_UNREAD_COUNT = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = 0, last_read_at = NOW()
    WHERE topic_id = '{topic_id}'::uuid AND user_id = {user_id}
"""

RESET_ALL_UNREAD_COUNTS_FOR_TOPIC = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = 0, last_read_at = NOW()
    WHERE topic_id = '{topic_id}'::uuid
"""


GET_TOPICS_BY_OBJECT_WITH_MEMBERS = """
    WITH topic_data AS (
        SELECT 
            ct.topic_id,
            ct.name,
            ct.description,
            ct.object_type,
            ct.object_ids,
            ct.app_code,
            ct.created_by,
            ct.created_at,
            ct.updated_by,
            ct.updated_at,
            ct.status,
            um.name as created_by_user,
            uum.name as updated_by_user,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count,
            COALESCE(cm_user.is_pinned, false) AS is_pinned
        FROM {promo_schema}.tb_chat_topic ct
        LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
        LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
        LEFT JOIN {promo_schema}.tb_chat_message m ON m.topic_id = ct.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu ON cu.topic_id = ct.topic_id AND cu.user_id = {user_id}
        LEFT JOIN {promo_schema}.tb_chat_member cm_user ON cm_user.topic_id = ct.topic_id AND cm_user.user_id = {user_id}
        WHERE ct.object_type = {object_type}
          AND {object_id} = ANY(ct.object_ids)
          AND ct.status != 'archived'
          AND EXISTS (
              SELECT 1 FROM {promo_schema}.tb_chat_member cm 
              WHERE cm.topic_id = ct.topic_id AND cm.user_id = {user_id}
          )
        GROUP BY ct.topic_id, ct.name, ct.description, ct.object_type, ct.object_ids, ct.app_code,
                 ct.created_by, ct.created_at, ct.updated_by, ct.updated_at, ct.status,
                 um.name, uum.name, cu.unread_count, cm_user.is_pinned
    ),
    member_data AS (
        SELECT 
            cm.topic_id,
            json_agg(
                json_build_object(
                    'user_id', cm.user_id,
                    'user_name', um.name,
                    'is_pinned', cm.is_pinned,
                    'member_id', cm.id, 
                    'email', um.email
                ) ORDER BY um.name
            ) as members
        FROM {promo_schema}.tb_chat_member cm
        LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
        WHERE cm.topic_id IN (SELECT topic_id FROM topic_data)
        GROUP BY cm.topic_id
    )
    SELECT 
        td.*,
        COALESCE(md.members, '[]'::json) as members
    FROM topic_data td
    LEFT JOIN member_data md ON md.topic_id = td.topic_id
    ORDER BY td.created_at DESC
"""

GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS = """
    WITH topic_data AS (
        SELECT 
            ct.topic_id,
            ct.name,
            ct.description,
            ct.object_type,
            ct.object_ids,
            ct.app_code,
            ct.created_by,
            ct.created_at,
            ct.updated_by,
            ct.updated_at,
            ct.status,
            um.name as created_by_user,
            uum.name as updated_by_user,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count,
            COALESCE(cm_user.is_pinned, false) AS is_pinned
        FROM {promo_schema}.tb_chat_topic ct
        LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
        LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
        LEFT JOIN {promo_schema}.tb_chat_message m ON m.topic_id = ct.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu ON cu.topic_id = ct.topic_id AND cu.user_id = {user_id}
        LEFT JOIN {promo_schema}.tb_chat_member cm_user ON cm_user.topic_id = ct.topic_id AND cm_user.user_id = {user_id}
        WHERE ct.topic_id = '{topic_id}'::uuid AND ct.status != 'archived'
        GROUP BY ct.topic_id, ct.name, ct.description, ct.object_type, ct.object_ids, ct.app_code,
                 ct.created_by, ct.created_at, ct.updated_by, ct.updated_at, ct.status,
                 um.name, uum.name, cu.unread_count, cm_user.is_pinned
    ),
    member_data AS (
        SELECT 
            cm.topic_id,
            json_agg(
                json_build_object(
                    'user_id', cm.user_id,
                    'user_name', um.name,
                    'is_pinned', cm.is_pinned,
                    'member_id', cm.id, 
                    'email', um.email
                ) ORDER BY um.name
            ) as members
        FROM {promo_schema}.tb_chat_member cm
        LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
        WHERE cm.topic_id IN (SELECT topic_id FROM topic_data)
        GROUP BY cm.topic_id
    )
    SELECT 
        td.*,
        COALESCE(md.members, '[]'::json) as members
    FROM topic_data td
    LEFT JOIN member_data md ON md.topic_id = td.topic_id
    ORDER BY td.created_at DESC
"""
