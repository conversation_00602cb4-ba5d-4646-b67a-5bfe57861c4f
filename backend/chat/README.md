# PriceSmart Chat System

## Overview

The PriceSmart Chat System is a real-time messaging platform built with FastAPI and WebSockets, designed to facilitate communication around business objects like promotions, events, store groups, and products. The system provides topic-based conversations with real-time notifications, unread message tracking, and comprehensive member management.

## Table of Contents

- [High-Level Architecture](#high-level-architecture)
- [Low-Level Architecture](#low-level-architecture)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [WebSocket Architecture](#websocket-architecture)
- [User Flows](#user-flows)
- [Components Overview](#components-overview)
- [Deployment & Configuration](#deployment--configuration)

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Frontend]
        MOB[Mobile App]
    end
    
    subgraph "API Gateway"
        NGINX[NGINX/Load Balancer]
    end
    
    subgraph "Application Layer"
        FASTAPI[FastAPI Application]
        CHAT[Chat Module]
        WS[WebSocket Manager]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL Database)]
        REDIS[(Redis Cache)]
    end
    
    subgraph "External Services"
        FIREBASE[Firebase Auth]
        SECRETS[Google Secret Manager]
    end
    
    WEB --> NGINX
    MOB --> NGINX
    NGINX --> FASTAPI
    FASTAPI --> CHAT
    CHAT --> WS
    CHAT --> POSTGRES
    WS --> REDIS
    FASTAPI --> FIREBASE
    FASTAPI --> SECRETS
```

## Low-Level Architecture

### Module Structure

```
backend/chat/
├── __init__.py
├── constants.py          # Application constants
├── controller.py         # FastAPI route handlers
├── data.py              # Database operations
├── exceptions.py        # Custom exceptions
├── models.py           # Pydantic models
├── queries.py          # SQL queries
├── schema.sql          # Database schema
├── service.py          # Business logic
├── utils.py            # Utility functions
└── web_sockets/        # WebSocket implementation
    ├── websocket_controller.py
    ├── websocket_manager.py
    ├── websocket_models.py
    └── websocket_notifications.py
```

### Layer Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        CTRL[Controller Layer]
        WS_CTRL[WebSocket Controller]
    end
    
    subgraph "Business Logic Layer"
        SVC[Service Layer]
        WS_MGR[WebSocket Manager]
        NOTIF[Notification Service]
    end
    
    subgraph "Data Access Layer"
        DATA[Data Layer]
        QUERIES[Query Layer]
    end
    
    subgraph "Database Layer"
        TOPICS[(tb_chat_topic)]
        MESSAGES[(tb_chat_message)]
        MEMBERS[(tb_chat_member)]
        UNREAD[(tb_chat_unread)]
    end
    
    CTRL --> SVC
    WS_CTRL --> WS_MGR
    SVC --> DATA
    WS_MGR --> NOTIF
    NOTIF --> DATA
    DATA --> QUERIES
    QUERIES --> TOPICS
    QUERIES --> MESSAGES
    QUERIES --> MEMBERS
    QUERIES --> UNREAD
```

## Database Schema

### Core Tables

#### 1. tb_chat_topic
Stores chat topic/conversation information
```sql
CREATE TABLE price_promo.tb_chat_topic (
    topic_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name varchar(255) NOT NULL,
    description text,
    object_type varchar(100) NOT NULL,
    object_ids text[] NOT NULL,
    app_code chat_app_code_enum,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now(),
    updated_by int4,
    updated_at timestamp,
    status chat_status_enum DEFAULT 'open'
);
```

#### 2. tb_chat_message
Stores individual messages within topics
```sql
CREATE TABLE price_promo.tb_chat_message (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id uuid NOT NULL REFERENCES tb_chat_topic(topic_id),
    sender_id int4 NOT NULL,
    content text NOT NULL,
    tagged_users int4[],
    priority chat_priority_enum DEFAULT 'low',
    created_at timestamp DEFAULT now(),
    edited boolean DEFAULT false,
    deleted boolean DEFAULT false,
    reply_to uuid REFERENCES tb_chat_message(id),
    is_pinned boolean DEFAULT false
);
```

#### 3. tb_chat_member
Manages topic membership and user preferences
```sql
CREATE TABLE price_promo.tb_chat_member (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id uuid NOT NULL REFERENCES tb_chat_topic(topic_id),
    user_id int4 NOT NULL,
    is_pinned boolean DEFAULT false,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now(),
    updated_by int4,
    updated_at timestamp,
    UNIQUE(topic_id, user_id)
);
```

#### 4. tb_chat_unread
Tracks unread message counts per user per topic
```sql
CREATE TABLE price_promo.tb_chat_unread (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id uuid NOT NULL REFERENCES tb_chat_topic(topic_id),
    user_id int4 NOT NULL,
    unread_count int4 DEFAULT 0,
    last_read_at timestamp,
    UNIQUE(topic_id, user_id)
);
```

### Enums
```sql
CREATE TYPE chat_priority_enum AS ENUM ('low', 'medium', 'high');
CREATE TYPE chat_status_enum AS ENUM ('open', 'resolved', 'closed', 'archived');
CREATE TYPE chat_app_code_enum AS ENUM ('promosmart', 'markdown', 'basesmart');
```

## API Endpoints

### Topic Management
- `GET /api/v3/chat/topics/{object_type}` - Get topics by object type
- `POST /api/v3/chat/topic` - Create new topic
- `GET /api/v3/chat/topic/{topic_id}` - Get topic details
- `PUT /api/v3/chat/topic/{topic_id}` - Update topic
- `DELETE /api/v3/chat/topic/{topic_id}` - Delete topic

### Message Management
- `GET /api/v3/chat/messages/{topic_id}` - Get paginated messages
- `POST /api/v3/chat/message` - Create new message
- `PUT /api/v3/chat/message/{message_id}` - Update message
- `DELETE /api/v3/chat/message/{message_id}` - Delete message
- `DELETE /api/v3/chat/messages/{topic_id}/clear` - Clear all messages

### Member Management
- `GET /api/v3/chat/topic/{topic_id}/members` - Get topic members
- `POST /api/v3/chat/topic/{topic_id}/members` - Add members
- `DELETE /api/v3/chat/topic/{topic_id}/members` - Remove members

### WebSocket Endpoints
- `WS /api/v3/chat/ws/{obj_type}/{user_id}` - Topic updates subscription
- `WS /api/v3/chat/ws/topic/{topic_id}/{user_id}` - Message updates subscription

## WebSocket Architecture

### Dual WebSocket System

The chat system implements two separate WebSocket managers:

#### 1. Topic Updates WebSocket (`WebSocketManager`)
- **Purpose**: Notifies users about topic-level changes
- **Scope**: Object-type based subscriptions (promo, event, store_group, product)
- **Events**: Topic creation, member changes, topic deletion
- **Connection Pattern**: `/ws/{obj_type}/{user_id}`

#### 2. Message Updates WebSocket (`TopicWebSocketManager`)
- **Purpose**: Real-time message delivery within specific topics
- **Scope**: Topic-specific subscriptions
- **Events**: New messages, message updates, message deletions
- **Connection Pattern**: `/ws/topic/{topic_id}/{user_id}`

```mermaid
graph TB
    subgraph "WebSocket Architecture"
        subgraph "Topic Updates"
            WS_MGR[WebSocketManager]
            OBJ_CONN[Object Type Connections]
        end
        
        subgraph "Message Updates"
            TOPIC_MGR[TopicWebSocketManager]
            TOPIC_CONN[Topic Connections]
        end
        
        subgraph "Notification Service"
            NOTIF_SVC[WebSocketNotificationService]
        end
    end
    
    CLIENT[Client] --> WS_MGR
    CLIENT --> TOPIC_MGR
    WS_MGR --> OBJ_CONN
    TOPIC_MGR --> TOPIC_CONN
    NOTIF_SVC --> WS_MGR
    NOTIF_SVC --> TOPIC_MGR
```

## User Flows

### 1. Topic Creation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant S as Service
    participant D as Data Layer
    participant N as Notification Service
    participant W as WebSocket Manager
    
    U->>C: POST /topic (topic_data)
    C->>S: create_topic(topic_data, user_id)
    S->>D: create_topic(topic_data, user_id)
    D->>D: Insert topic, members, unread entries
    D-->>S: topic_id
    S->>N: handle_topic_created(topic_id, ...)
    N->>W: broadcast_to_users(affected_users)
    S-->>C: topic_details
    C-->>U: Response with topic data
    
    Note over N,W: Async notification to all<br/>affected users via WebSocket
```

### 2. Message Creation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant S as Service
    participant D as Data Layer
    participant TM as TopicWebSocketManager
    
    U->>C: POST /message (message_data)
    C->>S: create_message(message_data, user_id)
    S->>D: create_message(message_data, user_id)
    D-->>S: message_id
    S->>D: get_message_by_id(message_id)
    D-->>S: message_details
    S->>TM: broadcast_message_to_topic(topic_id, message)
    TM->>TM: Send to connected users
    TM->>D: update_unread_counts_for_offline_users()
    S-->>C: message_details
    C-->>U: Response with message data
    
    Note over TM: Real-time delivery to<br/>connected topic members
```

### 3. WebSocket Connection Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant WC as WebSocket Controller
    participant WM as WebSocket Manager
    participant DB as Database
    
    C->>WC: Connect to /ws/{obj_type}/{user_id}
    WC->>WM: subscribe(obj_type, user_id, websocket)
    WM->>WM: Accept connection & store
    WM-->>WC: success = True
    WC->>C: Send welcome message
    
    loop Keep Alive
        C->>WC: Send ping/status message
        WC->>C: Send pong/status response
    end
    
    Note over C,DB: Connection maintained until<br/>client disconnects or error occurs
```

## Components Overview

### 1. Controller Layer (`controller.py`)
- **Responsibility**: HTTP request handling and routing
- **Key Features**:
  - RESTful API endpoints
  - Request validation using Pydantic models
  - User authentication via `UserDependency`
  - WebSocket route inclusion

### 2. Service Layer (`service.py`)
- **Responsibility**: Business logic and orchestration
- **Key Features**:
  - Topic and message management
  - Member management
  - Async WebSocket notifications
  - Unread count management
  - Access control validation

### 3. Data Layer (`data.py`)
- **Responsibility**: Database operations and data persistence
- **Key Features**:
  - Optimized batch operations
  - Pagination support
  - Complex query execution
  - Transaction management

### 4. WebSocket Manager (`web_sockets/websocket_manager.py`)
- **Responsibility**: Real-time communication management
- **Key Features**:
  - Dual WebSocket system
  - Connection lifecycle management
  - Message broadcasting
  - Connection cleanup and error handling

### 5. Notification Service (`web_sockets/websocket_notifications.py`)
- **Responsibility**: Event-driven notifications
- **Key Features**:
  - Topic-level event handling
  - User data aggregation
  - Async notification delivery


### Dependencies
- **FastAPI**: Web framework and WebSocket support
- **PostgreSQL**: Primary database
- **asyncpg**: Async PostgreSQL driver
- **Pydantic**: Data validation and serialization
- **Firebase Admin**: Authentication
- **Google Cloud Secret Manager**: Configuration management

### Performance Considerations
- **Connection Pooling**: Async database connection management
- **Batch Operations**: Optimized multi-record operations
- **Pagination**: Efficient large dataset handling
- **WebSocket Cleanup**: Automatic connection lifecycle management
- **Async Processing**: Non-blocking notification delivery

### Monitoring & Logging
- **WebSocket Statistics**: Real-time connection monitoring
- **Error Handling**: Comprehensive exception management
- **Audit Trail**: Complete message and topic history
- **Performance Metrics**: Query execution and response times

## Advanced User Flows

### 4. Real-time Message Broadcasting Flow

```mermaid
graph TB
    subgraph "Message Broadcasting System"
        MSG[New Message Created]
        TM[TopicWebSocketManager]
        CONN[Active Connections]
        OFFLINE[Offline Users]
        UNREAD[Unread Count Update]
    end

    MSG --> TM
    TM --> CONN
    TM --> OFFLINE
    OFFLINE --> UNREAD

    CONN --> |Real-time delivery| USER1[Connected User 1]
    CONN --> |Real-time delivery| USER2[Connected User 2]
    UNREAD --> |Database update| DB[(Database)]
```

### 5. Topic Subscription Management Flow

```mermaid
sequenceDiagram
    participant U as User
    participant WS as WebSocket Controller
    participant TM as TopicWebSocketManager
    participant DB as Database
    participant OTHER as Other Topic Members

    U->>WS: Connect to topic WebSocket
    WS->>TM: subscribe(topic_id, user_id, websocket)
    TM->>TM: Check existing connections
    TM->>TM: Remove old connection if exists
    TM->>TM: Add new connection
    TM-->>WS: Connection successful
    WS->>U: Welcome message

    Note over U,OTHER: User is now subscribed to real-time updates

    OTHER->>TM: Send message to topic
    TM->>U: Broadcast message

    U->>WS: Disconnect
    WS->>TM: cleanup connection
    TM->>TM: Remove user connection
```

### 6. Unread Count Management Flow

```mermaid
graph TB
    subgraph "Unread Count System"
        NEW_MSG[New Message]
        CHECK_ONLINE[Check Online Users]
        ONLINE[Online Users]
        OFFLINE[Offline Users]
        BROADCAST[Real-time Broadcast]
        UPDATE_UNREAD[Update Unread Count]
        MARK_READ[Mark as Read]
    end

    NEW_MSG --> CHECK_ONLINE
    CHECK_ONLINE --> ONLINE
    CHECK_ONLINE --> OFFLINE
    ONLINE --> BROADCAST
    OFFLINE --> UPDATE_UNREAD
    UPDATE_UNREAD --> |User comes online| MARK_READ
```

## Data Flow Architecture

### Message Processing Pipeline

```mermaid
graph LR
    subgraph "Input Layer"
        API[REST API]
        WS_IN[WebSocket Input]
    end

    subgraph "Processing Layer"
        VALIDATE[Validation]
        AUTH[Authentication]
        BUSINESS[Business Logic]
    end

    subgraph "Storage Layer"
        POSTGRES[(PostgreSQL)]
        CACHE[(Redis Cache)]
    end

    subgraph "Output Layer"
        WS_OUT[WebSocket Output]
        RESPONSE[HTTP Response]
    end

    API --> VALIDATE
    WS_IN --> VALIDATE
    VALIDATE --> AUTH
    AUTH --> BUSINESS
    BUSINESS --> POSTGRES
    BUSINESS --> CACHE
    BUSINESS --> WS_OUT
    BUSINESS --> RESPONSE
```

### WebSocket Connection Management

```mermaid
graph TB
    subgraph "Connection Lifecycle"
        CONNECT[Client Connect]
        VALIDATE[Validate Parameters]
        ACCEPT[Accept Connection]
        STORE[Store Connection]
        MONITOR[Monitor Health]
        CLEANUP[Cleanup on Disconnect]
    end

    subgraph "Connection Storage"
        OBJ_CONN[Object Type Connections]
        TOPIC_CONN[Topic Connections]
        ACTIVE_SET[Active Connections Set]
    end

    CONNECT --> VALIDATE
    VALIDATE --> ACCEPT
    ACCEPT --> STORE
    STORE --> OBJ_CONN
    STORE --> TOPIC_CONN
    STORE --> ACTIVE_SET
    STORE --> MONITOR
    MONITOR --> CLEANUP
```




## Performance Optimization

### Database Optimization Strategies

1. **Indexing Strategy**
   ```sql
   -- Topic lookup by object type and IDs
   CREATE INDEX idx_chat_topic_object_type_ids ON tb_chat_topic USING GIN (object_ids);

   -- Message retrieval by topic
   CREATE INDEX idx_chat_message_topic_created ON tb_chat_message (topic_id, created_at DESC);

   -- Member lookup
   CREATE INDEX idx_chat_member_topic_user ON tb_chat_member (topic_id, user_id);

   -- Unread count lookup
   CREATE INDEX idx_chat_unread_user_topic ON tb_chat_unread (user_id, topic_id);
   ```

2. **Query Optimization**
   - Batch operations for member and unread entry creation
   - Pagination with efficient offset handling
   - Optimized JOIN operations with proper indexing

3. **Connection Management**
   - WebSocket connection pooling
   - Automatic cleanup of stale connections
   - Health check mechanisms



## Monitoring & Observability

### Key Metrics to Monitor

1. **WebSocket Metrics**
   - Active connection count by object type
   - Connection duration
   - Message delivery success rate
   - Connection failure rate

2. **Database Metrics**
   - Query execution time
   - Connection pool utilization
   - Transaction success rate
   - Index usage statistics

3. **Application Metrics**
   - API response times
   - Error rates by endpoint
   - Message throughput
   - User activity patterns


*This comprehensive documentation covers all aspects of the PriceSmart Chat System, from high-level architecture to detailed implementation specifics, providing a complete reference for development, deployment, and maintenance teams.*
