
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common.data import get_config_value


async def get_store_hierarchy_id_mapping()-> dict[int,HierarchyFiltersInfoType]:
    store_hierarchy_config = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    return {
        hierarchy_value["id"]: hierarchy_value
        for hierarchy_value in store_hierarchy_config.values()
    }
