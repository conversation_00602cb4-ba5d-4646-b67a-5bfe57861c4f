from fastapi import APIRouter
from product.controller import product_router
from product_group.controller import router as product_group_router
from store_group.controller import router as store_group_router
from promotions.controller import promo_router,promo_v3_router
from reports.controller import reports_router
from workbench.controller import router as workbench_router
from decision_dashboard.controller import router as dd_router
from marketing_calendar.controller import router as marketing_calendar_router
from events.controller import events_router
from filters.controller import filters_router
from fiscal_calendar.controller import router as fiscal_calendar_router
from notifications.controller import router as notification_router
from server_sent_events.controller import sse_router
from pricesmart_common import constants as global_constants
from pricesmart_common.controller import common_router
from landing_page.controller import landing_page_router
from chat.controller import router as chat_router

router = APIRouter()


router.include_router(router=product_router, prefix=global_constants.API_PREFIX)
router.include_router(router=product_group_router,prefix=global_constants.API_PREFIX)
router.include_router(router=store_group_router,prefix=global_constants.API_PREFIX)
router.include_router(router=promo_router, prefix=global_constants.API_PREFIX)
router.include_router(router=promo_v3_router,prefix=global_constants.API_V3_PREFIX)
router.include_router(router=sse_router, prefix=global_constants.API_PREFIX)
router.include_router(router=reports_router, prefix=global_constants.API_PREFIX)
router.include_router(router=workbench_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=dd_router,prefix=global_constants.API_V3_PREFIX)
router.include_router(router=marketing_calendar_router,prefix=global_constants.API_V3_PREFIX)
router.include_router(router=events_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=fiscal_calendar_router, prefix=global_constants.API_PREFIX)
router.include_router(router=notification_router, prefix=global_constants.API_PREFIX)
router.include_router(router=filters_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=common_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=landing_page_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=chat_router, prefix=global_constants.API_V3_PREFIX)

