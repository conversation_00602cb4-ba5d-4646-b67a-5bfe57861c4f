from typing import Annotated
from async_lru import alru_cache
from fastapi import Depends, Request
from exceptions.exceptions import UnderMaintenanceException
from pricesmart_common.utils import async_execute_query
from pricesmart_common import constants as pricesmart_common_constants

@alru_cache(ttl=60)
async def is_maintenance():
    try:
        query = """
        select 1 from metaschema.tb_app_sub_master where name = 'Maintenance' and is_active=1
        """

        res = await async_execute_query(query)
    except Exception:
        res = 2 # not able to communicate to the source, check database connectivity, or  contact to the DevOps team. 
    return res


async def check_maintenance():
    if await is_maintenance():
        raise UnderMaintenanceException


async def get_user_id(
    request: Request
):
    return request.state.user_id


async def get_event_dependency():
    query = """
        select case when lower(config_value) = 'true' then True else False end as use_events from price_promo.tb_tool_configurations  where module='event' and config_name = 'use_event';
    """
    res = await async_execute_query(query)
    pricesmart_common_constants.USE_EVENTS = res[0]['use_events'] if res else False
    


UserDependency = Annotated[int,Depends(get_user_id)]

