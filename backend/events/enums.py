from enum import Enum


class EventProductSelectionType(Enum):
    WHOLE_CATEGORY = "whole_category"
    SPECIFIC_PRODUCTS = "specific_products"
    SITE_WIDE = "sitewide"
    PRODUCT_GROUP = "product_group"


class EventStoreSelectionType(Enum):
    BNM_STORES = "bnm_stores"
    ECOM_STORES = "ecom_stores"
    ALL_STORES = "all_stores"
    STORE_GROUP = "store_group"
    SPECIFIC_STORES = "specific_stores"

