from events.enums import EventProductSelectionType,EventStoreSelectionType

PRODUCT_SELECTION_TYPE_ID_MAPPING = {
    EventProductSelectionType.SITE_WIDE.value: 1,
    EventProductSelectionType.WHOLE_CATEGORY.value: 2,
    EventProductSelectionType.SPECIFIC_PRODUCTS.value: 4,
    EventProductSelectionType.PRODUCT_GROUP.value: 3
}


STORE_SELECTION_TYPE_ID_MAPPING = {
    EventStoreSelectionType.ALL_STORES.value: 1,
    EventStoreSelectionType.BNM_STORES.value: 2,
    EventStoreSelectionType.ECOM_STORES.value: 3,
    EventStoreSelectionType.SPECIFIC_STORES.value: 4,
    EventStoreSelectionType.STORE_GROUP.value: 7
}

SCREEN_NAME = "Event"