import json
import httpx
from common_utils.notifications import NotificationModules
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from events import data as events_data
from events import models as event_models
from configuration.environment import environment
from exceptions.exceptions import ConflictException
from logger.logger import logger
from pricesmart_common.data import get_config_value
from promotions.decorators import log_action_decorator
from promotions import constants as promo_constants
from promotions import utils as promo_utils
from events import constants as event_constants
from promotions import service as promo_service
from fastapi import BackgroundTasks
from promotions import models as promo_models
from server_sent_events.utils import insert_notification
from events import exceptions as event_exceptions
from client_configuration import constants as client_configuration_constants

async def create_event(event_details: event_models.EventModel, user_id: int):
    data = await events_data.create_event(event_details, user_id)
    return data

async def get_event_types():
    return await events_data.get_event_types()

async def get_event_objectives():
    return await events_data.get_event_objectives()

async def get_events(get_events_filters: event_models.GetEventsFilters):
    return await events_data.get_events(get_events_filters)

async def get_event(event_id: int):
    return await events_data.get_event(event_id)

async def update_event(event_id: int, request_payload: event_models.EventModel, user_id: int):
    return await events_data.update_event(event_id, request_payload, user_id)

async def get_event_effected_offers(event_id: int, request_payload: event_models.EventModel):
    return await events_data.get_event_effected_offers(event_id, request_payload)

@log_action_decorator(
    get_promo_ids_func=lambda i: [i["promo_id"] for i in i._effected_offers],
    action_name=promo_constants.ACTION_EDIT,
    screen_name=event_constants.SCREEN_NAME,
    update_task_completion=False,
)
async def update_event_and_its_effected_offers(
    request_payload: event_models.UpdateEventModel,
    user_id: int,
    event_id: int,
    action_log_id: int
):
    async def handle_exception(e,message):
        await events_data.unset_event_under_processing(event_id)
        await insert_notification(
            request = {},
            user_id=user_id,
            module=NotificationModules.PROMOTIONS.value,
            action=promo_constants.ACTION_EVENT_EDIT,
            message=message,
            promo_id=None,
            status=False,
            identifier=None,
            header_text=f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} Edit",
            promo_ids=None
        )
        raise e

    try:
        await set_event_under_processing(event_id)
        await update_event(event_id, request_payload, user_id)
        await call_opt_event_edit_resimulate_api(
            event_id,
            request_payload._effected_offers,
            user_id,
            action_log_id
        )

        archived_promos = [i["promo_id"] for i in request_payload._effected_offers]
        if archived_promos:
            previously_synced_promo_ids = await promo_service.get_previously_synced_promo_id_service(
                promo_id_list=archived_promos
            )
            if previously_synced_promo_ids:
                await promo_utils.downstream_cloud_function_invoke(
                    promo_ids=previously_synced_promo_ids,
                    action=promo_constants.ACTION_ARCHIVE,
                )
    except ConflictException as e:
        await handle_exception(e,e.message)
    except Exception as e:
        await handle_exception(e,"Something went wrong while updating event and it's effected offers")

async def set_event_under_processing(event_id: int):
    event_is_under_processing = await get_event_is_under_processing(event_id)
    if event_is_under_processing["is_under_processing"]:
        raise event_exceptions.EventAlreadyUnderProcessingException

    await events_data.set_event_under_processing(event_id)

async def call_opt_event_edit_resimulate_api(event_id: int, effected_offers: list[dict],user_id: int,action_log_id: int):
    async with httpx.AsyncClient() as client:
        payload = {
            "event_id": event_id,
            "effected_offers": json.loads(json.dumps(effected_offers,default=str)),
            "user_id": user_id,
            "action_log_id": action_log_id,
            "action": promo_constants.ACTION_EVENT_EDIT,
            "source": promo_constants.SCREEN_MARKETING_CALENDAR,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }
        logger.info(f"url: {environment.OPT_EVENT_EDIT_API_URL}")
        logger.info(f"payload: {payload}")
        response = await client.post(
            environment.OPT_EVENT_EDIT_API_URL,
            json=payload
        )
        logger.info(f"response: {response.text}")
        if response.status_code != 200:
            raise event_exceptions.EventEditOptCallFailedException


async def get_event_is_under_processing(event_id: int):
    return await events_data.get_event_is_under_processing(event_id)

async def get_finalized_promos_under_events(event_ids: list[int]):
    return await events_data.get_finalized_promos_under_events(event_ids)

async def delete_events(
        event_ids: list[int], 
        user_id: int, 
        background_tasks: BackgroundTasks
    ):
    promo_ids =  await events_data.delete_events_and_fetch_effected_promos(event_ids)
    if promo_ids:
        delete_promo_request = promo_models.DeletePromosRequest(promo_ids=promo_ids)
        await promo_service.delete_promos_v3(
            delete_promo_request, 
            user_id, 
            background_tasks=background_tasks
        )

async def is_event_locked(event_id: int):
    return await events_data.is_event_locked(event_id)

async def lock_unlock_events(request_payload: event_models.LockUnlockEvents,user_id: int):
    if request_payload.is_locked is False:
        live_events =  await has_live_events(request_payload.event_ids)
        if live_events:
            raise event_exceptions.EventLockedException(
                f"Ongoing/Completed {client_configuration_constants.EVENT_IDENTIFIER_PLURAL} cannot be unlocked"
            )
    return await events_data.lock_unlock_events(request_payload,user_id)

async def has_live_events(event_ids: list[int]):
    return await events_data.has_live_events(event_ids)

async def has_locked_events(event_ids: list[int]):
    return await events_data.has_locked_events(event_ids)

async def get_event_details(event_id: int,select: str):
    return await events_data.get_event_details(event_id,select)

async def get_event_ids_by_promo_ids(promo_ids: list[int]):
    return await events_data.get_event_ids_by_promo_ids(promo_ids)


async def validate_event_lock(
    event_ids: list[int],
    module: ConfigModuleEnum,
    key: ConfigKeyEnum,
    message: str
):
    action_allowed_after_event_lock = await get_config_value(
        module,
        key
    )
    if not action_allowed_after_event_lock and (await has_locked_events(event_ids)):
        raise event_exceptions.EventLockedException(message)
    

async def get_event_attributes_value_list():
    return await events_data.get_event_attributes_value_list()

@log_action_decorator(
    get_promo_ids_func=lambda i: i._effected_promo_ids,
    action_name=promo_constants.ACTION_EVENT_RESIMULATE,
    screen_name=promo_constants.SCREEN_MARKETING_CALENDAR,
    update_task_completion=False,
)
async def resimulate_events(
    request_payload: event_models.ResimulateEvents,
    user_id: int,
    action_log_id: int
):
    try:
        await set_events_under_processing(request_payload.event_ids)
        await call_opt_events_resimulate_api(
            request_payload.event_ids,
            user_id,
            action_log_id
        )
    finally:
        await events_data.unset_events_under_processing(request_payload.event_ids)

async def set_events_under_processing(event_ids: list[int]):
    under_processing_events = await get_under_processing_events(event_ids)
    if under_processing_events:
        raise event_exceptions.EventsUnderProcessingException

    await events_data.set_events_under_processing(event_ids)

async def get_under_processing_events(event_ids: list[int]):
    return await events_data.get_under_processing_events(event_ids)

async def call_opt_events_resimulate_api(
    event_ids: list[int],
    user_id: int,
    action_log_id: int
):
    async with httpx.AsyncClient() as client:
        payload = {
            "event_ids": event_ids,
            "user_id": user_id,
            "action_log_id": action_log_id,
            "action": promo_constants.ACTION_EVENT_RESIMULATE,
            "source": promo_constants.SCREEN_MARKETING_CALENDAR,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }
        logger.info(f"url: {environment.OPT_EVENT_RESIMULATE_API_URL}")
        logger.info(f"payload: {payload}")
        response = await client.post(
            environment.OPT_EVENT_RESIMULATE_API_URL,
            json=payload
        )
        logger.info(f"response: {response.text}")
        if response.status_code != 200:
            raise event_exceptions.EventResimulateOptCallFailedException

async def get_effected_promo_ids_for_events(event_ids: list[int]):
    return await events_data.get_effected_promo_ids_for_events(event_ids)

async def copy_events(request_payload: event_models.CopyEventsRequest, user_id: int):
    data =  await events_data.copy_events(request_payload, user_id)

    await create_notification_for_copy_event(
        event_ids=[id for id in request_payload.events],
        new_event_ids = [item['value'] for item in data],
        user_id = user_id
    )
    return data


async def create_notification_for_copy_event(
    event_ids: list[int],
    new_event_ids: list[int],
    user_id
):

    if new_event_ids:
        status = True
        header_text = f"Copy {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} Completed"

        if len(new_event_ids) > 1:
            identifier = f"{len(new_event_ids)} {client_configuration_constants.EVENT_IDENTIFIER_PLURAL}."
            message = f"Copy results are ready for {identifier}"
        else:
            event_name = await get_event_details(new_event_ids[0], " name ")
            event_name = event_name["name"]
            new_event_name = await get_event_details(new_event_ids[0], " name ")
            new_event_name = new_event_name["name"]
            message = f"Copy successful for {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} {event_name}. New {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} created with name: {new_event_name}."
            identifier = f"{new_event_name}."

    else:
        status = False
        header_text = f"Copy {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} Failed"
        if len(event_ids) > 1:
            identifier = f"{len(new_event_ids)} {client_configuration_constants.EVENT_IDENTIFIER_PLURAL}."
            message = f"Copy failed for {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} {identifier}."
        else:
            event_name = await get_event_details(event_ids[0], " name ")
            message = f"Copy failed for {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} {event_name}."
            identifier = f"{event_name}."

    await insert_notification(
        request = {},
        user_id=user_id,
        module=NotificationModules.EVENT.value,
        action=promo_constants.ACTION_COPY,
        message=message,
        promo_id=None,
        status=status,
        identifier=identifier,
        header_text=header_text,
        promo_ids=new_event_ids if new_event_ids else event_ids
    )


async def get_product_details_of_event(event_id:int):
    data = await events_data.get_product_details_of_event(event_id)
    return data

async def get_store_details_of_event(event_id:int):
    data = await events_data.get_store_details_of_event(event_id)
    return data
