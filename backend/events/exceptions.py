from exceptions.custom_base_exception import CustomBaseException
from fastapi import status
from client_configuration import constants as client_configuration_constants


class EventAlreadyUnderProcessingException(CustomBaseException):
    def __init__(self, message: str = f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} is already under processing") -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)
    
class EventsUnderProcessingException(CustomBaseException):
    def __init__(self, message: str = f"Some process is already running for the selected {client_configuration_constants.EVENT_IDENTIFIER_PLURAL}") -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)

class EventLockedException(CustomBaseException):
    def __init__(self, message: str = f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} is locked") -> None:
        super().__init__(message, status.HTTP_400_BAD_REQUEST)
    
class EventEditOptCallFailedException(CustomBaseException):
    def __init__(self, message: str = f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} edit failed") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)

class EventResimulateOptCallFailedException(CustomBaseException):
    def __init__(self, message: str = f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} resimulation failed") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)