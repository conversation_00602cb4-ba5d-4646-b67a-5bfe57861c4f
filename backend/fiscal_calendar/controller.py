from fastapi import APIRouter
from fiscal_calendar import models as fiscal_models
from fiscal_calendar import constants as fiscal_constants
from fiscal_calendar import service as fiscal_service
from pricesmart_common import utils as common_utils

router = APIRouter(tags=["Fiscal Calendar"])


@router.post(
    path=fiscal_constants.FISCAL_CALENDAR_URL,
    tags=[fiscal_constants.MODULE_NAME],
)
async def promotions(request_payload: fiscal_models.FiscalCalendarFetch):
    data = await fiscal_service.get_fiscal_calendar(request_payload)
    return common_utils.create_response(
        data=data
    )
