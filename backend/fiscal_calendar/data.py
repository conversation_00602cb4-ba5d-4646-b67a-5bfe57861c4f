from fiscal_calendar import models as fiscal_models
from fiscal_calendar import queries as fiscal_queries
from configuration.environment import environment
from pricesmart_common.utils import async_execute_query, get_str_repr


async def get_fiscal_calendar(request_payload_dict: fiscal_models.FiscalCalendarFetch):
    where_str = ''
    if request_payload_dict.years:
        where_str = f"where year in {get_str_repr(request_payload_dict.years)}"

    query = fiscal_queries.GET_FISCAL_CALENDAR_QUERY.format(
        global_schema=environment.global_schema,
        where_str=where_str
    )
    return await async_execute_query(query)