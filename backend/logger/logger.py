import logging
from logging.handlers import RotatingFileHandler
from slack_logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lackFormatter
import os
import os.path
from logstash_async.handler import AsynchronousLogstashHandler
import socket
from enums.Enums import LogLevel
from logger.logger_settings import logger_settings, log_stash_settings
from configuration.environment import environment
from pricesmart_common.staticclass import StaticClass
from pricesmart_common.constants import LOCAL_ENV

class EnvPathFilter(logging.Filter):
    def filter(self, record):
        record.log_env = f"biglots-{environment.deployment_env}-pricesmart"
        return True
class LoggerAdapter(logging.LoggerAdapter):
    def __init__(self, logger, prefix):
        super(LoggerAdapter, self).__init__(logger, {})
        self.prefix = prefix
    def process(self, msg, kwargs):
        return f'{msg} | extra msg: {self.prefix}', kwargs
    
class AppFilter(logging.Filter):
    def filter(self, record):
        record.log_env = environment.deployment_env
        return True


"""
The LoggerConfiguration class will set the  logger for
Save logs into the file
and also allow to send logs to be sent to Slack channel
if required logs can be sent to the APM if configured
"""
class LoggerConfiguration:
    def __init__(self):
        self.user_name = os.environ.get('USER')
        self.system_name = socket.gethostname()
        self.is_flag_level_set = False

    def get_debug_handler(self):
        path_to_debug_log = StaticClass.get_file_with_path(
                LogLevel.DEBUG.name.lower(), "log")
        debug_handler = RotatingFileHandler(
            path_to_debug_log, maxBytes=logger_settings.max_bytes, backupCount=logger_settings.backup_count
        )
        debug_handler.setFormatter(logger_settings.logger_formatter)
        debug_handler.setLevel(logging.DEBUG)
        logger.addHandler(debug_handler)

    def logger_config(self):
        """
        The logger_config function is used to configure the logger.
        It takes in a single parameter, which is an instance of the class that inherits from LoggerConfigBase.
        The function returns a logger object.
        :param self: Reference the class instance
        :return: The logger object
        :author: Arun kumar
        """
        try:
            logger = self.get_logger()
            if logger_settings.append_user_and_system_info:
                logger_settings.logger_name = self.get_logger_name_with_user_information()
            logger.addFilter(AppFilter())
            self.set_logger_for_all_levels(logger)
            if logger_settings.is_slack_logger:
                self.set_slack_logger_handler(logger)
            if log_stash_settings.is_logstash:
                #self.set_stash_logger_handler(logger)
                api_logger = logging.getLogger("uvicorn.access")
                print(api_logger)
                loggers = [logger, api_logger]
                for logger_ in loggers:
                    logger_.setLevel(logging.INFO)
                    logger_.addFilter(EnvPathFilter())
                    if logger_settings.log_level == LogLevel.DEBUG:
                        logger.addHandler(self.get_debug_handler())
                    else:
                        self.set_stash_logger_handler(logger_)
                logger_extra_msg = '-'
                logger = LoggerAdapter(logger, logger_extra_msg)
            return logger
        except Exception as ex:
            print(ex)

    def set_logger_for_all_levels(self, logger:logging.Logger):
        """
        The set_logger_for_all_levels function sets up a logger for all the log levels.
        It takes in a logger as an argument and then creates handlers for each of the log levels.
        Each handler is configured with its own file path, max bytes, backup count and formatter.
        
        :param self: Allow a method to modify its own attributes
        :param logger: Set the logger for all levels
        :return: The logger object
        :author: Arun Kumar[1533]
        """
        
        for level in LogLevel:
            if level != LogLevel.NOTSET:
                try:
                    log_file = StaticClass.get_file_with_path(
                        level.name.lower(), "log")
                    log_handler = RotatingFileHandler(
                        log_file, maxBytes=logger_settings.max_bytes, backupCount=logger_settings.backup_count
                    )
                    log_handler.setFormatter(logging.Formatter(
                        logger_settings.logger_formatter))
                    log_handler.setLevel(int(level.value))
                    logger.addHandler(log_handler)
                except Exception as ex:
                    print (ex)

    def set_stash_logger_handler(self, logger:logging.Logger):
        """
        The set_stash_logger_handler function sets the log level for the logger and adds a handler to send logs to
        the Logstash server. The function takes in a logger as an argument, which is then set to the log level specified
        in settings.py (default: INFO). The function also creates an AsynchronousLogstashHandler object with host and port
        specified in settings.py (default: localhost:5000), which is then added as a handler for this logger.
        :param self: Access the class attributes of the calling class
        :param logger: Set the logger to be used
        :return: A logger handler
        :author: Arun kumar
        """
        if not self.is_flag_level_set:
            self.is_flag_level_set = True
            logger.setLevel(log_stash_settings.log_level.value)
            logger.addHandler(
                AsynchronousLogstashHandler(
                    log_stash_settings.host,
                    log_stash_settings.port,
                    ssl_enable=False,
                    ssl_verify=False,
                    database_path=None,
                )
            )

    def set_slack_logger_handler(self, logger:logging.Logger):
        """
        The set_slack_logger_handler function sets up a SlackHandler object to send logs to the specified webhook.
        The SlackHandler is set with the level of logging specified in logger_settings.py, and uses a custom formatter
        defined in logger_settings.py
        :param self: Allow the function to refer to itself
        :param logger: Set the logger for which we want to send slack messages
        :return: A slackhandler instance
        :author: Arun kumar
        """
        slack_user= str(self.user_name) + " :: " + str(self.system_name) +" - " + environment.client_name
        slack_handler = SlackHandler(
            username=slack_user,
            icon_emoji=logger_settings.slack_icon,
            url=logger_settings.webhook_url,
        )
        slack_handler.setLevel(logger_settings.slack_level_logging.value)
        slackformatter = SlackFormatter()
        slack_handler.setFormatter(slackformatter)
        logger.addHandler(slack_handler)

    def get_logger_name_with_user_information(self):
        """
        The get_logger_name_with_user_information function is a helper function that returns the logger name with user information.
        The get_logger_name_with_user_information function accepts no arguments and returns the logger name with user information.
        :param self: Allow the method to reference attributes or methods of the class
        :return: The name of the logger with user information
        :author: Arun kumar
        """
        try:
            logger_name = logger_settings.logger_name + ": User :-" + \
                str(self.user_name) + ":: System Name :-" + str(self.system_name)
        except Exception:
            logger_name = logger_settings.logger_name + "-" + logger_settings.log_env
        return logger_name


    def get_logger(self):
        """
        The get_logger function is a helper function that returns a logger object.
        It takes the name of the module as an argument, and creates a logger with that name.
        The log_env variable in settings.py determines which environment to use for logging.
        :param self: Reference the class instance
        :return: A logger object that has been configured to log to a file
        :author: Arun kumar
        """
        logger = logging.getLogger(
            logger_settings.logger_name + "-" + logger_settings.log_env
        )
        return logger

    def debug_info_logger(self):
        """
        The debug_info_logger function creates a logger that writes to the function_logging_file.
        The debug_info_logger is used for logging debug information, such as when a function is called and what arguments were passed to it.
        :param self: Access variables that belongs to the class
        :return: A logger object
        :author: Arun kumar
        """
        debug_info_logger = logging.getLogger(
            logger_settings.logger_name + "-" + logger_settings.log_env)
        try:
            path_to_debug_log = StaticClass.get_file_with_path(
                logger_settings.function_logging_file.lower(), "log")
            function_debug_info_handler = RotatingFileHandler(
                path_to_debug_log, maxBytes=logger_settings.max_bytes, backupCount=logger_settings.backup_count
            )
            function_debug_info_handler.setFormatter(logging.Formatter(
                logger_settings.logger_formatter))
            function_debug_info_handler.setLevel(logger_settings.log_level.value)
            debug_info_logger.addHandler(function_debug_info_handler)
        except Exception:
            print('Unable to create function logger')
        return debug_info_logger




decorator_logger = LoggerConfiguration().debug_info_logger()
if environment.environment == LOCAL_ENV:
    logging.basicConfig()
    logger =  logging.getLogger("my_app")
    logger.setLevel(logging.INFO)
else:
    from monitoring.logger import logger as mtp_logger
    logger = mtp_logger
