from pydantic_settings import BaseSettings
from enums.Enums import LogLevel
import logger.logger_constants as Constants
from configuration.environment import environment


class LoggerSettings(BaseSettings):
    slack_level_logging: LogLevel = LogLevel.ERROR
    is_slack_logger: bool = bool(environment.is_slack_logger)
    log_level: LogLevel = LogLevel[environment.log_level].value
    max_bytes: int = Constants.MAX_BYTES
    backup_count: int = Constants.BACKUP_COUNT
    logger_formatter: str = Constants.LOGGER_FORMATTER
    logger_name: str = environment.logger_name
    slack_icon: str = Constants.SLACK_ICON
    webhook_url: str = Constants.SLACK_WEBHOOK_URL
    log_env: str = environment.deployment_env
    function_logging_file: str = Constants.FUNCTION_LOGGING_FILE
    append_user_and_system_info: bool = (
        environment.append_user_and_system_info_to_logger
    )


class LogStashSettings(BaseSettings):
    host: str = environment.log_stash_host
    port: int = environment.log_stash_port
    is_logstash: bool = eval(environment.is_logstash)
    log_level: LogLevel = LogLevel.DEBUG


logger_settings = LoggerSettings()
log_stash_settings = LogStashSettings()


apm_config = {
    "SERVICE_NAME": environment.elastic_apm_key,
    "SERVER_URL": environment.elastic_apm_url,
    "ENVIRONMENT": environment.deployment_env,
    "GLOBAL_LABELS": environment.GLOBAL_LABELS,
}

prometheus_variable = {
    "app_name": environment.client_name,
    "prefix": "biglots",
    "labels": {"service": "api", "env": environment.deployment_env},
    "group_paths": True,
    "filter_unhandled_paths": True,
    "buckets": [0.1, 0.25, 0.5],
    "skip_paths": ["/health"],
    "always_use_int_status": False,
}
