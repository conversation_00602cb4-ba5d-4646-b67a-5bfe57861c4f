ARCHIVE_FINALIZED_OFFERS_QUERY = """
    BEGIN;

        UPDATE price_promo.promo_master
        SET
            status = 6, 
            updated_by = -1, 
            updated_at = NOW()
        WHERE
            promo_id IN {promo_ids};

        delete from price_promo.tb_promo_stacked_offers_mapping
        where promo_id in {promo_ids} or stacked_promo_id in {promo_ids};

    END;
"""


ARCHIVING_PAST_AND_ONGOING_PLACEHOLDER_OFFERS_QUERY = """
    BEGIN;

        -- Update promo_master table
        UPDATE price_promo.promo_master
        SET
            status = 6,
            updated_by = -1,
            updated_at = NOW()
        WHERE
            promo_id IN {promo_ids};

        -- Update tb_placeholder_targets table
        UPDATE price_promo.tb_placeholder_targets
        SET
            status = 6
        WHERE
            promo_id IN {promo_ids};

    END;
"""
