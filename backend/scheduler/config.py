from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.schedulers.base import BaseScheduler
from fastapi_amis_admin.admin.settings import Settings
from fastapi_amis_admin.admin.site import AdminSite
from fastapi_scheduler import SchedulerAdmin
from pricesmart_common import constants as common_constants

# Configure the AdminSite
site = AdminSite(
    settings=Settings(database_url_async="sqlite+aiosqlite:///amisadmin.db")
)

# Set up the AsyncIOScheduler
scheduler_strategy: BaseScheduler = AsyncIOScheduler(
    jobstores={"default": MemoryJobStore()},
    timezone=common_constants.CLIENT_TIMEZONE_FOR_PYTHON,
)

# Bind the scheduler with SchedulerAdmin
scheduler = SchedulerAdmin.bind(site, scheduler=scheduler_strategy)
