from scheduler.config import scheduler
from scheduler.jobs import (
    archive_finalized_offer,
    archive_past_and_ongoing_placeholder_promotions,
    notify_users_before_x_days_placeholder_promo_expiration,
)


def schedule_jobs():
    # Schedule all jobs
    scheduler.add_job(archive_past_and_ongoing_placeholder_promotions)
    scheduler.add_job(archive_finalized_offer)
    scheduler.add_job(notify_users_before_x_days_placeholder_promo_expiration)
    # Add more jobs here as needed
