import datetime
import traceback

import pytz
from configuration.environment import environment
from logger.logger import logger
from pricesmart_common import constants as common_constants
from scheduler import utils as sched_utils
from scheduler.config import scheduler
from pricesmart_common.utils import async_execute_query

# Get current date and time in Asia/Kolkata timezone
today = lambda : datetime.datetime.now(pytz.timezone("Asia/Kolkata"))

@scheduler.scheduled_job("cron",hour=0,minute=0,second=0)
async def lock_events():
    process_name = "LOCKING EVENTS"
    log_title = environment.LOG_TITLE
    logger.info(f"{log_title} : {process_name} ---- START")
    try:
        query = f"""
            with config_values_cte as (
                select 
                    max(case when config_name = 'first_lock_buffer_days' then config_value end)::text as first_lock_buffer_days,
                    max(case when config_name = 'final_lock_buffer_days' then config_value end)::text as final_lock_buffer_days
                from
                    price_promo.tb_tool_configurations
                where
                    module = 'event'
            )
            update price_promo.event_master
            set is_locked = true,
                updated_at = now()
            from config_values_cte cv
            where (
                submit_by + (cv.first_lock_buffer_days || ' days')::interval = date(timezone('{common_constants.CLIENT_TIMEZONE}', now()))
                or 
                start_date + (cv.final_lock_buffer_days || ' days')::interval = date(timezone('{common_constants.CLIENT_TIMEZONE}', now()))
            )
            and is_deleted = 0
            and is_locked = false
        """
        await async_execute_query(query)

        await sched_utils.send_success_email(process_name,None)
    except Exception:
        trace = traceback.format_exc()  # Get the traceback for error logging
        logger.error(f"{log_title} : Error in {process_name}",exc_info=True)
        # Send failure email with error details
        await sched_utils.send_failure_email(today(),trace,process_name,None)
    finally:
        logger.info(f"{log_title} : {process_name} ---- END")
    

@scheduler.scheduled_job("cron", hour=0, minute=0, second=0)
async def archive_past_and_ongoing_placeholder_promotions():
    process_name = "ARCHIVING PAST AND ONGOING PLACEHOLDER OFFERS"
    log_title = environment.LOG_TITLE
    logger.info(f"{log_title} : {process_name} ---- START")
    print(f"{log_title} : {process_name} ---- START")

    attachment = None  # Initialize attachment variable

    try:
        # Define SQL query parts to fetch eligible promotions
        select_str = "promo_id, name AS promo_name"
        where_str = """
            status = -1
            AND (start_date <= current_date OR end_date <= current_date)
        """

        # Fetch promo_ids of eligible promotions
        eligible_promos = await sched_utils.fetch_valid_promo_ids(select_str, where_str)

        # Generate Excel attachment for the eligible promotions
        attachment = sched_utils.generate_excel_attachment(eligible_promos)

        # Extract promo Id
        promos = await sched_utils.extract_promo_ids(eligible_promos)

        # Process the archiving of finalized offers
        if promos:
            await sched_utils.archiving_finalized_offers(promos)

        # Send success email with the generated attachment
        await sched_utils.send_success_email(process_name, attachment)
        logger.info(f"{log_title} : {process_name} ---- SUCCESSFUL")
        print(f"{log_title} : {process_name} ---- SUCCESSFUL")

    except Exception as e:
        trace = traceback.format_exc()  # Get the traceback for error logging
        logger.error(f"{log_title} : Error in {process_name} ---- {e}")
        print(f"{log_title} : Error in {process_name} ---- {e}")
        # Send failure email with error details
        await sched_utils.send_failure_email(today(), trace, process_name, attachment)
    finally:
        logger.info(f"{log_title} : {process_name} ---- END")
        print(f"{log_title} : {process_name} ---- END")


@scheduler.scheduled_job("cron", hour=0, minute=0, second=0)
async def archive_finalized_offer():
    process_name = "ARCHIVING FINALIZED OFFERS"
    log_title = environment.LOG_TITLE
    logger.info(f"{log_title} : {process_name} ---- START")
    print(f"{log_title} : {process_name} ---- START")

    attachment = None  # Initialize attachment variable

    try:
        # Define SQL query parts to fetch finalized offers
        select_str = "promo_id, name AS promo_name"
        where_str = f"""
            status = 4
            AND start_date = date(timezone('{common_constants.CLIENT_TIMEZONE}', now()))
        """

        # Fetch promo_ids of finalized offers
        eligible_promos = await sched_utils.fetch_valid_promo_ids(select_str, where_str)

        # Generate Excel attachment for the finalized offers
        attachment = sched_utils.generate_excel_attachment(eligible_promos)

        # Extract promo Id
        promos = await sched_utils.extract_promo_ids(eligible_promos)

        # Process the archiving of finalized offers
        if promos:
            await sched_utils.archiving_finalized_offers(promos)

        # Send success email with the generated attachment
        await sched_utils.send_success_email(process_name, attachment)
        logger.info(f"{log_title} : {process_name} ---- SUCCESSFUL")
        print(f"{log_title} : {process_name} ---- SUCCESSFUL")

    except Exception as e:
        trace = traceback.format_exc()  # Get the traceback for error logging
        logger.error(f"{log_title} : Error in {process_name} ---- {e}")
        print(f"{log_title} : Error in {process_name} ---- {e}")
        # Send failure email with error details
        await sched_utils.send_failure_email(today(), trace, process_name, attachment)
    finally:
        logger.info(f"{log_title} : {process_name} ---- END")
        print(f"{log_title} : {process_name} ---- END")


@scheduler.scheduled_job("cron", hour=0, minute=0, second=0)
async def notify_users_before_x_days_placeholder_promo_expiration():
    process_name = "PLACEHOLDER OFFERS EXPIRY REMINDER"
    log_title = environment.LOG_TITLE
    logger.info(f"{log_title} : {process_name} ---- START")
    print(f"{log_title} : {process_name} ---- START")

    attachment = None  # Initialize attachment variable

    try:
        # Define SQL query parts to fetch placeholder promotions nearing expiry
        select_str = "promo_id, name AS promo_name, created_by, updated_by"
        where_str = """
            status = -1
            AND start_date = CURRENT_DATE + INTERVAL '21 days'
        """

        # Fetch promo_ids of eligible promotions for expiry notifications
        eligible_promos = await sched_utils.fetch_valid_promo_ids(select_str, where_str)

        # Process the promotions along with user information
        promos = await sched_utils.process_promos_with_users(eligible_promos)

        # Generate Excel attachment for the promotions
        attachment = sched_utils.generate_excel_attachment(promos)

        # Send notifications to users about the expiry
        if promos:
            await sched_utils.send_placeholder_offers_expiry_notification(
                promos, status=True
            )

        # Send success email with the generated attachment
        await sched_utils.send_success_email(process_name, attachment)
        logger.info(f"{log_title} : {process_name} ---- SUCCESSFUL")
        print(f"{log_title} : {process_name} ---- SUCCESSFUL")

    except Exception as e:
        # Attempt to notify users even if there is an error
        await sched_utils.send_placeholder_offers_expiry_notification(
            promos, status=False
        )

        trace = traceback.format_exc()  # Get the traceback for error logging
        logger.error(f"{log_title} : Error in {process_name} - {str(e)}")
        print(f"{log_title} : Error in {process_name} ---- {e}")
        # Send failure email with error details
        await sched_utils.send_failure_email(today(), trace, process_name, attachment)
    finally:
        logger.info(f"{log_title} : {process_name} ---- END")
        print(f"{log_title} : {process_name} ---- END")
