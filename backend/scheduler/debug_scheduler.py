import asyncio

from scheduler.config import scheduler
from scheduler.schedule_jobs import schedule_jobs

# pylint: disable=W0105(pointless-string-statement)
"""
    Run from backend directory using below command
        - python -m scheduler.debug_scheduler
"""


def list_scheduled_jobs():
    jobs = scheduler.get_jobs()
    for job in jobs:
        print(f"Job ID: {job.id}")
        print("Available attributes:", dir(job))

        if hasattr(job, "next_run_time") and job.next_run_time is not None:
            print(f"Next Run Time: {job.next_run_time}")
        else:
            print("No next run time available")

        print(f"Job Trigger: {job.trigger}")
        print(f"Job Function: {job.func_ref}")
        print("-" * 40)


async def main():
    # Schedule jobs
    schedule_jobs()

    # List scheduled jobs for debugging
    list_scheduled_jobs()

    # Start the scheduler
    scheduler.start()
    print("Scheduler started")

    try:
        # Keep the script running to observe scheduled jobs
        while True:
            await asyncio.sleep(60)  # Sleep to keep the script running
    except asyncio.CancelledError:
        print("Task was cancelled")
    except KeyboardInterrupt:
        # Handle script interruption (Ctrl+C)
        print("Stopping scheduler...")
    finally:
        scheduler.shutdown()
        print("Scheduler shut down")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("KeyboardInterrupt caught outside asyncio.run()")
